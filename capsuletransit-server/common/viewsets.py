from common.pagination import DefaultPageNumberPagination
from rest_framework.viewsets import GenericViewSet


class PaginatableViewSet(GenericViewSet):
    """
    A viewset that disables pagination by default.
    The `page` query parameter is required to enable pagination.
    """

    pagination_class = DefaultPageNumberPagination

    def paginate_queryset(self, queryset):
        """
        Return a single page of results, if `page` query parameter is present. Otherwise, return None.
        """
        if self.paginator is None or self.request.query_params.get("page") is None:
            return None
        return self.paginator.paginate_queryset(queryset, self.request, view=self)
