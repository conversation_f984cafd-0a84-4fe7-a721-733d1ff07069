# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Platform",
            fields=[
                (
                    "platform_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Booking Platform ID",
                    ),
                ),
                (
                    "platform",
                    models.CharField(max_length=50, verbose_name="Booking Platform"),
                ),
                (
                    "is_archive",
                    models.BooleanField(
                        default=False, verbose_name="Is Booking Platform archived?"
                    ),
                ),
                ("color_tags", models.Char<PERSON>ield(default="#ffffff", max_length=10)),
            ],
            options={
                "verbose_name": "Booking Platform",
                "verbose_name_plural": "Booking Platforms",
                "db_table": "bookings_platform",
            },
        ),
    ]
