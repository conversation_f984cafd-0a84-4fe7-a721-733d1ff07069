from uuid import uuid4
from django.db import models

# Create your models here.
class Platform(models.Model):
    platform_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Booking Platform ID",
    )
    platform = models.Char<PERSON>ield(max_length=50, verbose_name="Booking Platform")
    is_archive = models.BooleanField(
        default=False, verbose_name="Is Booking Platform archived?"
    )
    color_tags = models.CharField(max_length=10, default="#ffffff")
    can_late_payment = models.BooleanField(default=True, verbose_name="Can do Late Payment for this platform?")
    ota_code = models.Char<PERSON>ield(max_length=50, null=True, default=None)

    def __str__(self):
        return self.platform

    class Meta:
        # Ensure that the table name is correct
        db_table = "bookings_platform"
        verbose_name = "Booking Platform"
        verbose_name_plural = "Booking Platforms"

