from django.db import models
from lot.models import Lot
from uuid import uuid4



SHOWER_STATUS = (
    ("Available", "Available"),
    ("Occupied", "Occupied"),
    ("Rental", "Rental"),
    ("Maintenance", "Maintenance"),
)

class Shower(models.Model):
    shower_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    details = models.CharField(max_length=50) 
    access_card = models.BinaryField(unique=True, null=True)
    status = models.CharField(choices=SHOWER_STATUS, default="Available", max_length=15)
    is_archived = models.BooleanField(default=False, null=True, blank=True)

    def __str__(self):
        return self.access_card

class ShowerRate(models.Model):
    shower_rate_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    tiers =  models.IntegerField(null=False, default=0) 
    hours_of_usage = models.IntegerField(null=False)
    shower_rate = models.DecimalField(max_digits=100, decimal_places=2)
    lot = models.ForeignKey(Lot, on_delete=models.RESTRICT, null=True)
    is_latest = models.BooleanField(default=True)