from django.http import JsonResponse
from django.shortcuts import render
from rest_framework import viewsets, status, generics
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from rest_framework.decorators import action

from djangorestframework_camel_case.parser import Camel<PERSON><PERSON><PERSON><PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON>aseJ<PERSON>NRenderer

from shower.models import Shower, ShowerRate
from shower.serializer import ShowerRateSerializer, ShowerSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


class ShowerViewSet(viewsets.ViewSet):
    queryset = Shower.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = ShowerSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser, )
    @swagger_auto_schema(
            manual_parameters=[
                openapi.Parameter(
                    "status",
                    openapi.IN_QUERY,
                    description="Filter by availability",
                    type=openapi.TYPE_STRING,
                    enum=["Available", "Rental", "Maintanance", "Occupied" ]
                ),
            ],
            responses={
                '200' : ShowerSerializer(many=True),
            }
    )
        
    def list(self, request):
        qs = Shower.objects.all()
        
        if "status" in request.GET:
            qs = qs.filter(status__in=request.GET.getlist("status"))

        serializer = ShowerSerializer(qs, many=True)
        return Response(
            {
                "status": "success",
                "data" : serializer.data,
            },
            status=status.HTTP_200_OK,
        )
    
    def retrieve(self, request, pk=None):
        queryset = Shower.objects.all()
        room = get_object_or_404(queryset, pk=pk)
        serializer = ShowerSerializer(room)
        return Response(
            {
                "status": "success",
                "data" : serializer.data,
            },
            status=status.HTTP_200_OK,
        )
    


    @swagger_auto_schema(
        request_body=ShowerSerializer,
        responses={
            200: "success"
        }
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="archive/(?P<shower_id>[0-9a-f-]+)",
        url_name="archive",
        permission_classes=[],
    )
    def archive_shower(self, request, shower_id):
        try:
            shower = Shower.objects.get(pk = shower_id)
            shower.is_archived = True
            shower.save()
            return Response(
                {"status": "success", "msg": "shower archive success"},
                status=status.HTTP_200_OK,
            )
        
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                    "error" : str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    swagger_auto_schema(
        request_body=ShowerSerializer,
        responses={
            200: "success"
        }
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="unarchive/(?P<shower_id>[0-9a-f-]+)",
        url_name="unarchive",
        permission_classes=[],
    )
    def unarchive_shower(self, request, shower_id):
        try:
            shower = Shower.objects.get(pk = shower_id)
            shower.is_archived = False
            shower.save()
            return Response(
                {"status": "success", "msg": "unarchive shower success"},
                status=status.HTTP_200_OK,
            )
        
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                    "error" : str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        


class ShowerRateViewSet(viewsets.ViewSet):
    queryset = ShowerRate.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = ShowerRate
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser, )


    def list(self, request):
        qs = self.queryset.all()
        serializer = ShowerRateSerializer(qs, many=True)
        return Response(serializer.data)
    
    def retrieve(self, request, pk=None):
        queryset = ShowerRate.objects.all()
        shower = get_object_or_404(queryset, pk=pk)
        serializer = ShowerRateSerializer(shower)
        return Response(serializer.data)
    
    @swagger_auto_schema(
        request_body=ShowerRateSerializer,
        responses={
            200: "success",
            400: "bad request",
            500: "internal server error"
        }
    )
    def create(self, request):
        serializer = ShowerRateSerializer(data=request.data)
        if serializer.is_valid():
            model = serializer.save()
            model.save()
            return Response({
                "status": "success",
                "message": "new shower rate registered"
            }, status=status.HTTP_201_CREATED)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @swagger_auto_schema(
        request_body=ShowerRateSerializer,
        responses={
            200: "success",
            400: "bad request",
            500: "internal server error"
        }
    )
    def update(self, request, pk=None):
        queryset = ShowerRate.objects.all()
        customer = get_object_or_404(queryset, pk=pk)
        serializer = ShowerRateSerializer(customer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        queryset = ShowerRate.objects.all()
        shower = get_object_or_404(queryset, pk=pk)
        shower.delete()
        return Response({
            "status": "success",
            "message": "shower rate deleted"
        }, status=status.HTTP_200_OK)

