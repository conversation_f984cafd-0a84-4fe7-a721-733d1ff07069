# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("lot", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Shower",
            fields=[
                (
                    "shower_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("details", models.CharField(max_length=50)),
                ("access_card", models.BinaryField(null=True, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Available", "Available"),
                            ("Occupied", "Occupied"),
                            ("Rental", "Rental"),
                            ("Maintenance", "Maintenance"),
                        ],
                        default="Available",
                        max_length=15,
                    ),
                ),
                (
                    "is_archived",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ShowerRate",
            fields=[
                (
                    "shower_rate_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("tiers", models.IntegerField(default=0)),
                ("hours_of_usage", models.IntegerField()),
                ("shower_rate", models.DecimalField(decimal_places=2, max_digits=100)),
                ("is_latest", models.BooleanField(default=True)),
                (
                    "lot",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="lot.lot",
                    ),
                ),
            ],
        ),
    ]
