# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("airportCode", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Lot",
            fields=[
                (
                    "lot_id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="LotID"
                    ),
                ),
                (
                    "lot_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Lot Number"
                    ),
                ),
                (
                    "lot_description",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Lot Description",
                    ),
                ),
                (
                    "airport_code_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="lot",
                        to="airportCode.airportcode",
                        verbose_name="Airport Lot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Settings",
            fields=[
                (
                    "settings_id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="SettingsID"
                    ),
                ),
                (
                    "settings_name",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "settings_category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Tax", "Tax"),
                            ("Wave Payment", "Wave Payment"),
                            ("Charges", "Charges"),
                        ],
                        default="Pending",
                        max_length=50,
                    ),
                ),
                (
                    "settings_description",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("creation_datetime", models.DateTimeField(auto_now_add=True)),
                (
                    "lot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT, to="lot.lot"
                    ),
                ),
            ],
        ),
    ]
