from rest_framework import serializers

from lot.models import Lot, Settings

from rooms.serializers import RoomSerializer


class LotSerializer(serializers.ModelSerializer):

    class Meta:
        model = Lot
        fields = "__all__"


class LotSettingsSerializer(serializers.ModelSerializer):

    class Meta:
        model = Settings
        fields = "__all__"


class EditSettingsDescriptionSerializer(serializers.Serializer):
    settings_id = serializers.CharField()
    settings_name = serializers.CharField(required=False, default=None)
    new_description = serializers.CharField()


class CreateSettingsSerializer(serializers.Serializer):
    settings_category = serializers.Char<PERSON>ield()
    settings_name = serializers.CharField()
    settings_description = serializers.CharField()


class LotAvailableSerializer(RoomSerializer):
    prev_room_id = serializers.CharField()
    prev_room_code = serializers.CharField()
    prev_room_zone = serializers.CharField()
    prev_room_color_code = serializers.Char<PERSON>ield()
    prev_room_type_details = serializers.Char<PERSON>ield()
