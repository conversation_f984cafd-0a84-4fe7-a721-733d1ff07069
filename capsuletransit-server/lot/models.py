from airportCode.models import AirportCode
from django.db import models
from django.db.models import CharField


class Lot(models.Model):
    lot_id = models.AutoField(primary_key=True, verbose_name="LotID")
    airport_code_id = models.ForeignKey(
        AirportCode,
        verbose_name="Airport Lot",
        on_delete=models.RESTRICT,
        related_name="lot",
    )
    lot_number = models.CharField(max_length=50, unique=True, verbose_name="Lot Number")
    lot_description = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="Lot Description"
    )
    staah_property_id = CharField(max_length=50, unique=True, blank=True, null=True)
    full_name = CharField(max_length=100, blank=True, null=True)
    reg_no = CharField(max_length=100, blank=True, null=True)
    sst_no = CharField(max_length=100, blank=True, null=True)
    address = CharField(max_length=250, blank=True, null=True)
    tel = CharField(max_length=50, blank=True, null=True)
    gateawaymall_lot = CharField(max_length=50, blank=True, null=True)
    mahb_lot = CharField(max_length=50, blank=True, null=True)

    def __str__(self):
        return self.lot_number


SETTINGS_CATEGORY = (
    ("Tax", "Tax"),
    ("Wave Payment", "Wave Payment"),
    ("Charges", "Charges"),
)


class Settings(models.Model):
    settings_id = models.AutoField(primary_key=True, verbose_name="SettingsID")
    settings_name = models.CharField(max_length=50, null=True, blank=True)
    settings_category = models.CharField(
        choices=SETTINGS_CATEGORY,
        max_length=50,
        default="Pending",
        blank=True,
        null=False,
    )
    settings_description = models.CharField(max_length=100, null=True, blank=True)
    lot = models.ForeignKey(Lot, on_delete=models.RESTRICT)
    creation_datetime = models.DateTimeField(auto_now_add=True)
