from django.utils import timezone
from django.db.models import Sum

from accounts.models import Shift, StaffShift
from bookings.models import Booking, BookingStatus, RoomBooking
from rooms.models import RoomType, RoomZone, Room
from lot.models import Lot, Settings
from lot.serializers import LotSettingsSerializer, LotSerializer


def get_lot_availability(request, lot_id):

    booking_id = request.GET.get("bookingId", None)

    latest_booking_status = BookingStatus.objects.filter(
        is_latest=True, booking_id=booking_id
    ).first()

    if latest_booking_status:
        booking_check_in = latest_booking_status.check_in_datetime
        booking_check_out = latest_booking_status.check_out_datetime

        current_room_id = RoomBooking.objects.filter(booking=booking_id).values("room")

        current_room = Room.objects.filter(room_id__in=current_room_id)

        current_booking_max_pax = current_room.aggregate(
            total_max_pax=Sum("room_type__max_pax")
        )["total_max_pax"]

        new_lot_rooms = Room.objects.filter(room_type__roomzone__lot_id=lot_id)

        new_lot_room_bookings = RoomBooking.objects.filter(room__in=new_lot_rooms)

        new_lot_booking_statuses = BookingStatus.objects.filter(
            booking__in=new_lot_room_bookings.values("booking"), is_latest=True
        )

        filtered_booking_statuses = new_lot_booking_statuses.filter(
            check_in_datetime__lte=booking_check_out,
            max_check_out_datetime__gte=booking_check_in,
        ).values("booking")

        filtered_room_bookings = new_lot_room_bookings.filter(
            actual_checkin_date_time__lte=booking_check_out,
            actual_checkout_date_time__gte=booking_check_in,
        ).values("booking")

        combined_booking_ids = filtered_booking_statuses.union(filtered_room_bookings)

        clashing_room_bookings = RoomBooking.objects.filter(
            booking__in=combined_booking_ids
        )

        available_rooms = new_lot_rooms.exclude(
            room_id__in=clashing_room_bookings.values("room")
        )

        total_available_max_pax = available_rooms.aggregate(
            total_max_pax=Sum("room_type__max_pax")
        )["total_max_pax"]

        if (
            total_available_max_pax is None
            or total_available_max_pax < current_booking_max_pax
        ):
            raise Exception("No Room is Available to accomodate current pax")

        new_rooms = []

        for index, room in enumerate(current_room):
            room_tier = room.room_type.max_pax

            # This If section is for detecting if Room Type is the lowest tier
            if room_tier == 1:
                # For Now this Filter only allow if the other lot has the same room type
                equivalent_rooms = available_rooms.filter(
                    room_type__type_name=room.room_type.type_name
                )
                if len(equivalent_rooms) > 0:
                    equivalent_rooms[index].prev_room_id = room.room_id
                    equivalent_rooms[index].prev_room_code = room.room_code
                    equivalent_rooms[index].prev_room_zone = room.room_type.roomzone
                    equivalent_rooms[index].prev_room_color_code = (
                        room.room_type.color_tags
                    )
                    new_rooms.append(add_additional_data(equivalent_rooms[index], room))
                else:
                    raise Exception("No Room is Available for Transfer")
            else:
                # The Code below is for room tiers above than 1
                equivalent_rooms = available_rooms.filter(room_type__max_pax=room_tier)
                if len(equivalent_rooms) > 0:
                    equivalent_rooms[index].prev_room_id = room.room_id
                    equivalent_rooms[index].prev_room_code = room.room_code
                    equivalent_rooms[index].prev_room_zone = room.room_type.roomzone
                    equivalent_rooms[index].prev_room_color_code = (
                        room.room_type.color_tags
                    )
                    equivalent_rooms
                    new_rooms.append(add_additional_data(equivalent_rooms[index], room))
                else:
                    # This code currently takes all 1 maxpax room and push them for new room
                    lower_tier_rooms = available_rooms.filter(
                        room_type__max_pax__lt=room_tier
                    )
                    if len(lower_tier_rooms) >= room_tier:
                        count = room_tier
                        for index in range(count):
                            # The code below is to check the rooms that are available decremently
                            max_room_type = lower_tier_rooms.filter(
                                room_type__max_pax=room_tier - index - 1
                            ).first()
                            if max_room_type:
                                new_rooms.append(
                                    add_additional_data(max_room_type, room)
                                )
                                count = count - max_room_type.room_type.max_pax
                            else:
                                continue

                            if count < 0:
                                break
                    else:
                        raise Exception("No Room is Available for Transfer")

        return new_rooms

    return "Available"


def add_additional_data(target_room, previous_room):
    target_room.prev_room_id = previous_room.room_id
    target_room.prev_room_code = previous_room.room_code
    target_room.prev_room_zone = previous_room.room_type.roomzone
    target_room.prev_room_color_code = previous_room.room_type.color_tags
    target_room.prev_room_type_details = previous_room.room_type.RoomTypeDetails

    return target_room


def get_lot_settings_by_lot_id(lot_id):
    lot_settings = Settings.objects.filter(lot_id=lot_id)
    return lot_settings

def get_lot_settings_by_lot_with_serializer(lot_id):
    lot_settings = Settings.objects.filter(lot_id=lot_id)
    serializer = LotSettingsSerializer(lot_settings, many=True)
    return serializer.data