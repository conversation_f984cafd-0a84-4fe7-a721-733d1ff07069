from rest_framework.decorators import action

from lot.models import Lot, Settings
from lot.serializers import (
    CreateSettingsSerializer,
    EditSettingsDescriptionSerializer,
    LotAvailableSerializer,
    LotSerializer,
    LotSettingsSerializer,
)

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication

from drf_yasg.utils import swagger_auto_schema


from lot.utils import get_lot_availability

from rest_framework import viewsets, status
from rest_framework.response import Response
from djangorestframework_camel_case.parser import Camel<PERSON>aseFormParser
from djangorestframework_camel_case.render import Camel<PERSON>ase<PERSON><PERSON><PERSON><PERSON>er
from rest_framework.parsers import J<PERSON>NParser

from accounts.models import StaffShift, Shift


class LotViewSet(viewsets.ViewSet):
    parser_classes = (CamelCaseFormParser,)
    renderer_classes = (CamelCaseJSONRenderer,)
    permission_classes = []
    authentication_classes = []

    def get_queryset(self):
        qs = Lot.objects.all()
        return qs

    @swagger_auto_schema(responses={200: LotSerializer, 500: "server error"})
    def list(self, request):
        try:
            qs = self.get_queryset()

            serializer = LotSerializer(qs, many=True)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(responses={200: LotSerializer, 500: "server error"})
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-own-lot",
        url_name="get_own_lot",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def get_own_lot(self, request):
        try:
            qs = self.get_queryset()

            qs = qs.filter(lot_id=request.user.lot_id).first()

            print(qs)

            serializer = LotSerializer(qs)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-lot-availability/(?P<lot_id>[0-9a-f-]+)",
        url_name="get-lot-availability",
        permission_classes=[],
    )
    def get_lot_availability(self, request, lot_id):

        try:
            data = get_lot_availability(request=request, lot_id=lot_id)

            serializer = LotAvailableSerializer(data, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class LotSettingsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    queryset = Settings
    parser_classes = (CamelCaseFormParser,)
    renderer_classes = (CamelCaseJSONRenderer,)
    serializer_class = LotSettingsSerializer

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-settings/(?P<account_id>[0-9a-f-]+)",
        url_name="get-settings",
        permission_classes=[],
    )
    def get_settings_by_account(self, request, account_id):

        overstay_settings = request.GET.get("overstaySettings", None)
        add_1_hour_settings = request.GET.get("add1HourSettings", None)

        try:
            staff_shifts = StaffShift.objects.filter(
                staff_id=account_id, is_archived=False
            )
            shifts = Shift.objects.filter(staffshift__in=staff_shifts)

            data = Settings.objects.filter(lot_id=request.user.lot_id)

            if overstay_settings:
                data = data.filter(settings_category="Overstay Charges")

            if add_1_hour_settings:
                data = data.filter(settings_name="Add 1 hour")

            serializer = LotSettingsSerializer(data, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=CreateSettingsSerializer,
        responses={"200": "created", "400": "bad request", "500": "server error"},
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="create-settings",
        url_name="create-settings",
        permission_classes=[],
    )
    def create_settings(self, request):
        serializer = CreateSettingsSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            lot = request.user.lot_id

            validated_data = serializer.validated_data
            setting = Settings.objects.create(
                settings_name=validated_data["settings_name"],
                settings_category=validated_data["settings_category"],
                settings_description=validated_data["settings_description"],
                lot_id=lot,
            )

            return Response(
                {"status": "success", "message": "settings created"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    swagger_auto_schema(
        request_body=EditSettingsDescriptionSerializer,
        responses={"200": "edited", "400": "bad request", "500": "server error"},
    )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-settings-description",
        url_name="edit-settings-description",
        permission_classes=[],
    )
    def edit_settings_description(self, request):
        serializer = EditSettingsDescriptionSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data
            try:
                settings_name = validated_data["settings_name"]
            except:
                settings_name = None

            setting = Settings.objects.get(pk=validated_data["settings_id"])
            if settings_name:
                setting.settings_name = validated_data["settings_name"]
            setting.settings_description = validated_data["new_description"]
            setting.save()

            return Response(
                {"status": "success", "message": "settings edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
