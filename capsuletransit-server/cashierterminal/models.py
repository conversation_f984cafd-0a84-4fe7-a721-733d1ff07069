from uuid import uuid4

from django.db.models import (
    CASCADE,
    AutoField,
    BooleanField,
    CharField,
    DateTimeField,
    ForeignKey,
    Model,
    UUIDField,
)
from lot.models import Lot


class CashierTerminal(Model):
    cashier_terminal_id = AutoField("CashierTerminalID", primary_key=True)
    lot = ForeignKey("lot.Lot", CASCADE, verbose_name="LotID")
    cashier_terminal = CharField("Cashier Terminal", max_length=100)
    is_archive = BooleanField("Is Archive", default=False)
    kc_number = CharField(null=True, blank=True)

    def __str__(self):
        return self.cashier_terminal
