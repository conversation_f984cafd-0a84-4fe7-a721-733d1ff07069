from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.decorators import action

from .models import CashierTerminal
from .serializers import CashierEditSerializer, CashierTerminalArchiveSerializer, CashierTerminalSerializer, CashierWriteSerializer
from djangorestframework_camel_case.render import Came<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ender<PERSON>
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication


class CashierTerminalViewSet(ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    serializer_class = CashierTerminalSerializer

    def get_queryset(self):
        qs = CashierTerminal.objects.all()
        return qs

    def list(self, request):
        
        try:
            qs = self.get_queryset()
            qs = qs.filter(lot_id = request.user.lot_id)
            
            return_serializer = CashierTerminalSerializer(qs, many=True)

            return Response({
                "status" : "success",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        
    @action(
        methods=["GET"],
        detail=False,
        url_path="cashier/(?P<cashier_id>[0-9a-f-]+)",
        url_name="cashier"
    )
    def get_specific_cashier(self, request, cashier_id):

        try:
            qs = self.get_queryset()
            qs = qs.filter(pk = cashier_id).first()

            if not qs:
                return Response({
                    "status" : "failed",
                    "message" : "cashier not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            return_serializer = CashierTerminalSerializer(qs)
            
            return Response({
                "status" : "success",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    def create(self, request):
        serializer = CashierWriteSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            qs = self.get_queryset()
            cashier_available = qs.filter(cashier_terminal__iexact = validated_data['cashier_terminal_number']).exists()
            if cashier_available:
                return Response({
                    "status" : "failed",
                    "message" : "cashier number already used"
                }, status=status.HTTP_409_CONFLICT)

            new_cashier_terminal = CashierTerminal.objects.create(
                lot_id = request.user.lot_id,
                cashier_terminal = validated_data['cashier_terminal_number'],
                kc_number = validated_data['kc_number']
            )

            return_serializer = CashierTerminalSerializer(new_cashier_terminal)
            return Response({
                "status" : "success",
                "message" : "cashier terminal created",
                "data" : return_serializer.data
            }, status=status.HTTP_201_CREATED)
            

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit",
        url_name="edit"
    )
    def edit_cashier(self, request):
        serializer = CashierEditSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            try:
                cashier_terminal_obj = CashierTerminal.objects.get(pk = validated_data['cashier_terminal_id'])
            except:
                return Response({
                    "status": "failed",
                    "message": "cashier temrinal not found"
                }, status=status.HTTP_404_NOT_FOUND)

            qs = self.get_queryset()
            cashier_available = qs.filter(
                cashier_terminal__iexact = validated_data['cashier_terminal_number']
            ).exclude(
                pk = cashier_terminal_obj.pk
            ).exists()
            if cashier_available:
                return Response({
                    "status" : "failed",
                    "message" : "cashier name already used"
                }, status=status.HTTP_409_CONFLICT)

            cashier_terminal_obj.cashier_terminal = validated_data['cashier_terminal_number']
            cashier_terminal_obj.kc_number = validated_data['kc_number']
            cashier_terminal_obj.save()

            return_serializer = CashierTerminalSerializer(cashier_terminal_obj)
            return Response({
                "status" : "success",
                "message" : "cashier terminal edited",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    @action(
        methods=["PATCH"],
        detail=False,
        url_path="archive",
        url_name="archive"
    )
    def archive_cashier(self, request):
        serializer = CashierTerminalArchiveSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            try:
                cashier_terminal_obj = CashierTerminal.objects.get(pk = validated_data['cashier_terminal_id'])
            except:
                return Response({
                    "status": "failed",
                    "message": "cashier temrinal not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            cashier_terminal_obj.is_archive = validated_data['archive']
            cashier_terminal_obj.save()

            return Response({
                "status" : "success",
                "message" : "archived" if validated_data['archive'] else "unarchived"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )