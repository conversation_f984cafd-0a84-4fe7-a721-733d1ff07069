from rest_framework import serializers

from .models import CashierTerminal


class CashierTerminalSerializer(serializers.ModelSerializer):
    lot_number = serializers.CharField(source="lot.lot_number")
    class Meta:
        model = CashierTerminal
        fields = "__all__"


class CashierWriteSerializer(serializers.Serializer):
    cashier_terminal_number = serializers.CharField()
    kc_number = serializers.CharField(allow_blank=True)


class CashierEditSerializer(CashierWriteSerializer):
    cashier_terminal_id = serializers.IntegerField()


class CashierTerminalArchiveSerializer(serializers.Serializer):
    cashier_terminal_id = serializers.UUIDField()
    archive = serializers.BooleanField()

