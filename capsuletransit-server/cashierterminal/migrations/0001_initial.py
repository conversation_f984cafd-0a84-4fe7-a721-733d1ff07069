# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("lot", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CashierTerminal",
            fields=[
                (
                    "cashier_terminal_id",
                    models.AutoField(
                        primary_key=True,
                        serialize=False,
                        verbose_name="CashierTerminalID",
                    ),
                ),
                (
                    "cashier_terminal",
                    models.CharField(max_length=100, verbose_name="Cashier Terminal"),
                ),
                (
                    "is_archive",
                    models.BooleanField(default=False, verbose_name="Is Archive"),
                ),
                (
                    "lot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lot.lot",
                        verbose_name="LotID",
                    ),
                ),
            ],
        ),
    ]
