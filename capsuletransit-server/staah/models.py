from datetime import time
from uuid import uuid4

from bookings.models import Booking
from django.db.models.deletion import CASCADE
from django.db.models.fields import (
    BooleanField,
    CharField,
    DateTimeField,
    IntegerField,
    TimeField,
    UUIDField,
    TextField,
)
from django.db.models.fields.related import ForeignKey
from django.db.models.fields.json import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.base import Model
from rooms.models import RoomType


class StaahRoomType(Model):
    id = UUIDField("Room Type ID", primary_key=True, default=uuid4, editable=False)
    staah_id = CharField("Staah Room Type ID", max_length=20)
    old_staah_id = CharField("Old Staah Room Type ID", max_length=20, null=True)
    room_type = ForeignKey(
        RoomType, CASCADE, related_name="staah_room_types", verbose_name="Room Type"
    )
    name = Char<PERSON>ield("Name", max_length=100, null=True)
    check_in_time = TimeField("Check In Time", default=time.min)
    duration_hours = IntegerField("Duration Hours", default=24)

    class Meta:
        unique_together = ("room_type", "check_in_time", "duration_hours")

    @classmethod
    def get_first(cls, staah_id: str):
        return cls.objects.filter(staah_id=staah_id).first()


class StaahReservation(Model):
    id = UUIDField("Reservation ID", primary_key=True, default=uuid4, editable=False)
    booking = ForeignKey(
        Booking,
        CASCADE,
        related_name="staah_reservations",
        verbose_name="Booking",
        null=True,
    )

    json = JSONField("Reservation JSON")
    old_hms_status = TextField("Old HMS Status", null=True, blank=True)
    status = CharField("Status", max_length=1000, null=True, blank=True)
    is_manual_created = BooleanField(default=False)

    created_datetime = DateTimeField("Created Datetime", auto_now_add=True)
    updated_datetime = DateTimeField("Updated Datetime", auto_now=True)
