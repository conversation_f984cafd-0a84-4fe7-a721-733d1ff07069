# Generated by Django 4.2.3 on 2024-03-14 08:06

import datetime
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('rooms', '0003_room_is_upper'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaahRoomType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='Room Type ID')),
                ('staah_id', models.CharField(max_length=20, verbose_name='Staah Room Type ID')),
                ('check_in_time', models.TimeField(default=datetime.time(0, 0), verbose_name='Check In Time')),
                ('check_out_time', models.TimeField(default=datetime.time(23, 59, 59, 999999), verbose_name='Check Out Time')),
                ('room_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staah_room_types', to='rooms.roomtype', verbose_name='Room Type')),
            ],
        ),
    ]
