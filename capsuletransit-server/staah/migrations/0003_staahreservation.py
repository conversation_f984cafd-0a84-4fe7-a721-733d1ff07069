# Generated by Django 4.2.3 on 2024-03-19 10:23

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('bookings', '0004_roombooking_staah_room_reservation_id_and_more'),
        ('staah', '0002_alter_staahroomtype_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaahReservation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='Reservation ID')),
                ('json', models.JSONField(verbose_name='Reservation JSON')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, verbose_name='Created Datetime')),
                ('updated_datetime', models.DateTimeField(auto_now=True, verbose_name='Updated Datetime')),
                ('booking', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='staah_reservations', to='bookings.booking', verbose_name='Booking')),
            ],
        ),
    ]
