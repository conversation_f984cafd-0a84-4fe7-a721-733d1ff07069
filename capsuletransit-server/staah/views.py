import json
import traceback

import requests
from common.viewsets import PaginatableViewSet
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJ<PERSON>NRenderer
from rest_framework.mixins import ListModelMixin, UpdateModelMixin
from rest_framework.viewsets import GenericViewSet

from .models import StaahReservation, StaahRoomType
from .serializers import StaahReservationSerializer, StaahRoomTypeSerializer
from .staah import convert_to_old_format, update_or_create_ota_booking


class StaahRoomTypeViewSet(ListModelMixin, GenericViewSet):
    queryset = StaahRoomType.objects.all()
    serializer_class = StaahRoomTypeSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

class StaahReservationViewSet(UpdateModelMixin, ListModelMixin, PaginatableViewSet):
    queryset = StaahReservation.objects.filter(booking__isnull=True).order_by(
        "-created_datetime"
    )
    serializer_class = StaahReservationSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)


@csrf_exempt
def reservation_create(request):
    if request.method != "POST":
        return HttpResponse("Webhook for reservation create is listening")

    print("Data received:")
    print(req_data := json.loads(request.body))
    print()

    # response_text = "Not sent. Manually triggered from localhost."
    try:
        if formatted_data := convert_to_old_format(req_data):
            response = requests.post(
                "https://connect.interstellar.my/STAAH.svc/Reservation",
                json.dumps(formatted_data),
                headers={"Content-Type": "application/json"},
            )
            response_text = response.text
        else:
            response_text = "Not sent. Room not existed in old HMS."
    except Exception as e:
        response_text = f"{e}\n{traceback.format_exc(limit=3)}"

    reservation_notif_ids = []
    for reservation in req_data.get("reservations", []):
        staah_reservation = StaahReservation.objects.create(
            json=req_data, old_hms_status=response_text
        )
        try:
            staah_reservation.booking = update_or_create_ota_booking(reservation)
        except Exception as e:
            staah_reservation.status = str(e)
        staah_reservation.save()
        reservation_notif_ids.append(reservation.get("reservation_notif_id"))

    print("Data returned:")
    res_data = {"reservation_notif": {"reservation_notif_id": reservation_notif_ids}}
    print(res_data)
    print()

    return JsonResponse(res_data)


booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "confirm.bookings3234guest.booking.com",
                "first_name": "Confirm",
                "last_name": "Booking",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112745767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Confirm Booking",
                    "first_name": "Confirm",
                    "last_name": "Booking",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "new",
                    "roomreservation_id": "19876861234",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "1245255120001",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "1245255120001_KC2",
            "reservation_notif_id": "15967827724323456789",
            "modified_at": "2024-03-05",
            "status": "new",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
modified_booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Modified",
                "last_name": "Booking",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112745767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Modified Booking",
                    "first_name": "Modified",
                    "last_name": "Booking",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "modified",
                    "roomreservation_id": "198768612345",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "1245255120001",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "1245255120001_KC2",
            "reservation_notif_id": "159678277243234000231",
            "modified_at": "2023-10-29",
            "status": "modified",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
cancelled_booking = {
    "reservations": [
        {
            "booked_at": "2023-10-27",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Modified",
                "last_name": "Booking",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112745767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Modified Booking",
                    "first_name": "Modified",
                    "last_name": "Booking",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "cancelled",
                    "roomreservation_id": "198768612345",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "1245255120002",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "1245255120001_KC2",
            "reservation_notif_id": "159678277243234555444",
            "modified_at": "2024-03-05",
            "status": "cancelled",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
direct_modified_booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Direct",
                "last_name": "Modified",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112740767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Direct Modified",
                    "first_name": "Direct",
                    "last_name": "Modified",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "modified",
                    "roomreservation_id": "1004220033",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "1223644566",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "1223644566_KC2",
            "reservation_notif_id": "89956623374400",
            "modified_at": "2024-03-05",
            "status": "modified",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
direct_cancelled_booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Direct",
                "last_name": "Cancelled",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112740767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Direct Cancelled",
                    "first_name": "Direct",
                    "last_name": "Cancelled",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "cancelled",
                    "roomreservation_id": "1004244",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "*********",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "*********_KC2",
            "reservation_notif_id": "12457812546005105",
            "modified_at": "2024-03-05",
            "status": "cancelled",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
missing_info_booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "",
                "last_name": "",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112010767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "",
                    "first_name": "",
                    "last_name": "",
                    "id": "STD1",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "new",
                    "roomreservation_id": "5224116330",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "NRF",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "78845512230",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "78845512230_KC2",
            "reservation_notif_id": "45100380054599",
            "modified_at": "2024-03-05",
            "status": "new",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
unmapped_booking = {
    "reservations": [
        {
            "booked_at": "2024-03-05",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Unmapped",
                "last_name": "booking",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112010767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Unmapped booking",
                    "first_name": "Unmapped",
                    "last_name": "booking",
                    "id": "",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "new",
                    "roomreservation_id": "52241016330",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "*********",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "*********_KC2",
            "reservation_notif_id": "100380054555",
            "modified_at": "2024-03-05",
            "status": "new",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
booking_0000_00_00 = {
    "reservations": [
        {
            "booked_at": "0000-00-00",
            "commissionamount": "25.23",
            "currencycode": "AUD",
            "paymenttype": "Hotel Collect",
            "hotel_id": "KC2",
            "hotel_name": "Kings Cross",
            "paymentdue": "",
            "customer": {
                "address": "",
                "cc_cvc": "",
                "cc_expiration_date": "",
                "cc_name": "",
                "cc_number": "",
                "cc_type": "",
                "cc_current_balance": "",
                "cc_activation_date": "",
                "vcc_expiration_date": "",
                "cc_token": "",
                "cc_token_expiration": "",
                "cc_unique_code": "",
                "city": "Melbourne",
                "state": "Victoria",
                "countrycode": "AU",
                "email": "modified.bookings3234guest.booking.com",
                "first_name": "Test",
                "last_name": "booking",
                "remarks": "Approximate time of arrival: between 17:00 and 18:00Bed preference:Motel Room: 2 singles, 1 large doubleMotel Room: 2 singles, 1 large double |",
                "telephone": "+91112010767",
                "zip": "",
            },
            "rooms": [
                {
                    "arrival_date": "2024-03-08",
                    "departure_date": "2024-03-09",
                    "info": "There is no meal option with this room. Children and Extra Bed Policy: Children of any age are allowed. 1 child (up to and including 11 years old) stays for AUD 20 per child per night when using available extra beds. 1 person (12 years old and over) stays for AUD 30 per child per night when using available extra beds. You haven't added any cots. The maximum number of extra beds is 1. The maximum number of total guests is 4. Deposit Policy: The guest will be charged a prepayment of the total price of the reservation at any time. Cancellation Policy: The guest can cancel free of charge until 3 days before arrival. The guest will be charged the total price of the reservation if they cancel in the 3 days before arrival. --Child Age : 0",
                    "facilities": "smoking preference :: Non-Smoking, meal_plan : There is no meal option with this room. | rewritten_from_name : Domestic,genius_rate : no",
                    "guest_name": "Test booking",
                    "first_name": "Test",
                    "last_name": "booking",
                    "id": "",
                    "max_children": "1",
                    "numberofguests": "3",
                    "numberofchildren": "1",
                    "numberofadults": "2",
                    "roomstaystatus": "new",
                    "roomreservation_id": "52241776330",
                    "totalbeforetax": "191.16",
                    "totaltax": "19.12",
                    "totalprice": "210.28",
                    "price": [
                        {
                            "date": "2024-03-08",
                            "rate_id": "",
                            "mealplan_id": "15",
                            "mealplan": "All inclusive",
                            "tax": "19.12",
                            "pricebeforetax": "191.16",
                            "priceaftertax": "210.28",
                        }
                    ],
                    "adults": [],
                    "addons": [],
                    "extracomponents": [],
                }
            ],
            "affiliation": {
                "pos": "Booking.com",
                "source": "booking.com",
                "OTA_Code": "19",
            },
            "otadue": "",
            "payment_charge": "",
            "channel_booking_id": "56854700",
            "thread_id": "",
            "guest_id": "",
            "listingbaseprice": "",
            "processed_at": "2024-03-05 09:30:19",
            "vendor_booking_id": "",
            "id": "56854700_KC2",
            "reservation_notif_id": "32236635507878",
            "modified_at": "2024-03-05",
            "status": "new",
            "totalprice": "210.28",
            "totaltax": "19.12",
            "extrafees": [],
            "taxes": [],
        }
    ]
}
