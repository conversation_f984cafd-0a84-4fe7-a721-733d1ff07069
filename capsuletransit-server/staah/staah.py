import json
from datetime import datetime as dt
from datetime import timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from accounts.models import Account, Shift, ShiftSettings, StaffShift
from airportCode.models import AirportCode
from bookingplatform.models import Platform
from bookings.models import Booking, BookingNumberCounter, BookingStatus, RoomBooking
from bookings.utils import generate_booking_no
from cashierterminal.models import CashierTerminal
from constant.enums import TransactionItemCategory
from django.db.models import Exists, OuterRef, Q
from django.db.transaction import atomic
from lot.models import Lot, Settings
from payment.models import PaymentType
from rooms.models import Room
from staah.models import StaahRoomType
from transaction.models import Transaction
from transaction.utils import generate_invoice_no


def convert_to_old_format(
    reservations: Dict[str, List[Dict[str, Any]]]
) -> Optional[Dict[str, Dict[str, List[Dict[str, Any]]]]]:
    for reservation in reservations.get("reservations", {}):
        for room in reservation.get("rooms", []):
            if not (StaahRoomType.get_first(staah_id=room.get("id"))).old_staah_id:
                return

    return {
        "reservations": {
            "reservation": [
                {
                    "booking_id": reservation.get("id"),
                    "channel_ref": reservation.get("channel_booking_id"),
                    "booking_date": reservation.get("booked_at"),
                    "currencycode": reservation.get("currencycode"),
                    "booking_status": reservation.get("status"),
                    "totalprice": float(reservation.get("totalprice") or 0),
                    "hotel_id": int(reservation.get("hotel_id")),
                    "hotel_name": reservation.get("hotel_name"),
                    "room": [
                        {
                            "arrival_date": room.get("arrival_date"),
                            "departure_date": room.get("departure_date"),
                            "currencycode": reservation.get("currencycode"),
                            "id": (
                                staah_room_type := StaahRoomType.get_first(
                                    staah_id=room.get("id")
                                )
                            ).old_staah_id,
                            "name": staah_room_type.name,
                            "price": [
                                {
                                    "date": price.get("date"),
                                    "rate_id": int(price.get("rate_id")),
                                    "amount": float(price.get("priceaftertax") or 0),
                                }
                                for price in room.get("price", [])
                            ],
                            "totalprice": float(room.get("totalprice") or 0),
                            "remarks": room.get("specialrequest"),
                            "guest_name": room.get("guest_name"),
                            "numberofguests": int(room.get("numberofguests") or 0),
                            "numberofadult": int(room.get("numberofadults") or 0),
                            "numberofchild": int(room.get("numberofchildren") or 0),
                        }
                        for room in reservation.get("rooms", [])
                    ],
                    "customer": {
                        "first_name": (customer := reservation.get("customer", {})).get(
                            "first_name"
                        ),
                        "last_name": customer.get("last_name"),
                        "address": customer.get("address"),
                        "city": customer.get("city"),
                        "countrycode": customer.get("countrycode"),
                        "email": customer.get("email"),
                        "telephone": customer.get("telephone"),
                        "remarks": customer.get("remarks"),
                        "zip": customer.get("zip"),
                    },
                    "company": reservation.get("affiliation", {}).get("source"),
                    "commissionamount": float(reservation.get("commissionamount") or 0),
                    "deposit": float(reservation.get("deposit") or 0),
                }
                for reservation in reservations.get("reservations", {})
            ]
        }
    }


class Status:
    BOOKED = "Booked"
    CONFIRM_BOOKING = "Confirm Booking"
    CANCELLED = "Cancelled"


@atomic
def update_or_create_ota_booking(reservation: Dict[str, Any]):
    # Reservation subobjects
    customer = reservation.get("customer", {})
    rooms_data = reservation.get("rooms", [])
    affiliation = reservation.get("affiliation", {})

    # Referenced models
    platforms = Platform.objects.filter(
        platform__iexact=f"OTA - {affiliation.get('source')}"
    )
    platform = (
        platforms.first()
        if platforms
        else Platform.objects.get(ota_code=affiliation.get("OTA_Code"))
    )
    lot = Lot.objects.get(staah_property_id=reservation.get("hotel_id"))

    sst_setting, service_charge_setting = tuple(
        Settings.objects.get(settings_name=setting_name, lot=lot)
        for setting_name in ["SST", "Service Charge"]
    )
    sst, service_charge = tuple(
        Decimal(setting.settings_description[:-1]) / 100
        for setting in [sst_setting, service_charge_setting]
    )

    # Modify or create booking object
    ota_code = reservation.get("channel_booking_id")
    airport_code = AirportCode.objects.get(lot=lot).airport_code
    datetime_string = reservation.get("processed_at")
    booking_datetime = dt.strptime(datetime_string, "%Y-%m-%d %H:%M:%S")
    malaysia_timezone = timezone(timedelta(hours=8))
    booking_datetime = booking_datetime.astimezone(malaysia_timezone)
    customer_name = f"{customer.get('first_name')} {customer.get('last_name')}"
    adult_pax = sum(int(room.get("numberofadults", 0)) for room in rooms_data)
    child_pax = sum(int(room.get("numberofchildren", 0)) for room in rooms_data)
    detail_list = [reservation.get("paymenttype"), customer.get("remarks")]
    total_price = sum(Decimal(room.get("totalprice", 0)) for room in rooms_data)
    _sum = total_price / (1 + sst + service_charge)
    tax, charge = round(_sum * sst, 2), round(_sum * service_charge, 2)
    booking_defaults = {
        "booking_made_datetime": booking_datetime,
        "customer_booked": customer_name,
        "adult_pax": adult_pax,
        "child_pax": child_pax,
        "details": "\n".join(detail for detail in detail_list if detail),
        "sum": total_price - tax - charge,
        "lot": lot,
    }

    booking, is_created = Booking.objects.update_or_create(
        platform=platform,
        ota_code=ota_code,
        is_cancelled=False,
        defaults=booking_defaults,
    )
    booking.is_cancelled = reservation.get("status") == "cancelled"
    booking.save()

    print("Booking", "created:" if is_created else "modified:", booking.booking_id)

    if is_created:
        with BookingNumberCounter.Context(airport_code) as counter:
            booking_no = generate_booking_no(airport_code, counter.counter)
            booking.booking_no = booking_no
            booking.save()

    # Modify or create booking status object(s)
    transactions = Transaction.objects.filter(booking=booking, is_room_booking=True)
    transaction = transactions.first()
    is_paid = transaction.transaction_status == "Paid" if transaction else False
    # reservation.get("paymenttype") == "Channel Collect"
    status = (
        Status.CANCELLED
        if booking.is_cancelled
        else Status.CONFIRM_BOOKING if is_paid else Status.BOOKED
    )
    overall_check_in_datetime = min(
        dt.combine(
            dt.strptime(room.get("arrival_date"), "%Y-%m-%d").date(),
            StaahRoomType.get_first(staah_id=room.get("id")).check_in_time,
            malaysia_timezone,
        )
        for room in rooms_data
    )
    overall_check_out_datetime = max(
        dt.combine(
            check_in_date := dt.strptime(room.get("arrival_date"), "%Y-%m-%d").date(),
            (
                staah_room_type := StaahRoomType.get_first(staah_id=room.get("id"))
            ).check_in_time,
            malaysia_timezone,
        )
        + timedelta(hours=staah_room_type.duration_hours)
        * max(
            (
                dt.strptime(room.get("departure_date"), "%Y-%m-%d").date()
                - check_in_date
            ).days,
            1,
        )
        for room in rooms_data
    )
    grace_minutes = Settings.objects.get(
        settings_name="Grace Period", settings_category="Room Settings", lot=lot
    ).settings_description
    grace_minutes = int(grace_minutes)
    max_check_out_datetime = overall_check_out_datetime + timedelta(
        minutes=grace_minutes
    )
    booking_status_defaults = {
        "check_in_datetime": overall_check_in_datetime,
        "check_out_datetime": overall_check_out_datetime,
        "max_check_out_datetime": max_check_out_datetime,
        "is_latest": True,
    }

    BookingStatus.objects.filter(booking=booking).update(is_latest=False)
    BookingStatus.objects.update_or_create(
        booking=booking, booking_status=status, defaults=booking_status_defaults
    )

    # Modify or create room booking objects
    # Constraint: Cannot add, remove or replace room in a booking
    rooms = []
    for room_data in rooms_data:
        staah_room_type = StaahRoomType.get_first(staah_id=room_data.get("id"))
        room_reservation_id = room_data.get("roomreservation_id")
        check_in_datetime = dt.combine(
            check_in_date := dt.strptime(
                room_data.get("arrival_date"), "%Y-%m-%d"
            ).date(),
            staah_room_type.check_in_time,
            malaysia_timezone,
        )
        max_check_out_datetime = (
            check_in_datetime
            + timedelta(hours=staah_room_type.duration_hours)
            * max(
                (
                    dt.strptime(room_data.get("departure_date"), "%Y-%m-%d").date()
                    - check_in_date
                ).days,
                1,
            )
            + timedelta(minutes=grace_minutes)
        )
        room = (
            Room.objects.filter(is_archived=False)
            .prefetch_related(
                "room_type__roomzone",
                "room_type__staah_room_types",
                "room_bookings__booking__platform",
                "room_bookings__booking__booking_status",
            )
            .filter(
                ~Exists(
                    RoomBooking.objects.filter(
                        ~Q(booking__platform__platform="OTA-Reservation Slot"),
                        room_id=OuterRef("pk"),
                        booking__booking_status__is_latest=True,
                        booking__booking_status__check_in_datetime__lte=max_check_out_datetime,
                        booking__booking_status__max_check_out_datetime__gte=check_in_datetime,
                    )
                ),
                room_type__roomzone__lot_id=lot,
                room_type__staah_room_types__staah_id=room_data.get("id"),
                room_bookings__booking__platform__platform="OTA-Reservation Slot",
                room_bookings__booking__booking_status__is_latest=True,
                room_bookings__booking__booking_status__check_in_datetime__lte=check_in_datetime,
                room_bookings__booking__booking_status__max_check_out_datetime__gte=check_in_datetime,
            )
            .filter(
                room_bookings__booking__platform__platform="OTA-Reservation Slot",
                room_bookings__booking__booking_status__is_latest=True,
                room_bookings__booking__booking_status__check_in_datetime__lte=max_check_out_datetime,
                room_bookings__booking__booking_status__max_check_out_datetime__gte=max_check_out_datetime,
            )
            .first()
            if is_created
            else Room.objects.get(
                room_bookings__booking=booking,
                room_bookings__staah_room_reservation_id=room_reservation_id,
            )
        )
        if is_created and not room:
            room = (
                Room.objects.filter(is_archived=False)
                .prefetch_related(
                    "room_type__roomzone",
                    "room_type__staah_room_types",
                    "room_bookings__booking__platform",
                    "room_bookings__booking__booking_status",
                )
                .filter(
                    ~Exists(
                        RoomBooking.objects.filter(
                            ~Q(booking__platform__platform="OTA-Reservation Slot"),
                            room_id=OuterRef("pk"),
                            booking__booking_status__is_latest=True,
                            booking__booking_status__check_in_datetime__lte=max_check_out_datetime,
                            booking__booking_status__max_check_out_datetime__gte=check_in_datetime,
                        )
                    ),
                    room_type__roomzone__lot_id=lot,
                    room_type__staah_room_types__staah_id=room_data.get("id"),
                )
                .first()
            )
        if not room:
            raise Exception(
                ("No available room for booking" if is_created else "Room not found")
                + f". Room Reservation ID: {room_reservation_id}. Staah Room Type ID: {room_data.get('id')}"
            )
        rooms.append(room)

        total_price = Decimal(room_data.get("totalprice"))
        _sum = total_price / (1 + sst + service_charge)
        tax, charge = round(_sum * sst, 2), round(_sum * service_charge, 2)
        RoomBooking.objects.update_or_create(
            booking=booking,
            room=room,
            staah_room_reservation_id=room_reservation_id,
            defaults={
                "sum": total_price - tax - charge,
                "status": status,
            },
        )
    RoomBooking.objects.filter(booking=booking).update(status=status)

    # Get or create superadmin shift object
    if transaction and is_paid:
        shift = transaction.shift
    else:
        booking_time = booking_datetime.time()
        account = Account.objects.get(
            username__istartswith="superadmin", lot=lot, is_archive=False
        )
        shift_setting = ShiftSettings.objects.get(
            start_hour__lte=booking_time,
            end_hour__gte=booking_time,
            is_archived=False,
        )
        staff_shift = StaffShift.objects.get(
            staff=account, shiftsettings=shift_setting, is_archived=False
        )
        shift_defaults = {
            "cashier_terminal": CashierTerminal.objects.filter(
                lot=lot, is_archive=False
            ).first(),
            "start_shift_datetime": dt.combine(
                booking_datetime.date(), shift_setting.start_hour
            ),
            "start_personal_cash_amount": 0,
        }
        shift, is_created = Shift.objects.get_or_create(
            staffshift=staff_shift,
            end_shift_datetime=None,
            defaults=shift_defaults,
        )

    # Modify or create transaction object
    payment_type = "Channel Collect" if is_paid else "Paylater"
    payment_type = PaymentType.objects.get(payment_type__iexact=payment_type)
    duration = (
        overall_check_out_datetime - overall_check_in_datetime
    ).total_seconds() / 3600

    sum_, tax_amount, service_charge_amount = Decimal(0), Decimal(0), Decimal(0)
    payment_details = []
    for room, room_data in zip(rooms, rooms_data):
        total_price = Decimal(room_data.get("totalprice"))
        _sum = total_price / (1 + sst + service_charge)
        tax_amount += (tax := round(_sum * sst, 2))
        service_charge_amount += (charge := round(_sum * service_charge, 2))
        sum_ += (price := total_price - tax - charge)
        payment_detail = {
            "item_id": str(room.room_id),
            "item_name": room.room_code,
            "item_type": room.room_type.type_name,
            "quantity": 1,
            "duration": int(duration),
            "price": float(price),
            "category": TransactionItemCategory.ROOM_SALES,
        }
        payment_details.append(payment_detail)
    charge_category = TransactionItemCategory.SERVICE_CHARGE
    setting_details = [
        (sst_setting, tax_amount, TransactionItemCategory.TAX),
        (service_charge_setting, service_charge_amount, charge_category),
    ]
    for setting, price, category in setting_details:
        payment_detail = {
            "item_id": setting.settings_id,
            "item_name": f"{setting.settings_name} {setting.settings_description}",
            "item_type": setting.settings_category,
            "price": float(price),
            "category": category,
        }
        payment_details.append(payment_detail)
    total_price = reservation.get("totalprice")
    transaction_defaults = {
        "payment_type": payment_type,
        "pan": customer.get("cc_number")[-4:] or None,
        "currency_id": 1,
        "shift": shift,
        "invoice_no": generate_invoice_no(booking.booking_no, dt.now()),
        "payment_details": json.dumps(payment_details),
        "sum": sum_,
        "tax_amount": tax_amount,
        "service_charge_amount": service_charge_amount,
        "adjustements_amount": 0,
        "promotion_amount": 0,
        "credit_amount": Decimal(0 if is_paid else total_price),
        "debit_amount": Decimal(total_price if is_paid else 0),
        "transaction_datetime": booking_datetime if is_paid else None,
        "transaction_status": (
            "Paid" if is_paid else "Void" if booking.is_cancelled else "Pending Payment"
        ),
        "is_latest": True,
        "items": payment_details,
        "payment_reference": customer.get("cc_token") or None,
        "payment_remarks": None if is_paid else "Channel Collect",
    }
    Transaction.objects.update_or_create(
        booking=booking, is_room_booking=True, defaults=transaction_defaults
    )

    return booking
