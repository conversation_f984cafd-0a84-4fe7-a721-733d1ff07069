from rest_framework.serializers import ModelSerializer

from .models import StaahReservation, StaahRoomType


class StaahRoomTypeSerializer(ModelSerializer):
    class Meta:
        model = StaahRoomType
        fields = ("staah_id", "name")
        read_only_fields = ("staah_id", "name")


class StaahReservationSerializer(ModelSerializer):
    class Meta:
        model = StaahReservation
        fields = (
            "id",
            "json",
            "status",
            "is_manual_created",
            "created_datetime",
            "updated_datetime",
        )
        read_only_fields = (
            "id",
            "json",
            "status",
            "created_datetime",
            "updated_datetime",
        )
