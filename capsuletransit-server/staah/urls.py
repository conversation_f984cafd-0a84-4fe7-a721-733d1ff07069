from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>

from .views import StaahReservationViewSet, StaahRoomTypeViewSet, reservation_create

router = DefaultRouter()
router.register("room-types", StaahRoomTypeViewSet)
router.register("reservations", StaahReservationViewSet)

urlpatterns = [
    path("", include(router.urls)),
    path("reservation_create/", reservation_create, name="reservation_create"),
]
