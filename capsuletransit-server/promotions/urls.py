from os import path
from rest_framework import routers
from .views import  PromotionViewset, IndividualPromoViewSet
from django.urls import include, path

router = routers.DefaultRouter()
router.register("", PromotionViewset, basename="promotion")
router.register("individual-promo", IndividualPromoViewSet, basename="individualpromo")
urlpatterns = [
    path('', include(router.urls)),
    path('individual-promo/get-by-id/<int:individual_promo_id>/', IndividualPromoViewSet.as_view({'get': 'individual_promo'}), name='get-by-id'),
]