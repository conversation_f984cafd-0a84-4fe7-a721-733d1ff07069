from promotions.models import Promotion, PromotionUsage, IndividualPromo, MemberPromolist
from bookings.models import BookingStatus
from guests.models import Customer
from django.utils.timezone import datetime
from django.db.models import Q
from django.db import transaction as tr

from transaction.models import Transaction

from promotions.serializers import PromotionSerialzier


def get_promotion_list(request):
    if request.query_params.get("search"):
        search_query = request.query_params.get("search")
        promotions = Promotion.objects.filter(
            Q(promo_code__icontains=search_query) | Q(details__icontains=search_query)
        )
        seriliazer = PromotionSerialzier(promotions, many=True)
    else:
        promotions = Promotion.objects.all()
        seriliazer = PromotionSerialzier(promotions, many=True)
    active_promotions = promotions.filter(archived=False).count()
    inactive_promotions = promotions.filter(archived=True).count()
    return seriliazer.data, active_promotions, inactive_promotions



def handle_use_promo(transaction_id):

    with tr.atomic():
        transaction_obj = Transaction.objects.get(pk = transaction_id)
        transaction_items = transaction_obj.items

        promo_id = ""
        for item in transaction_items:
            if item['item_type'] != "Promotion": continue
            promo_id = item['item_id']
        
        if promo_id == "": return
        
        promo = Promotion.objects.select_for_update().filter(pk = promo_id).first()

        # if using normal promo
        if promo:
            if promo.seats == 0:
                raise Exception('promo seats insufficient')
            
            if promo.seats - 1 == 0:
                promo.archived = True

            promo.seats = promo.seats - 1
            
            promotion_usage = PromotionUsage.objects.create(
                customer_id = transaction_obj.customer_id,
                promotion_id = promo_id,
                transaction_id = transaction_obj.pk
            )

            promo.save()

            return
        
        # if using individual promo
        individual_promo = IndividualPromo.objects.filter(
            individual_promo_id=promo_id
        ).first()

        if not individual_promo: return
        
        customer : Customer = transaction_obj.booking.customer_staying

        member_promo_list = MemberPromolist.objects.filter(
            individual_promo_id=promo_id
        ).first()

        if not member_promo_list: return

        member_promo_list.transaction_id = transaction_id
        member_promo_list.used = True
        member_promo_list.save()



def revert_used_individual_promo(transaction_id):

    room_booking_transaction = Transaction.objects.get(transaction_id = transaction_id)

    member_claimed_promo = MemberPromolist.objects.filter(
        transaction_id = room_booking_transaction.transaction_id
    ).first()

    if not member_claimed_promo: return

    member_claimed_promo.used = False
    member_claimed_promo.transaction_id = None
    member_claimed_promo.save()

        
