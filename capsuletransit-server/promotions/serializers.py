from guests.serializer import CustomerSerializer
from guests.models import Customer
from transaction.models import Transaction
from .models import Promotion, PromotionUsage, IndividualPromo
from rest_framework import serializers
class PromotionSerialzier(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = Promotion





class CustomerSerializer(CustomerSerializer):
    class Meta:
        model = Customer
        fields = ["firstname", "lastname", "member"]

class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = '__all__'

class PromotionUsageSerialzier(serializers.ModelSerializer):

    customer = CustomerSerializer()
    promotion = PromotionSerialzier()
    transaction = TransactionSerializer()

    class Meta:
        fields = '__all__'
        model = PromotionUsage

class IndividualPromoSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = IndividualPromo

class CreateIndividualPromoSerializer(serializers.Serializer):
    promo_code = serializers.CharField(max_length=20)
    details = serializers.CharField(max_length=50)
    promo_start_datetime = serializers.DateTimeField()
    promo_end_datetime = serializers.DateTimeField()
    seats = serializers.IntegerField()
    stackable = serializers.BooleanField(default=False)
    archived = serializers.BooleanField(default=False)
    max_capped = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        allow_null=True
    )
    is_milestone = serializers.BooleanField(default=False)
    hours_to_hit = serializers.IntegerField(default=0)
    overwrite = serializers.BooleanField(default=False)

class EditIndividualArchiveSerializer(serializers.Serializer):
    individual_promo_id = serializers.UUIDField()
    new_archived = serializers.BooleanField(required=True, write_only=True)
