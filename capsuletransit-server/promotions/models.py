from typing import Set
from django.db import models
from uuid import uuid4
from guests.models import Customer
from transaction.models import Transaction

class Promotion(models.Model):
    promotion_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    promo_code = models.CharField(max_length=20) 
    details = models.CharField(max_length=50) 
    promo_start_datetime = models.DateTimeField(verbose_name="Promo Start Datetime")
    promo_end_datetime = models.DateTimeField(verbose_name="Promo End Datetime")
    seats = models.IntegerField(null=False)
    stackable = models.BooleanField(default=False, null=True, blank=True)
    member_only = models.BooleanField(default=False)
    visible_at_website = models.BooleanField(default=False)
    archived = models.BooleanField(default=False)


    promotion_usage : Set["PromotionUsage"]
    def use_promotion(self, customer : Customer, transaction : Transaction):
        """
        Invokation method to wrap use promotion
        """
        if self.seats > 0:
            self.seats -= 1
            self.save()
            usage = PromotionUsage()
            usage.customer = customer
            usage.transaction = transaction
            self.promotion_usage.add(usage)
            return True
        else:
            return False

class PromotionUsage(models.Model):
    promo_usage_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    customer = models.ForeignKey(Customer, null=True, on_delete=models.RESTRICT)
    promotion = models.ForeignKey(Promotion, null=True, on_delete=models.RESTRICT, related_name="promotion_usage")
    transaction = models.ForeignKey(Transaction, null=True, on_delete=models.RESTRICT)


# LP Individual Promo
class IndividualPromo(models.Model):
    individual_promo_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    promo_code = models.CharField(max_length=20) 
    details = models.CharField(max_length=50) 
    promo_start_datetime = models.DateTimeField(verbose_name="Promo Start Datetime")
    promo_end_datetime = models.DateTimeField(verbose_name="Promo End Datetime")
    seats = models.IntegerField(null=False)
    stackable = models.BooleanField(default=False, null=True, blank=True)
    archived = models.BooleanField(default=False)
    max_capped = models.DecimalField(
        default=None,
        decimal_places=2,
        max_digits=10,
        null=True,
        blank=True
    )
    is_milestone = models.BooleanField(default=False)
    hours_to_hit = models.IntegerField(default=0) # for is_mailstone
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def deduct_seats(self, deduct_by : int):
        if self.seats < deduct_by:
            return
        
        self.seats -= deduct_by
        self.save()


# Customer Promo
class MemberPromolist(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    individual_promo = models.ForeignKey(IndividualPromo, on_delete=models.CASCADE)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    transaction = models.ForeignKey(
        Transaction,
        null=True,
        on_delete=models.RESTRICT,
        verbose_name="member-promo-transaction",
    )
    used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)




