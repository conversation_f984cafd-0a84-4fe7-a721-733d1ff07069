# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("guests", "0001_initial"),
        ("transaction", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Promotion",
            fields=[
                (
                    "promotion_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("promo_code", models.CharField(max_length=20)),
                ("details", models.CharField(max_length=50)),
                (
                    "promo_start_datetime",
                    models.DateTimeField(verbose_name="Promo Start Datetime"),
                ),
                (
                    "promo_end_datetime",
                    models.DateTimeField(verbose_name="Promo End Datetime"),
                ),
                ("seats", models.IntegerField()),
                (
                    "stackable",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                ("member_only", models.BooleanField(default=False)),
                ("archived", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="PromotionUsage",
            fields=[
                (
                    "promo_usage_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.customer",
                    ),
                ),
                (
                    "promotion",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="promotion_usage",
                        to="promotions.promotion",
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="transaction.transaction",
                    ),
                ),
            ],
        ),
    ]
