# Generated by Django 4.2.3 on 2024-07-23 12:00

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('guests', '0004_customeraccount_created_at_and_more'),
        ('transaction', '0007_alter_transaction_payment_reference'),
        ('promotions', '0002_promotion_visible_at_website'),
    ]

    operations = [
        migrations.CreateModel(
            name='IndividualPromo',
            fields=[
                ('individual_promo_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('promo_code', models.CharField(max_length=20)),
                ('details', models.CharField(max_length=50)),
                ('promo_start_datetime', models.DateTimeField(verbose_name='Promo Start Datetime')),
                ('promo_end_datetime', models.DateTimeField(verbose_name='Promo End Datetime')),
                ('seats', models.IntegerField()),
                ('stackable', models.BooleanField(blank=True, default=False, null=True)),
                ('archived', models.BooleanField(default=False)),
                ('max_capped', models.DecimalField(blank=True, decimal_places=2, default=None, max_digits=10, null=True)),
                ('is_milestone', models.BooleanField(default=False)),
                ('hours_to_hit', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='MemberPromolist',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='guests.customer')),
                ('individual_promo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='promotions.individualpromo')),
                ('transaction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.RESTRICT, to='transaction.transaction', verbose_name='member-promo-transaction')),
            ],
        ),
    ]
