from promotions.models import Promotion, PromotionUsage, IndividualPromo, MemberPromolist
from django.http import JsonResponse
from django.shortcuts import render, get_object_or_404

# Create your views here.
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from djangorestframework_camel_case.parser import (
    CamelCaseMultiPartParser,
    CamelCaseFormParser,
    CamelCaseJSONParser,
)
from djangorestframework_camel_case.render import CamelCaseJSONRenderer

from promotions.promotion import get_promotion_list
from .serializers import (
    PromotionSerialzier,
    PromotionUsageSerialzier,
    IndividualPromoSerializer,
    CreateIndividualPromoSerializer,
    PromotionSerialzier, 
    PromotionUsageSerialzier,
    IndividualPromoSerializer,
    CreateIndividualPromoSerializer,
    EditIndividualArchiveSerializer
)

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication


class PromotionViewset(viewsets.ModelViewSet):
    permission_classes = []
    authentication_classes = []
    queryset = Promotion.objects.all().order_by("-promo_start_datetime")
    parser_classes = (CamelCaseMultiPartParser, CamelCaseFormParser)
    renderer_classes = (CamelCaseJSONRenderer,)
    serializer_class = PromotionSerialzier

    def get_queryset(self):
        return Promotion.objects.all()

    def list(self, request, *args, **kwargs):
        try:
            (
                promotions,
                active_promotion_num,
                inactive_promotion_num,
            ) = get_promotion_list(request)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {
                "promotions": promotions,
                "active_promotion_num": active_promotion_num,
                "inactive_promotion_num": inactive_promotion_num,
                "message": "Retrieved all promotions",
            },
            status=status.HTTP_200_OK,
        )

    def create(self, request):
        serializer = PromotionSerialzier(data=request.data)
        if serializer.is_valid():
            model = serializer.save()
            model.save()
            return Response(
                {
                    "status": "success",
                    "message": "new promotion registered",
                    "promotionId": model.pk,
                },
                status=status.HTTP_201_CREATED,
            )
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        queryset = Promotion.objects.all()
        customer = get_object_or_404(queryset, pk=pk)
        serializer = PromotionSerialzier(customer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_destroy(self, instance: Promotion):
        instance.archived = True
        instance.save()
        return Response(
            data={
                "status": "OK",
            },
            status=204,  # No Content
        )

    @swagger_auto_schema(request_body=PromotionSerialzier, responses={200: "success"})
    @action(
        methods=["PUT"],
        detail=False,
        url_path="archive/(?P<promotion_id>[0-9a-f-]+)",
        url_name="archive",
        permission_classes=[],
    )
    def archive_promotion(self, request, promotion_id):
        try:
            promotion = Promotion.objects.get(pk=promotion_id)
            promotion.archived = True
            promotion.save()
            return Response(
                {"status": "success", "msg": "Promotion archive success"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(request_body=PromotionSerialzier, responses={200: "success"})
    @action(
        methods=["PUT"],
        detail=False,
        url_path="un-archive/(?P<promotion_id>[0-9a-f-]+)",
        url_name="un-archive",
        permission_classes=[],
    )
    def unarchive_promotion(self, request, promotion_id):
        try:
            promotion = Promotion.objects.get(pk=promotion_id)
            promotion.archived = False
            promotion.save()
            return Response(
                {"status": "success", "msg": "Promotion un-archive success"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PromotionUsageViewSet(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []
    queryset = PromotionUsage
    parser_classes = (CamelCaseFormParser,)
    renderer_classes = (CamelCaseJSONRenderer,)
    serializer_class = PromotionUsageSerialzier

    def list(self, request):
        qs = PromotionUsage.objects.all()
        serializer = PromotionUsageSerialzier(qs, many=True)
        return Response(serializer.data)


class IndividualPromoViewSet(viewsets.ViewSet):
    permission_classes=[IsAuthenticated]
    authentication_classes=[JWTAuthentication]
    parser_classes = (CamelCaseFormParser,CamelCaseJSONParser,)
    renderer_classes = (CamelCaseJSONRenderer,)

    def get_queryset(self):
        return IndividualPromo.objects.all()
    
    @action(
        detail=False,
        methods=['GET'],
        url_path="list",
        url_name="list",
    )
    def individual_promo_list(self, request):
        customer_id = request.GET.get("customerId", None)
        used = request.GET.get("used", None)

        try:
            promo_list = IndividualPromo.objects.all()

            if customer_id:
                promo_list = promo_list.prefetch_related(
                    "memberpromolist_set"
                ).filter(
                    memberpromolist__customer__customer_id=customer_id,
                    memberpromolist__used=False
                )
                
            if used == "true":
                promo_list = promo_list.filter(
                    memberpromolist__used = True
                )
            
            if used == "false":
                promo_list = promo_list.filter(
                    memberpromolist__used = False
                )

            if not promo_list.exists():
              return Response(
                {"status": "failed", "message": "No individual promo found"},
                status=status.HTTP_200_OK
            )
            serializer = IndividualPromoSerializer(promo_list,many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK
            )
        except IndividualPromo.DoesNotExist:
            return Response(
                {"status":"failed","message":"individual promo not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "Server Error","error":str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(
        detail=False,
        methods=['GET'],
        url_path="get-by-id/(?P<individual_promo_id>[0-9a-f-]+)",
        url_name="get-by-id",
    )
    def individual_promo(self, request, individual_promo_id):
        try:
            promo_list = IndividualPromo.objects.filter(individual_promo_id=individual_promo_id)
            if not promo_list.exists():
                return Response(
                    {"status": "failed", "message": "No individual promo found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            serializer = IndividualPromoSerializer(promo_list, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "Server Error","error":str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        
    @action(
        detail=False,
        methods=['POST'],
        url_path="create",
        url_name="create",
    )
    def create_individual_promo(self,request):
        serializer = CreateIndividualPromoSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status":"failed","message":serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            
            validated_data = serializer.validated_data
            is_milestone = validated_data.get('is_milestone', False)
            hours_to_hit = validated_data.get('hours_to_hit',0)
            promo_start_datetime = validated_data.get('promo_start_datetime')
            promo_end_datetime = validated_data.get('promo_end_datetime')
            overwrite = validated_data.get('overwrite', False)

            if is_milestone:
                if hours_to_hit <= 0:
                    return Response(
                        {"status":"failed","message":"If is_milestone is true, hours_to_hit must be greater than 0."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                
                conflict_promo = IndividualPromo.objects.filter(
                    is_milestone=True,
                    archived=False,
                    promo_start_datetime__lt =promo_end_datetime,
                    promo_end_datetime__gt = promo_start_datetime
                )
                if conflict_promo.exists():
                    if overwrite:
                        conflict_promo.update(archived = True)
                    else:
                        return Response(
                            {"status":"failed","message":"The date range overlaps with an existing promo"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
            validated_data.pop('overwrite',None)
                    
            validated_data['hours_to_hit'] = 0 if not is_milestone else hours_to_hit
            validated_data['archived'] = False
            individual_promo = IndividualPromo.objects.create(**validated_data)
            indiserializer = IndividualPromoSerializer(individual_promo)
            return Response(
                {"status":"success","message":"Individual promo created successfully","data":indiserializer.data},
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(
                {"status":"failed","message":"server error","error":str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    @action(
        detail=False,
        methods=['PUT'],
        url_path="edit",
        url_name="edit",
    )
    def edit_individial_promo(self,request):
        try:
            individual_promo_id = request.data.get('individual_promo_id')
            if not individual_promo_id:
                return Response(
                    {"status":"failed","message":"Individual promo ID is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            try:
                individual_promo = IndividualPromo.objects.get(pk=individual_promo_id)
            except IndividualPromo.DoesNotExist:
                return Response(
                    {"status":"failed","message":"Individual promo does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            serializer = CreateIndividualPromoSerializer(data=request.data, partial=True)
            if not serializer.is_valid():
                return Response(
                    {"status":"failed","message":serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            validated_data = serializer.validated_data
            is_milestone = validated_data.get('is_milestone',individual_promo.is_milestone)
            hours_to_hit = validated_data.get('hours_to_hit',individual_promo.hours_to_hit)
            promo_start_datetime = validated_data.get('promo_start_datetime',individual_promo.promo_start_datetime)
            promo_end_datetime = validated_data.get('promo_end_datetime',individual_promo.promo_end_datetime)
            overwrite = validated_data.get('overwrite',False)

            if individual_promo.is_milestone and hours_to_hit != individual_promo.hours_to_hit:
                return Response({
                    "status" : "failed",
                    "message" : "Unable to change hours to hit. Create new milestone promo instead"
                }, status=status.HTTP_403_FORBIDDEN)

            if is_milestone:
                if hours_to_hit <= 0:
                    return Response(
                        {"status":"failed","message":"If is_milestone is true, hours_to_hit must be greater than 0."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                
                conflict_promo = IndividualPromo.objects.filter(
                    is_milestone=True,
                    archived=False,
                    promo_start_datetime__lt=promo_end_datetime,
                    promo_end_datetime__gt = promo_start_datetime
                ).exclude(pk=individual_promo_id)

                if conflict_promo.exists():
                    if overwrite:
                        conflict_promo.update(archived=True)
                    else:
                        return Response(
                            {"status":"failed","message":"The date range overlaps with an existing promo."},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
            else:
                validated_data['hours_to_hit'] = 0

            for attr, value in validated_data.items():
                setattr(individual_promo, attr, value)
            individual_promo.save()

            indiserializer = IndividualPromoSerializer(individual_promo)
            return Response(
                {"status":"success","message":"Individual promo updated successfully","data":indiserializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status":"failed","message":"server error","error":str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(
        detail=False,
        methods=['PUT'],
        url_path="edit-archive",
        url_name="edit-archive",
    )
    def edit_individual_archive(self,request):
        try:
            serializer = EditIndividualArchiveSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"status":"failed","message":serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            validated_data = serializer.validated_data
            individual_promo_id = validated_data['individual_promo_id']
            new_archived = validated_data['new_archived']

            try:
                individual_promo = IndividualPromo.objects.get(pk=individual_promo_id)
            except IndividualPromo.DoesNotExist:
                return Response(
                    {"status":"failed","message":"Individual promo does not exist."},
                    status=status.HTTP_404_NOT_FOUND,
                )
            
            individual_promo.archived = new_archived
            individual_promo.save()
            serializer = IndividualPromoSerializer(individual_promo)
            return Response(
                {"status":"success","message":"Individual promo updated successfully","data":serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status":"failed","message":"server error","error":str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )