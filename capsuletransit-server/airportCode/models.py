from django.db import models
from uuid import uuid4


class AirportCode(models.Model):
    airport_code_id = models.AutoField(
        primary_key=True,
        verbose_name="AirportCodeID",
    )
    airport_code = models.CharField(
        max_length=20, unique=True, verbose_name="Airport Code"
    )
    airport_description = models.CharField(
        max_length=100, verbose_name="Airport Description"
    )

    def __str__(self):
        return f"{self.airport_code} - {self.airport_description}"

    # Define class-level choices for airport codes and descriptions
    AIRPORT_CODE_CHOICES = [
        ("KLIA", "Kuala Lumpur International Airport"),
        ("KLIA2", "Kuala Lumpur International Airport 2"),
        ("PIA", "Penang International Airport"),
        ("BKI", "Kota Kinabalu International Airport"),
        ("KIA", "Kuching International Airport"),
        ("MYY", "Miri Airport"),
        ("LIA", "Langkawi International Airport"),
    ]

    # Add choices to the model fields
    airport_code.choices = AIRPORT_CODE_CHOICES
    airport_description.choices = AIRPORT_CODE_CHOICES
