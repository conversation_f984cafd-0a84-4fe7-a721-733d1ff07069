# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AirportCode",
            fields=[
                (
                    "airport_code_id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="AirportCodeID"
                    ),
                ),
                (
                    "airport_code",
                    models.CharField(
                        choices=[
                            ("KLIA", "Kuala Lumpur International Airport"),
                            ("KLIA2", "Kuala Lumpur International Airport 2"),
                            ("PIA", "Penang International Airport"),
                            ("BKI", "Kota Kinabalu International Airport"),
                            ("KIA", "Kuching International Airport"),
                            ("MYY", "Miri Airport"),
                            ("LIA", "Langkawi International Airport"),
                        ],
                        max_length=20,
                        unique=True,
                        verbose_name="Airport Code",
                    ),
                ),
                (
                    "airport_description",
                    models.CharField(
                        choices=[
                            ("KLIA", "Kuala Lumpur International Airport"),
                            ("KLIA2", "Kuala Lumpur International Airport 2"),
                            ("PIA", "Penang International Airport"),
                            ("BKI", "Kota Kinabalu International Airport"),
                            ("KIA", "Kuching International Airport"),
                            ("MYY", "Miri Airport"),
                            ("LIA", "Langkawi International Airport"),
                        ],
                        max_length=100,
                        verbose_name="Airport Description",
                    ),
                ),
            ],
        ),
    ]
