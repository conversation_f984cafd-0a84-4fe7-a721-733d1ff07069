apiVersion: batch/v1
kind: CronJob
metadata:
  name: overstay-cron-scheaduler
spec:
  schedule: "*/5 * * * *" # Set your desired cron schedule here
  jobTemplate:
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: acr-secret-infront-sea-dev
          containers:
            - name: overstay-container-scheaduler
              image: infrontseadev.azurecr.io/capsuletransit c# Replace with the image for your cron job
              # Define the command or script for your cron job
              envFrom:
                - secretRef:
                    name: secretsenvironments
              command: ["python"]
              args : ["manage.py", "mark_overstay"]
          restartPolicy: Never
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: shift-cron-scheaduler
spec:
  schedule: "*/5 * * * *" # Set your desired cron schedule here
  jobTemplate:
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: acr-secret-infront-sea-dev
          containers:
            - name: shift-container-scheaduler
              image: infrontseadev.azurecr.io/capsuletransit # Replace with the image for your cron job
              # Define the command or script for your cron job
              envFrom:
                - secretRef:
                    name: secretsenvironments
              command: ["python"]
              args : ["manage.py", "shift_check"]
          restartPolicy: Never