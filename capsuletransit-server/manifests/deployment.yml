apiVersion: apps/v1
kind: Deployment
metadata:
  name: capsuletransit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: capsuletransit
  template:
    metadata:
      labels:
        app: capsuletransit
    spec:
      containers:
        - name: capsuletransit
          image: infrontseadev.azurecr.io/capsuletransit
          ports:
            - containerPort: 8000
          envFrom:
            - secretRef:
                name: secretsenvironments
      imagePullSecrets:
        - name: acr-secret-infront-sea-dev
