apiVersion: batch/v1
kind: CronJob
metadata:
  name: noshow-cron-scheaduler
spec:
  schedule: "*/1 * * * *" # Set your desired cron schedule here
  jobTemplate:
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: acr-secret-infront-sea-dev
          containers:
            - name: overstay-container-scheaduler
              image: infrontseadev.azurecr.io/capsuletransit:4204 # Replace with the image for your cron job
              # Define the command or script for your cron job
              command: ["python"]
              args: ["manage.py", "mark_no_show"]
              envFrom:
                - secretRef:
                    name: secretsenvironments
          restartPolicy: Never 
