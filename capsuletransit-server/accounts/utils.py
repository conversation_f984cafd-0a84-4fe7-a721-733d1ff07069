from django.utils import timezone

from accounts.models import Shift, ShiftSettings, StaffShift
from cashierterminal.models import CashierTerminal


def get_current_shift(account_id):
    try:
        shift = Shift.objects.filter(
            end_shift_datetime__isnull=True,
            staffshift__staff_id=account_id,
            status = "Open"
        ).first()
    except:
        shift = None

    return shift


def auto_start_shift(account_id, lot_id):
    
    shift_setting = ShiftSettings.objects.filter(
        start_hour__lte=timezone.now(),
        end_hour__gte=timezone.now(),
        is_archived=False,
    ).first()

    if not shift_setting:
        return None

    staff_shift = StaffShift.objects.filter(
        staff_id=account_id, shiftsettings=shift_setting, is_archived=False
    ).first()

    if not staff_shift:
        return None

    cashier_terminal = CashierTerminal.objects.filter(
        lot_id=lot_id, is_archive=False
    ).first()

    if not cashier_terminal:
        return None

    new_shift = Shift.check_in(
        staffshift_id=staff_shift.pk,
        cashier_terminal_id= cashier_terminal.pk,
        start_personal_cash_amount=0.00,
        date_time=timezone.now()
    )

    return new_shift