from rest_framework import serializers
from accounts.models import House<PERSON>eeping, Shift, StaffShift, ShiftSettings, Account
from rooms.serializers import RoomSerializer


class AccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = Account
        fields = "__all__"


class ArchiveAccountSerializer(serializers.Serializer):
    account_id = serializers.UUIDField()
    archive = serializers.BooleanField()


class ShiftSerializer(serializers.ModelSerializer):
    cashier_name = serializers.ReadOnlyField(source="cashier_terminal.cashier_terminal")
    class Meta:
        model = Shift
        fields = "__all__"


class ShiftSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShiftSettings
        fields = "__all__"


class StaffShiftSerializer(serializers.ModelSerializer):
    shiftsettings = ShiftSettingsSerializer(read_only=True)

    class Meta:
        model = StaffShift
        fields = "__all__"


class ReadStaffShiftSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffShift
        fields = "__all__"


class ReadShiftSettingsSerializer(serializers.ModelSerializer):
    staffshifts = serializers.SerializerMethodField()

    class Meta:
        model = ShiftSettings
        fields = "__all__"

    def get_staffshifts(self, obj):
        staffshifts = obj.staffshifts.filter(is_archived=False)
        serializer = ReadStaffShiftSerializer(staffshifts, many=True)
        return serializer.data


class WriteStaffShiftSerializer(serializers.Serializer):
    account_id = serializers.UUIDField()
    shiftsettings_id = serializers.UUIDField()


class WriteShiftSettingsSerializer(serializers.Serializer):
    shiftsettings_id = serializers.UUIDField(required=False)
    shift_name = serializers.CharField()
    start_hour = serializers.TimeField()
    end_hour = serializers.TimeField()


class ArchiveShiftSettingsSerializer(serializers.Serializer):
    shiftsettings_id = serializers.UUIDField()
    archive = serializers.BooleanField()


# class ShiftCheckInOutSerializer(serializers.Serializer):
#     staffshift_id = serializers.UUIDField(required=True, help_text="ShiftSetting ID")
#     timestamp = serializers.IntegerField(
#         required=True, help_text="Unix Epoch UTC Timestamp"
#     )
#     cash = serializers.DecimalField(
#         required=True, max_digits=10, decimal_places=2, help_text="Cash Amount"
#     )
#     cashier_terminal_id = serializers.IntegerField(
#         required=False, help_text="Cashier Terminal ID, Required for Checkin"
#     )


# class ShiftCheckoutSerializer(serializers.Serializer):
#     shift_id = serializers.UUIDField(required=True, help_text="Shift ID of the Staff")
#     timestamp = serializers.IntegerField(
#         required=True, help_text="Unix Epoch UTC Timestamp"
#     )
#     cash = serializers.DecimalField(
#         required=True, max_digits=10, decimal_places=2, help_text="Cash Amount"
#     )


class ShiftCheckInOutSerializer(serializers.Serializer):
    staffshift_id = serializers.UUIDField(required=True, help_text="ShiftSetting ID")
    timestamp = serializers.DateTimeField(
        required=True, help_text="Start of Shift Date Time"
    )
    cash = serializers.DecimalField(
        required=True, max_digits=10, decimal_places=2, help_text="Cash Amount"
    )
    cashier_terminal_id = serializers.IntegerField(
        required=False, help_text="Cashier Terminal ID, Required for Checkin"
    )


class ShiftCheckoutSerializer(serializers.Serializer):
    shift_id = serializers.UUIDField(required=True, help_text="Shift ID of the Staff")
    timestamp = serializers.DateTimeField(
        required=True, help_text="End of Shift Date Time"
    )
    cash = serializers.DecimalField(
        required=True, max_digits=10, decimal_places=2, help_text="Cash Amount"
    )
    overflow_shortage = serializers.FloatField(required=False, default=0)
    end_shift_remarks = serializers.CharField(required=False, default="")


class HousekeepingSerializer(serializers.ModelSerializer):
    room_code = serializers.ReadOnlyField(source="room.room_code")
    room_zone = serializers.ReadOnlyField(source="room.room_type.roomzone.zone_name")
    staff_name = serializers.ReadOnlyField(source="staff.name")

    class Meta:
        model = HouseKeeping
        fields = "__all__"


class HousekeepingTypeScore(serializers.Serializer):
    type_name = serializers.CharField()
    score = serializers.IntegerField()


class HouseKeepingStaffScoreListSerializer(serializers.Serializer):
    staff_id = serializers.UUIDField()
    staff_name = serializers.CharField()
    room_type_score = HousekeepingTypeScore(many=True)
    total = serializers.IntegerField()


class RoomTypeListSerializer(serializers.Serializer):
    room_type_name = serializers.CharField()
    room_type_color = serializers.CharField()


class HouseKeepingScoreboardSerializer(serializers.Serializer):
    room_type_list = RoomTypeListSerializer(many=True)
    staff_score_list = HouseKeepingStaffScoreListSerializer(many=True)


class ShiftCashAmountSerializer(serializers.Serializer):
    staff = AccountSerializer()
    total_cash = serializers.DecimalField(max_digits=50, decimal_places=20)


class ArchiveStaffShift(serializers.Serializer):
    shiftsettings_id = serializers.UUIDField()
    account_id = serializers.UUIDField()


class ChangeShiftCashier(serializers.Serializer):
    shift_id = serializers.UUIDField()
    new_cashier_terminal_id = serializers.IntegerField()
