# Generated by Django 4.2.3 on 2024-03-07 17:25

import accounts.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("rooms", "0001_initial"),
        ("lot", "0001_initial"),
        ("cashierterminal", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Account",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "account_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("username", models.CharField(max_length=20, unique=True)),
                ("name", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=60, null=True, unique=True
                    ),
                ),
                (
                    "is_archive",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                ("is_superuser", models.BooleanField(default=False)),
                ("is_staff", models.BooleanField(default=False)),
                (
                    "lot",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lot.lot",
                        verbose_name="lot",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            managers=[
                ("objects", accounts.models.MyAccountManager()),
            ],
        ),
        migrations.CreateModel(
            name="ShiftSettings",
            fields=[
                (
                    "shiftsettings_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("shift_name", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "start_hour",
                    models.TimeField(blank=True, default="00:00:00", null=True),
                ),
                (
                    "end_hour",
                    models.TimeField(blank=True, default="00:00:00", null=True),
                ),
                ("creation_date", models.DateTimeField(auto_now=True)),
                (
                    "is_archived",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "codename",
                    models.CharField(
                        choices=[
                            ("SUPERADMIN", "SUPERADMIN"),
                            ("BOOKINGS", "BOOKINGS"),
                            ("GUEST", "GUEST"),
                            ("ROOMS", "ROOMS"),
                            ("LOCKERS", "LOCKERS"),
                            ("SHOWER", "SHOWER"),
                            ("FINANCE", "FINANCE"),
                            ("CUSTOMERSERVICE", "CUSTOMERSERVICE"),
                            ("SUPERVISOR", "SUPERVISOR"),
                            ("HOUSEKEEPING", "HOUSEKEEPING"),
                            ("POS", "POS"),
                            ("REPORTS", "REPORTS"),
                            ("ANALYTICALREPORTS", "ANALYTICALREPORTS"),
                            ("ADMINACCESS", "ADMINACCESS"),
                            ("SYSTEMSETTINGS", "SYSTEMSETTINGS"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name="StaffShift",
            fields=[
                (
                    "staffshift_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("creation_date", models.DateTimeField(auto_now=True)),
                (
                    "is_archived",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "shiftsettings",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="staffshifts",
                        to="accounts.shiftsettings",
                    ),
                ),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Shift",
            fields=[
                (
                    "shift_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ShiftID",
                    ),
                ),
                (
                    "start_shift_datetime",
                    models.DateTimeField(
                        null=True, verbose_name="Start Shift Datetime"
                    ),
                ),
                (
                    "start_personal_cash_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        verbose_name="Start Personal Cash Amount",
                    ),
                ),
                (
                    "end_shift_datetime",
                    models.DateTimeField(null=True, verbose_name="End Shift Datetime"),
                ),
                (
                    "end_personal_cash_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="End Personal Cash Amount",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Open", "Open"),
                            ("Closed", "Closed"),
                            ("SYSTEM", "SYSTEM"),
                        ],
                        default="Open",
                        max_length=50,
                        null=True,
                        verbose_name="Shift status",
                    ),
                ),
                (
                    "cashier_terminal",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cashierterminal.cashierterminal",
                        verbose_name="CashierTerminalID",
                    ),
                ),
                (
                    "staffshift",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.staffshift",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Roles",
            fields=[
                ("role_id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "permissions",
                    models.ManyToManyField(
                        blank=True,
                        to="accounts.userpermission",
                        verbose_name="Permissions",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HouseKeeping",
            fields=[
                (
                    "housekeeping_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="HousekeepingID",
                    ),
                ),
                ("housekeeping_datetime", models.DateTimeField(auto_now=True)),
                (
                    "initial_status",
                    models.CharField(
                        help_text="original room_status when record is created",
                        max_length=100,
                    ),
                ),
                (
                    "changed_status",
                    models.CharField(
                        blank=True,
                        help_text="edited room_status",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rooms.room",
                        verbose_name="Room",
                    ),
                ),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Staff",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="account",
            name="role",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="accounts.roles",
                verbose_name="Role",
            ),
        ),
    ]
