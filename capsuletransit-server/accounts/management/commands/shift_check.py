from typing import Any, Union
from django.core import management
from accounts.cron import automatic_shift_end
import logging

logger = logging.Logger(__name__)
class Command(management.BaseCommand):
    def handle(self, *args: Any, **options: Any) -> Union[str, None]:
        automatic_shift_end()
        logger.info("Shift Checking Compeleleted")
        self.stdout.write("Shift checking compelte")
        # return super().handle(*args, **options)