import uuid

from accounts.models import HouseKeeping


def create_housekeeping_record(
    new_status : str,
    old_status : str,
    room_id : uuid.uuid4,
    staff_id : uuid.uuid4
):
    
    housekeeping_record = HouseKeeping.objects.create(
        staff_id = staff_id,
        room_id = room_id,
        initial_status = old_status,
        changed_status = new_status
    )

    return housekeeping_record