from accounts.models import UserPermission


def get_all_permissions():
    permissions = UserPermission.objects.all().exclude(
        codename__in=["SUPERADMIN", "ADMINACCESS"]
    )

    data = []
    for permission in permissions:
        data.append({
            "permissionId" : permission.id,
            "codeName" : permission.codename,
            "permissionName" : permission.name
        })
    
    return data