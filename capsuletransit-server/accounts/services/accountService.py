from accounts.models import Account, Roles
from accounts import exceptions
from django.db.models import Q

from django.contrib.auth.hashers import make_password


def get_all_accounts(user, display_archive=None):
    accounts = Account.objects.all()
    accounts = accounts.filter(lot_id=user.lot_id)
    if display_archive is None:
        accounts = accounts.filter(is_archive=False)
    data = []
    for acc in accounts:
        data.append(
            {
                "accountId": acc.account_id,
                "username": acc.username,
                "name": acc.name,
                "email": acc.email,
                "role": {"roleId": acc.role.role_id, "roleName": acc.role.name},
                "is_archive": acc.is_archive,
            }
        )
    return data


def get_account_info(account_id):
    try:
        account = Account.objects.get(pk=account_id)
    except:
        raise exceptions.AccountNotFound("Account not found")

    data = {
        "accountId": account.account_id,
        "username": account.username,
        "name": account.name,
        "email": account.email,
        "role": {"roleId": account.role.role_id, "roleName": account.role.name},
    }

    return data


def create_account(data, user):
    account_available = Account.objects.filter(
        Q(username=data["username"]) | Q(email=data["email"])
    )

    # Check if email or username already exist
    for acc in account_available:
        if acc.username == data["username"]:
            raise exceptions.UsernameUsed("username already used")
        if acc.email == data["email"] and acc.email:
            raise exceptions.EmailUsed("email already used")

    # Check if role exist
    try:
        role = Roles.objects.get(pk=data["role_id"])
    except:
        raise exceptions.RoleNotFound("role not found")

    # check asign role permission
    # if role.name == "Super Admin":
    #     raise exceptions.NoPermission("you don't have permission to assign this role")

    created_account = Account.objects.create_user(
        username=data["username"],
        name=data["name"],
        email=data["email"],
        role=role,
        password=data["password"],
        lot_id=user.lot_id,
    )

    return created_account


# For admin to update user's account details
def update_account_for_admin(data):
    admin_account = Account.objects.get(pk=data["admin_account_id"])
    try:
        account = Account.objects.get(pk=data["account_id"])
    except:
        raise exceptions.AccountNotFound("account not found")

    if admin_account.pk == account.pk:
        raise exceptions.SameAccount(
            "unable to edit your own account using edit from admin api"
        )

    # check permission if using Super Admin credentials
    if admin_account.role.name == "Super Admin" and account.role.name == "Super Admin":
        raise exceptions.NoPermission("you don't have permission to do this action")

    # check permission if using Admin credentials
    if (
        admin_account.role.name == "Admin"
        and account.role.name == "Admin"
        or admin_account.role.name == "Admin"
        and account.role.name == "Super Admin"
    ):
        raise exceptions.NoPermission("you don't have permission to do this action")

    account_available = Account.objects.filter(
        Q(username=data["username"]) | Q(email=data["email"])
    )

    # Check if email or username already exist
    for acc in account_available:
        if acc.username == data["username"] and acc.username != account.username:
            raise exceptions.UsernameUsed("username already used")
        if acc.email == data["email"] and acc.email != account.email:
            raise exceptions.EmailUsed("email already used")

    # Check if role exist
    try:
        role = Roles.objects.get(pk=data["role_id"])
    except:
        raise exceptions.RoleNotFound("role not found")

    # check asign role permission
    if role.name == "Super Admin":
        raise exceptions.NoPermission("you don't have permission to assign this role")

    account.username = data["username"]
    account.name = data["name"]
    account.email = data["email"]
    account.role = role
    edited_acc = account.save()

    return edited_acc


# For user to update its own account info
def update_account_info(data):
    account = Account.objects.get(pk=data["account_id"])

    account_available = Account.objects.filter(
        Q(username=data["username"]) | Q(email=data["email"])
    )

    # Check if email or username already exist
    for acc in account_available:
        if acc.username == data["username"] and acc.username != account.username:
            raise exceptions.UsernameUsed("username already used")

        if acc.email == "" or acc.email == None:
            continue
        if acc.email == data["email"] and acc.email != account.email and acc.email:
            raise exceptions.EmailUsed("email already used")

    # Check if role exist

    # check asign role permission
    # if role.name == "Super Admin":
    #     raise exceptions.NoPermission("you don't have permission to assign this role")

    try:
        new_password = data["password"]
    except:
        new_password = ""

    if new_password != "":
        account.password = make_password(data["password"])

    account.username = data["username"]
    account.name = data["name"]
    account.email = data["email"]

    # print(data["role_id"])

    if data.get("role_id"):
        role_id = data["role_id"]

        try:
            role = Roles.objects.get(pk=role_id)
            account.role = role
        except:
            raise exceptions.RoleNotFound("role not found")

    edited_acc = account.save()

    return edited_acc


def delete_account(validated_data, admin_account_id):
    if validated_data["account_id"] == admin_account_id:
        raise exceptions.NoPermission("you don't have permission to do this action")

    admin_account = Account.objects.get(pk=admin_account_id)
    try:
        account = Account.objects.get(pk=validated_data["account_id"])
    except:
        raise exceptions.AccountNotFound("account not found")

    # check permission if using Super Admin credentials
    # if admin_account.role.name == "Super Admin" and account.role.name == "Super Admin":
    #     raise exceptions.NoPermission("you don't have permission to do this action")

    # check permission if using Admin credentials
    # if (
    #     admin_account.role.name == "Admin"
    #     and account.role.name == "Admin"
    #     or admin_account.role.name == "Admin"
    #     and account.role.name == "Super Admin"
    # ):
    #     raise exceptions.NoPermission("you don't have permission to do this action")

    account.is_archive = validated_data["archive"]
    account.save()
    return True
