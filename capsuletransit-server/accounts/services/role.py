from accounts.models import Roles, UserPermission, Account
from accounts import exceptions


def get_all_roles():

    roles = Roles.objects.all()

    data = []
    for role in roles:
        permission_list = []
        for permission in role.get_permissions():
            permission_list.append(permission.codename)

        data.append({
            "roleId" : role.role_id,
            "roleName" : role.name,
            "rolePermissions" : permission_list
        })

    return data

def get_single_role(role_id):
    # Check if role is exist
    try:
        role = Roles.objects.get(pk = role_id)
    except:
        raise exceptions.RoleNotFound('role not found')
    
    permission_list = []
    for permission in role.get_permissions():
        permission_list.append(permission.codename)

    data = {
        "roleId" : role.role_id,
        "roleName" : role.name,
        "rolePermissions" : permission_list
    }
    return data

def create_role(data):
    
    role_available = Roles.objects.filter(name = data['role_name'])
    if len(role_available) > 0:    
        raise exceptions.RoleExist('role already exist')
    
    permissions = UserPermission.objects.filter(codename__in=data['role_permission'])
    
    if len(permissions) == 0:
        raise exceptions.PermissionNotFound('no permission found')
    
    if "SUPERADMIN" in data['role_permission'] or "ADMINACCESS" in data['role_permission']:
        raise exceptions.PermissionForbidden('unable to add selected permission')
    
    created_role = Roles.objects.create(
        name = data['role_name']
    )
    created_role.permissions.set(permissions)

    return created_role

def update_role(data):

    # Check if role is exist
    try:
        role = Roles.objects.get(pk = data['role_id'])
    except:
        raise exceptions.RoleNotFound('role not found')
    
    # Check if the selected role is Super Admin or Admin, then prevent to update
    if role.name == "Super Admin" or role.name == "Admin":
        raise exceptions.NoPermission('unable to update the selected role')
    
    # Check if the role name already used
    role_available = Roles.objects.filter(name = data['role_new_name'])
    if len(role_available) > 0 and role_available.first().role_id is not int(data['role_id']):    
        raise exceptions.RoleExist('role already exist')
    
    # Check if selected permission is include SUPERADMIN or ADMINACCESS
    if "SUPERADMIN" in data['role_permission'] or "ADMINACCESS" in data['role_permission']:
        raise exceptions.PermissionForbidden('unable to add selected permission')
    
    new_permission = UserPermission.objects.filter(codename__in=data['role_permission'])
    role.name = data['role_new_name']
    role.permissions.clear()
    role.permissions.set(new_permission)
    role.save()

    return role

def delete_role(role_id):
    
    # Check if role is exist
    try:
        role = Roles.objects.get(pk = role_id)
    except:
        raise exceptions.RoleNotFound('role not found')
    
    # Check if the selected role is Super Admin or Admin, then prevent to delete
    if role.name == "Super Admin" or role.name == "Admin":
        raise exceptions.NoPermission('no permission')
    
    # Check if the selected role still have active accounts used the role
    accounts_use_role = Account.objects.filter(role_id = role_id)
    if len(accounts_use_role) > 0:
        raise exceptions.RoleHaveActiveAccounts('role still have active accounts')
    

    role.delete()
    return True