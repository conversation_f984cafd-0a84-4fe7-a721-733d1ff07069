from django.urls import path
from . import views

from rest_framework.routers import DefaultRouter

# Route
from .views import Accounts, Role, Permissions, ShiftSettingsListView, ShiftViewset, HousekeepingViewset, ShiftSettingsViewSet, StaffShiftViewSet

router = DefaultRouter()
router.register('', Accounts, basename='accounts')
router.register('role', Role, basename='role')
router.register('permission', Permissions, basename='permission')
router.register('housekeeping', HousekeepingViewset, basename="housekeeping")
router.register('shift-settings', ShiftSettingsViewSet, basename="shift-settings")
router.register('staff-shift', StaffShiftViewSet, basename="staff-shift")

router.register('shift', ShiftViewset, basename='shift')


urlpatterns = router.urls + [
    path('shift/list/', ShiftSettingsListView.as_view(), name='shift-list'),
]
    
