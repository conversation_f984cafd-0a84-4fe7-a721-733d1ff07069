import datetime
import uuid

from airportCode.models import AirportCode
from django.contrib.auth.models import AbstractBaseU<PERSON>, BaseUserManager, Group
from django.db import models
from django.db.models import Q, UniqueConstraint
from django.utils import timezone
from lot.models import Lot
from rooms.models import Room

USER_PERMISSION = (
    ("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SUPERADMIN"),
    ("BOOKINGS", "BOOKINGS"),
    ("GUEST", "GUEST"),
    ("ROOMS", "ROOMS"),
    ("LOCKERS", "LOCKERS"),
    ("SHOWER", "SHOWER"),
    ("FINANCE", "FINANCE"),
    ("CUSTOMERSERVICE", "C<PERSON><PERSON>MERSERVICE"),
    ("SUPERVISOR", "SUPERVISOR"),
    ("HOUSEKEEPING", "HOUSEKEEPING"),
    ("POS", "POS"),
    ("REPOR<PERSON>", "REPORTS"),
    ("ANALY<PERSON><PERSON><PERSON>EPOR<PERSON>", "ANALYTICALREPORTS"),
    ("ADMINACCESS", "ADMINACCESS"),
    ("SYSTEMSETTINGS", "SYSTEMSETTINGS"),
)


class MyAccountManager(BaseUserManager):
    use_in_migrations = True

    def create_user(self, username, email, role, name, password, lot_id):
        user = self.model(
            username=username,
            name=name,
            email=self.normalize_email(email),
            role_id=role.pk,
            password=password,
            lot_id=lot_id,
        )

        user.set_password(password)
        user.save(using=self._db)

        return user

    def create_superuser(self, username, email, name, password):
        if not email:
            raise ValueError("Email is required")

        superadmin, _ = Roles.objects.get_or_create(
            name="Super Admin", defaults={"name": "Super Admin"}
        )
        admin, _ = Roles.objects.get_or_create(name="Admin", defaults={"name": "Admin"})
        default_airport, _ = AirportCode.objects.get_or_create(
            airport_code="Default",
            defaults={
                "airport_code": "Default",
                "airport_description": "Default Airport",
            },
        )
        default_lot, _ = Lot.objects.get_or_create(
            lot_number="Default",
            defaults={
                "lot_number": "Default",
                "lot_description": "Default Lot",
                "airport_code_id_id": default_airport.pk,
            },
        )

        permissions = UserPermission.objects.all()
        superadmin.permissions.set(permissions)
        admin.permissions.set(permissions.exclude(codename="SUPERADMIN"))

        user = self.model(
            username=username,
            name=name,
            email=self.normalize_email(email),
            role_id=superadmin.pk,
            password=password,
            lot_id=default_lot.pk,
        )

        user.is_staff = True
        user.is_superuser = True

        user.set_password(password)
        user.save(using=self._db)

        return user


class UserPermission(models.Model):
    codename = models.CharField(max_length=50, unique=True, choices=USER_PERMISSION)
    name = models.CharField(max_length=50)

    def __str__(self):
        return self.name


class Roles(models.Model):
    role_id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    permissions = models.ManyToManyField(
        UserPermission,
        verbose_name=("Permissions"),
        blank=True,
    )

    def __str__(self):
        return self.name

    def get_permissions(self):
        if self.permissions:
            return self.permissions.all()
        else:
            return []


class Account(AbstractBaseUser):
    account_id = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False, unique=True
    )
    username = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    email = models.EmailField(max_length=60, blank=True, null=True)
    role = models.ForeignKey(
        Roles, on_delete=models.CASCADE, verbose_name=("Role"), blank=True, null=True
    )
    lot = models.ForeignKey(
        Lot, on_delete=models.CASCADE, verbose_name=("lot"), default=1
    )
    is_archive = models.BooleanField(default=False, null=True, blank=True)

    is_superuser = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)

    USERNAME_FIELD = "username"
    REQUIRED_FIELDS = ["email", "name"]

    objects = MyAccountManager()

    def get_shift(self):
        shift = Shift.objects.filter(
            staffshift__staff=self, end_shift_datetime__isnull=True
        ).first()
        return shift

    def __str__(self):
        return self.username

    def get_user_permissions(self):
        if self.role:
            return self.role.permissions.all()
        else:
            return []


class ShiftSettings(models.Model):
    shiftsettings_id = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False, unique=True
    )
    shift_name = models.CharField(max_length=20, null=True, blank=True)
    start_hour = models.TimeField(default="00:00:00", null=True, blank=True)
    end_hour = models.TimeField(default="00:00:00", null=True, blank=True)
    creation_date = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False, null=True, blank=True)


class StaffShift(models.Model):
    staffshift_id = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False, unique=True
    )
    staff = models.ForeignKey(Account, on_delete=models.CASCADE)
    shiftsettings = models.ForeignKey(
        ShiftSettings, on_delete=models.CASCADE, related_name="staffshifts"
    )
    creation_date = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False, null=True, blank=True)


SHIFT_STATUS = (("Open", "Open"), ("Closed", "Closed"), ("SYSTEM", "SYSTEM"))


class SHIFTSTATUS:
    OPEN = "Open"
    CLOSED = "Closed"
    SYSTEM = "SYSTEM"
    choices = ((OPEN, "Open"), (CLOSED, "Closed"), (SYSTEM, "SYSTEM"))


class Shift(models.Model):
    shift_id = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False, verbose_name="ShiftID"
    )
    staffshift = models.ForeignKey(
        StaffShift, on_delete=models.CASCADE, null=True, blank=True
    )
    cashier_terminal = models.ForeignKey(
        "cashierterminal.CashierTerminal",
        on_delete=models.CASCADE,
        verbose_name="CashierTerminalID",
    )
    start_shift_datetime = models.DateTimeField(
        null=True, verbose_name="Start Shift Datetime"
    )
    start_personal_cash_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="Start Personal Cash Amount"
    )
    end_shift_datetime = models.DateTimeField(
        null=True, verbose_name="End Shift Datetime"
    )
    end_personal_cash_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        verbose_name="End Personal Cash Amount",
    )
    status = models.CharField(
        choices=SHIFTSTATUS.choices,
        max_length=50,
        default="Open",
        null=True,
        verbose_name="Shift status",
    )
    overflow_shortage = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        verbose_name="Overflow Shortage Amount",
    )
    end_shift_remarks = models.CharField(null=True, max_length=300)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=["staffshift"],
                name="unique_open_staffshift",
                condition=Q(end_shift_datetime=None),
            )
        ]

    # The Function for this check in uses EPOCH to calculate the start time of the shift
    # @classmethod
    # def check_in(cls, staffshift_id : uuid.UUID, cashier_terminal_id : int, start_personal_cash_amount : float, timestamp : int):
    #     """
    #     Perform Shift Checkin for Staff
    #     @param staffshift_id: StaffShift ID
    #     @param cashier_terminal: Cashier Terminal ID
    #     @param start_personal_cash_amount: Start Personal Cash Amount
    #     @param timestamp: Unix Epoch UTC Timestamp
    #     """

    #     if Shift.objects.filter(staffshift__staffshift_id=staffshift_id, end_shift_datetime__isnull=True).exists():
    #         raise Exception("Staff already checked in")
    #     timestamp = datetime.datetime.fromtimestamp(timestamp)
    #     timestamp = timezone.make_aware(timestamp, timezone.get_default_timezone())
    #     shift = cls.objects.create(
    #         staffshift_id=staffshift_id,
    #         cashier_terminal_id=cashier_terminal_id,
    #         start_personal_cash_amount=int(start_personal_cash_amount),
    #         start_shift_datetime=timestamp
    #     )
    #     return shift

    # This Check out function uses EPOCH to calculate the end date time of the shift
    # def checkout(self, end_personal_cash_amount: float, timestamp: int):
    #     """
    #     Perform Shift Checkout for Staff
    #     @param end_personal_cash_amount: End Personal Cash Amount
    #     @param timestamp: Unix Epoch UTC Timestamp
    #     """
    #     if self.end_shift_datetime:
    #         raise Exception("Staff already checked out")
    #     timestamp = datetime.datetime.fromtimestamp(timestamp)
    #     timestamp = timezone.make_aware(timestamp, timezone.get_default_timezone())
    #     self.end_shift_datetime = timestamp
    #     self.end_personal_cash_amount = end_personal_cash_amount
    #     self.status = SHIFTSTATUS.CLOSED
    #     self.save()
    #     return self

    @classmethod
    def check_in(
        cls,
        staffshift_id: uuid.UUID,
        cashier_terminal_id: int,
        start_personal_cash_amount: float,
        date_time: datetime,
    ):
        """
        Perform Shift Checkin for Staff
        @param staffshift_id: StaffShift ID
        @param cashier_terminal: Cashier Terminal ID
        @param start_personal_cash_amount: Start Personal Cash Amount
        @param date_time: Date Time format
        """

        if Shift.objects.filter(
            staffshift__staffshift_id=staffshift_id, end_shift_datetime__isnull=True
        ).exists():
            raise Exception("Staff already checked in")
        shift = cls.objects.create(
            staffshift_id=staffshift_id,
            cashier_terminal_id=cashier_terminal_id,
            start_personal_cash_amount=int(start_personal_cash_amount),
            start_shift_datetime=date_time,
        )
        return shift

    def checkout(
        self,
        end_personal_cash_amount: float,
        date_time: datetime,
        overflow_shortage: float,
        remarks: str,
    ):
        """
        Perform Shift Checkout for Staff
        @param end_personal_cash_amount: End Personal Cash Amount
        @param date_time: Date Time of the end shift
        """
        if self.end_shift_datetime:
            raise Exception("Staff already checked out")
        self.end_shift_datetime = date_time
        self.end_personal_cash_amount = end_personal_cash_amount
        self.overflow_shortage = overflow_shortage
        self.end_shift_remarks = remarks
        self.status = SHIFTSTATUS.CLOSED
        self.save()
        return self

    def end_by_system(self):
        self.end_shift_datetime = timezone.now()
        self.status = SHIFTSTATUS.SYSTEM
        self.save()
        return self


class HouseKeeping(models.Model):
    housekeeping_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name="HousekeepingID",
    )
    housekeeping_datetime = models.DateTimeField(auto_now=True)
    staff = models.ForeignKey(Account, verbose_name="Staff", on_delete=models.CASCADE)
    room = models.ForeignKey(Room, verbose_name="Room", on_delete=models.CASCADE)
    initial_status = models.CharField(
        max_length=100, help_text="original room_status when record is created"
    )
    changed_status = models.CharField(
        max_length=100, help_text="edited room_status", null=True, blank=True
    )
