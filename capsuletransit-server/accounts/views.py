import datetime
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.decorators import action
from django.contrib.auth import authenticate
from accounts import exceptions
from rest_framework import generics
from .models import Account, HouseKeeping, ShiftSettings, Shift, StaffShift
from django.utils import timezone
from .serializers import (
    AccountSerializer,
    ArchiveAccountSerializer,
    ArchiveShiftSettingsSerializer,
    ArchiveStaffShift,
    ChangeShiftCashier,
    HouseKeepingScoreboardSerializer,
    HousekeepingSerializer,
    ReadShiftSettingsSerializer,
    ShiftCashAmountSerializer,
    ShiftCheckInOutSerializer,
    ShiftCheckoutSerializer,
    ShiftSettingsSerializer,
    ShiftSerializer,
    StaffShiftSerializer,
    WriteShiftSettingsSerializer,
    WriteStaffShiftSerializer,
)
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from djangorestframework_camel_case.parser import CamelCaseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON>ase<PERSON><PERSON><PERSON>enderer

# Permissions
from rest_framework.permissions import IsAdminUser
from accounts import permissions

# services
from accounts.services.role import (
    get_all_roles,
    get_single_role,
    create_role,
    update_role,
    delete_role,
)
from accounts.services.permission import get_all_permissions
from accounts.services.accountService import (
    get_all_accounts,
    get_account_info,
    create_account,
    update_account_for_admin,
    update_account_info,
    delete_account,
)

# Authentication
from django.contrib.auth.hashers import check_password
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle

from rooms.models import RoomType
from django.db import models

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from transaction.services.transaction import get_staff_inhand_cash

from cashierterminal.models import CashierTerminal


class Accounts(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-accounts",
        url_name="get-all-accounts",
        permission_classes=[],
    )
    def get_all_accounts(self, request):
        display_archive = request.query_params.get("display_archive")
        try:
            data = get_all_accounts(user=request.user, display_archive=display_archive)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-single-account",
        url_name="get-single-account",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def get_single_account(self, request):
        account_id = request.query_params.get("accountId")
        try:
            data = get_account_info(account_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.AccountNotFound as e:
            return Response(
                {"status": "failed", "msg": "Account not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-account-info",
        url_name="get-account-info",
        permission_classes=[],
    )
    def get_account_info(self, request):
        account_id = request.user.account_id
        try:
            data = get_account_info(account_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.AccountNotFound as e:
            return Response(
                {"status": "failed", "msg": "Account not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-account",
        url_name="create-account",
        permission_classes=[],
    )
    def create_account(self, request):
        # req_data = {key: value for key, value in request.POST.items()}

        try:
            created_account = create_account(request.data, request.user)
            return Response(
                {
                    "status": "success",
                    "msg": "Account created",
                    "id": created_account.account_id,
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.UsernameUsed as e:
            return Response(
                {"status": "failed", "msg": "Username already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.EmailUsed as e:
            return Response(
                {"status": "failed", "msg": "Email already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoleNotFound as e:
            return Response(
                {"status": "failed", "msg": "Role not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.NoPermission as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "You don't have permission to assign this role",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-account",
        url_name="edit-account",
        permission_classes=[],
    )
    def edit_account(self, request):
        # req_data = {key: value for key, value in request.POST.items()}
        # req_data["account_id"] = request.user.account_id

        try:
            update_account_info(request.data)
            return Response(
                {"status": "success", "msg": "Account info updated"},
                status=status.HTTP_200_OK,
            )

        except exceptions.UsernameUsed as e:
            return Response(
                {"status": "failed", "msg": "Username already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.EmailUsed as e:
            return Response(
                {"status": "failed", "msg": "Email already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-account-admin",
        url_name="edit-account-admin",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def edit_account_admin(self, request):
        req_data = {key: value for key, value in request.POST.items()}
        req_data["admin_account_id"] = request.user.account_id

        try:
            update_account_for_admin(req_data)
            return Response(
                {"status": "success", "msg": "Account info updated"},
                status=status.HTTP_200_OK,
            )

        except exceptions.UsernameUsed as e:
            return Response(
                {"status": "failed", "msg": "Username already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.EmailUsed as e:
            return Response(
                {"status": "failed", "msg": "Email already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.NoPermission as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "You don't have permission to do this action",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.SameAccount as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "Unable to edit your own account using edit from admin api",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.AccountNotFound as e:
            return Response(
                {"status": "failed", "msg": "Account not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.RoleNotFound as e:
            return Response(
                {"status": "failed", "msg": "Role not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PATCH"],
        detail=False,
        url_path="archive-account",
        url_name="archive-account",
        permission_classes=[],
    )
    def delete_account(self, request):
        serializer = ArchiveAccountSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data
            admin_account_id = request.user.account_id

            delete_account(validated_data, admin_account_id)
            return Response(
                {"status": "success", "msg": "Delete account"},
                status=status.HTTP_200_OK,
            )

        except exceptions.AccountNotFound as e:
            return Response(
                {"status": "failed", "msg": "Account not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.NoPermission as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "You don't have permission to do this action",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    throttle_classes = [UserRateThrottle, AnonRateThrottle]

    @action(
        methods=["POST"],
        detail=False,
        url_path="check-admin-password",
        url_name="check-admin-password",
        permission_classes=[],
    )
    def check_admin_password(self, request):
        admin_name = request.data.get("admin_name")
        admin_password = request.data.get("admin_password")

        admin_user = authenticate(username=admin_name, password=admin_password)
        if admin_user.role and admin_user.role.name in ["Super Admin", "Admin"]:
            if not check_password(admin_password, admin_user.password):
                return Response(
                    {"status": "failed", "msg": "Invalid admin password"},
                    status=status.HTTP_403_FORBIDDEN,
                )

        else:
            return Response(
                {"status": "failed", "msg": "User does not have admin privileges"},
                status=status.HTTP_403_FORBIDDEN,
            )

        return Response(
            {
                "status": "success",
                "msg": "success",
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-action",
        url_name="verify-action",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def verify_action(self, request):
        username = request.data.get("username")
        password = request.data.get("password")

        user = authenticate(username=username, password=password)
        # if user.account_id == request.user.account_id:
        #     return Response(
        #         {
        #             "status": "failed",
        #             "msg": "Please consult with another Manager/Supervisor to perform action",
        #         },
        #         status=status.HTTP_403_FORBIDDEN,
        #     )

        if not user or user.is_archive:
            return Response(
                {
                    "status": "failed",
                    "msg": "No active account found with given credentials",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if user.role and user.role.name in [
            "Super Admin",
            "Manager",
            "Supervisor",
        ]:
            if not check_password(password, user.password):
                return Response(
                    {"status": "failed", "msg": "Invalid admin password"},
                    status=status.HTTP_403_FORBIDDEN,
                )

        else:
            return Response(
                {"status": "failed", "msg": "User does not have admin privileges"},
                status=status.HTTP_403_FORBIDDEN,
            )

        return Response(
            {
                "status": "success",
                "msg": "success",
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(responses={"200": AccountSerializer, "500": "server error"})
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-housekeeper-list",
        url_name="get-housekeeper-list",
        permission_classes=[],
    )
    def get_all_housekeeper(self, request):
        try:
            housekeeper_list = Account.objects.filter(
                role__name__in=["Super Admin", "HK"]
            )

            serializer = AccountSerializer(housekeeper_list, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Roles
class Role(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-roles",
        url_name="get-all-roles",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def get_all_roles(self, request):
        try:
            data = get_all_roles()
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-single-role",
        url_name="get-single-role",
        permission_classes=[],
    )
    def get_single_roles(self, request):
        role_id = request.query_params.get("roleId")
        try:
            data = get_single_role(role_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.RoleNotFound as e:
            return Response(
                {"status": "failed", "msg": "Role not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-role",
        url_name="create-role",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def create_role(self, request):
        req_data = request.data

        try:
            created_role = create_role(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "role created",
                    "id": created_role.role_id,
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoleExist as e:
            return Response(
                {"status": "failed", "msg": "Role name already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.PermissionForbidden as e:
            return Response(
                {"status": "failed", "msg": "Unable to add selected permission"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.PermissionNotFound as e:
            return Response(
                {"status": "failed", "msg": "no permission found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="update-role",
        url_name="update-role",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def update_role(self, request):
        req_data = request.data

        try:
            update_role(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "role updated",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoleExist as e:
            return Response(
                {"status": "failed", "msg": "Role name already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.NoPermission as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "You don't have permission to update the selected role",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.PermissionForbidden as e:
            return Response(
                {"status": "failed", "msg": "Unable to add selected permission"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoleNotFound as e:
            return Response(
                {"status": "failed", "msg": "Role not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.PermissionNotFound as e:
            return Response(
                {"status": "failed", "msg": "no permission found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="delete-role",
        url_name="delete-role",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def delete_role(self, request):
        role_id = request.query_params.get("roleId")
        try:
            delete_role(role_id)
            return Response(
                {
                    "status": "success",
                    "msg": "role deleted",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoleNotFound as e:
            return Response(
                {"status": "failed", "msg": "Role not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.NoPermission as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "You don't have permission to delete the selected role",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoleHaveActiveAccounts as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "The Role you trying to remove still have active accounts using",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Permissions
class Permissions(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-permissions",
        url_name="get-all-permissions",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def get_all_permissions(self, request):
        try:
            data = get_all_permissions()
            return Response(
                {"status": "success", "msg": "role deleted", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ShiftSettingsListView(generics.ListAPIView):
    permission_classes = []
    authentication_classes = []
    queryset = StaffShift.objects.all()
    serializer_class = StaffShiftSerializer

    @swagger_auto_schema(
        tags=["Shift"],
        manual_parameters=[
            openapi.Parameter(
                "account_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="filter for account id",
            ),
        ],
        responses={
            "200": StaffShiftSerializer(many=True),
        },
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        account_id = request.GET.get("account_id", None)
        qs = self.get_queryset()
        if account_id:
            qs = qs.filter(staff_id=account_id)
        serializer = self.serializer_class(qs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ShiftSettingsViewSet(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = ShiftSettings.objects.all()
        return qs

    @swagger_auto_schema(
        responses={"200": ShiftSettingsSerializer, "500": "server error"}
    )
    def list(self, request):
        try:
            qs = self.get_queryset()

            serializer = ShiftSettingsSerializer(qs, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={"200": ReadShiftSettingsSerializer, "500": "server error"}
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get/(?P<shiftsettings_id>[a-zA-Z0-9-]+)",
        url_name="get-shift-setting",
    )
    def get_shift_settings(self, request, shiftsettings_id):
        try:
            qs = self.get_queryset().get(pk=shiftsettings_id)

            serializer = ReadShiftSettingsSerializer(qs)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteShiftSettingsSerializer,
        responses={
            200: ReadShiftSettingsSerializer,
            400: "bad request",
            403: "shift already exist",
            500: "server error",
        },
    )
    def create(self, request):
        serializer = WriteShiftSettingsSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            shift_settings = ShiftSettings.objects.filter(is_archived=False)
            for setting in shift_settings:
                if validated_data["shift_name"].upper() == setting.shift_name:
                    return Response(
                        {"status": "failed", "message": "shift name already exist"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

            new_shift_settings = ShiftSettings.objects.create(
                shift_name=validated_data["shift_name"].upper(),
                start_hour=validated_data["start_hour"],
                end_hour=validated_data["end_hour"],
            )

            return_serializer = ReadShiftSettingsSerializer(new_shift_settings)

            return Response(
                {
                    "status": "success",
                    "message": "shift settings created",
                    "data": return_serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteShiftSettingsSerializer,
        responses={
            200: ReadShiftSettingsSerializer,
            400: "bad request",
            403: "shift already exist",
            500: "server error",
        },
    )
    @action(
        detail=False, methods=["PUT"], url_name="edit-shiftsettings", url_path="edit"
    )
    def edit_shift_settings(self, request):
        serializer = WriteShiftSettingsSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                shiftsetting = ShiftSettings.objects.get(
                    pk=validated_data["shiftsettings_id"]
                )
            except:
                return Response(
                    {"status": "failed", "message": "shift settings not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            shift_settings = ShiftSettings.objects.filter(is_archived=False)
            for setting in shift_settings:
                if (
                    validated_data["shift_name"].upper() == setting.shift_name
                    and setting.shiftsettings_id != shiftsetting.pk
                ):
                    return Response(
                        {"status": "failed", "message": "shift name already exist"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

            shiftsetting.shift_name = validated_data["shift_name"].upper()
            shiftsetting.start_hour = validated_data["start_hour"]
            shiftsetting.end_hour = validated_data["end_hour"]
            shiftsetting.save()

            return_serializer = ReadShiftSettingsSerializer(shiftsetting)
            return Response(
                {
                    "status": "success",
                    "message": "shift settings edited",
                    "data": return_serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=ArchiveShiftSettingsSerializer,
        responses={
            200: "success",
            400: "bad request",
            403: "shift already exist",
            500: "server error",
        },
    )
    @action(
        detail=False,
        methods=["PATCH"],
        url_name="archive-shiftsettings",
        url_path="archive",
    )
    def archive_shift_settings(self, request):
        serializer = ArchiveShiftSettingsSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                shiftsetting = ShiftSettings.objects.get(
                    pk=validated_data["shiftsettings_id"]
                )
            except:
                return Response(
                    {"status": "failed", "message": "shift settings not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            shiftsetting.is_archived = validated_data["archive"]
            shiftsetting.save()

            return Response(
                {
                    "status": "success",
                    "message": f"shift settings {'archived' if validated_data['archive'] else 'unarchived'}",
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class StaffShiftViewSet(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    @swagger_auto_schema(
        request_body=WriteStaffShiftSerializer,
        responses={"200": "created", "400": "bad request", "500": "server error"},
    )
    def create(self, request):
        serializer = WriteStaffShiftSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            StaffShift.objects.create(
                staff_id=validated_data["account_id"],
                shiftsettings_id=validated_data["shiftsettings_id"],
            )

            return Response(
                {"status": "success", "message": "created"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PATCH"],
        detail=False,
        url_path="archive",
        url_name="archive",
    )
    def archive_staffshift(self, request):
        serializer = ArchiveStaffShift(data=request.data)
        try:

            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                staffshift = StaffShift.objects.get(
                    staff_id=validated_data["account_id"],
                    shiftsettings_id=validated_data["shiftsettings_id"],
                    is_archived=False,
                )
            except:
                return Response(
                    {"status": "failed", "message": "not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            staffshift.is_archived = True
            staffshift.save()

            return Response(
                {"status": "success", "message": "archived"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ShiftViewset(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []

    @swagger_auto_schema(responses={"200": ShiftSerializer, "500": "server error"})
    def list(self, request):
        try:
            qs = Shift.objects.all()
            serializer = ShiftSerializer(qs, many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="check-in-shift",
        url_name="check-in-shift",
    )
    @swagger_auto_schema(
        tags=["Shift"],
        request_body=ShiftCheckInOutSerializer,
        responses={"200": ShiftSerializer, "403": "You are already in shift"},
    )
    def check_in_shift(self, request):
        # account_id = request.user.account_id

        # shift = request.user.get_shift()
        serializer = ShiftCheckInOutSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # if shift:
        #     return Response({
        #         'status' : 'failed',
        #         'msg' : 'You are already in shift',
        #         'data' : ShiftSerializer(shift).data
        #     }, status=status.HTTP_403_FORBIDDEN)

        shift = Shift.check_in(
            staffshift_id=serializer.validated_data["staffshift_id"],
            cashier_terminal_id=serializer.validated_data["cashier_terminal_id"],
            start_personal_cash_amount=serializer.validated_data["cash"],
            date_time=serializer.validated_data["timestamp"],
        )
        return Response(
            {
                "status": "success",
                "msg": "Shift checked in",
                "data": ShiftSerializer(shift).data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        request_body=ShiftCheckoutSerializer,
        responses={"200": ShiftSerializer, "404": "Shift not found"},
        tags=["Shift"],
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="check-out-shift",
        url_name="check-out-shift",
    )
    def check_out_shift(self, request):
        serializer = ShiftCheckoutSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        shift_id = serializer.validated_data["shift_id"]
        date_time = serializer.validated_data["timestamp"]
        cash = serializer.validated_data["cash"]
        overflow_shortage = serializer.validated_data["overflow_shortage"]
        remarks = serializer.validated_data["end_shift_remarks"]
        s = Shift.objects.get(shift_id=shift_id)
        s.checkout(cash, date_time, overflow_shortage, remarks)
        return Response(
            {
                "status": "success",
                "msg": "Shift checked out",
                "data": ShiftSerializer(s).data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        tags=["Shift"], responses={"200": ShiftSerializer, "404": "Shift not found"}
    )
    @action(
        methods=["GET"],
        detail=True,
        url_path="current-shift",
        url_name="get-current-shift",
    )
    def get_current_shift(self, request, pk):
        shift = (
            Shift.objects.filter(staffshift__staff_id=pk)
            .filter(end_shift_datetime__isnull=True)
            .first()
        )
        if not shift:
            return Response(
                {"status": "failed", "msg": "Shift not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        return Response(
            {
                "status": "success",
                "msg": "Shift found",
                "data": ShiftSerializer(shift).data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        tags=["Shift"],
        responses={"200": StaffShiftSerializer, "404": "Shift not found"},
    )
    @action(
        methods=["GET"],
        detail=True,
        url_path="get-shift-info-details",
        url_name="get-shift-info-details",
    )
    def get_account_shift_details(self, request, pk):
        try:
            qs = StaffShift.objects.prefetch_related("shiftsettings").filter(
                staff_id=pk
            )

            if not qs:
                return Response(
                    {"status": "failed", "msg": "not found"}, status=status.HTTP_200_OK
                )

            serializer = StaffShiftSerializer(qs, many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="cash-amount",
        url_name="cash-amount",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def shift_cash_amount(self, request):
        try:

            shift_list = Shift.objects.filter(
                status="Open", staffshift__staff__lot_id=request.user.lot_id
            )

            data = []
            for shift in shift_list:
                staff = shift.staffshift.staff
                total_cash = get_staff_inhand_cash(shift.pk)

                data.append({"staff": staff, "total_cash": total_cash})

            serializer = ShiftCashAmountSerializer(data, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    @action(
        methods=["PUT"],
        detail=False,
        url_path="change-cashier",
        url_name="change-cashier",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def change_shift_cashier(self, request):
        serializer = ChangeShiftCashier(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status" : "failed",
                    "message" : serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data


            try:
                shift_obj = Shift.objects.get(pk = validated_data['shift_id'])
            except:
                return Response({
                    "status" : "failed",
                    "message" : "shift not found"
                }, status=status.HTTP_204_NO_CONTENT)
            
            cashier_terminal = CashierTerminal.objects.filter(pk = validated_data['new_cashier_terminal_id']).first()
            
            if not cashier_terminal:
                return Response({
                    "status" : "failed",
                    "message" : "Cashier terminal not found"
                }, status=status.HTTP_204_NO_CONTENT)
            
            shift_obj.cashier_terminal = cashier_terminal
            shift_obj.save()

            return Response({
                "status" : "success",
                "message" : "cashier changed",
                "data" : {
                    "cashier_name" : cashier_terminal.cashier_terminal
                },
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class HousekeepingViewset(viewsets.ViewSet):
    queryset = HouseKeeping.objects.all()
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = HousekeepingSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = self.queryset.prefetch_related(
            "room", "staff", "room__room_type__roomzone"
        )

        return qs

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "zoneId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter by its zone (multiple add ':' in between. example = <zone id>:<zone id>)",
            ),
            openapi.Parameter(
                "roomStatus",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="filter by record inital status (multiple add ':' in between. example = <room status>:<room status>)",
            ),
            openapi.Parameter(
                "housekeeperId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="filter by housekeeper (staff) id (multiple add ':' in between. example = <staff id>:<staff id>)",
            ),
        ],
        responses={"200": HousekeepingSerializer, "500": "server error"},
    )
    def list(self, request):
        qs = self.get_queryset().order_by("-housekeeping_datetime")
        qs = qs.filter(staff__lot_id=request.user.lot_id)

        zone_id = request.GET.get("zoneId", None)
        room_status = request.GET.get("roomStatus", None)
        housekeeper_id = request.GET.get("housekeeperId", None)

        try:
            if zone_id:
                splited_zone_id = zone_id.split(":")
                qs = qs.filter(room__room_type__roomzone_id__in=splited_zone_id)

            if room_status:
                splited_room_status = room_status.split(":")
                qs = qs.filter(initial_status__in=splited_room_status)

            if housekeeper_id:
                splited_housekeeper_id = housekeeper_id.split(":")
                qs = qs.filter(staff_id__in=splited_housekeeper_id)

            serializer = HousekeepingSerializer(qs, many=True)

            return Response({"status": "success", "data": serializer.data})

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    @swagger_auto_schema(
        responses={"200": HouseKeepingScoreboardSerializer, "500": "server error"},
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="scoreboard",
        url_name="scoreboard",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def housekeeping_scoreboard(self, request):
        qs = self.get_queryset()
        qs = qs.filter(staff__lot_id=request.user.lot_id)

        """
        for optimization

        SELECT aa."name", rr2."type_name", COUNT(ah.room_id) AS "score"
        FROM accounts_account aa
        CROSS JOIN rooms_roomtype rr2
        LEFT JOIN rooms_room rr ON rr.room_type_id = rr2.type_id
        LEFT JOIN accounts_housekeeping ah ON ah.staff_id = aa.account_id AND ah.room_id = rr.room_id
        GROUP BY aa."name", rr2."type_name"
        """

        try:
            room_type = RoomType.objects.filter(
                archived=False, roomzone__lot_id_id=request.user.lot_id
            )
            available_type = []
            for item in room_type:
                is_exist = False
                for type in available_type:
                    if type["room_type_name"] == item.type_name:
                        is_exist = True
                if not is_exist:
                    available_type.append(
                        {
                            "room_type_name": item.type_name,
                            "room_type_color": item.color_tags,
                        }
                    )

            scoreboard_data = {}
            for data in qs:
                staff_id = data.staff_id
                staff_name = data.staff.name
                room_type_score = []
                for type_count in available_type:
                    room_type_score.append(
                        {"type_name": type_count["room_type_name"], "score": 0}
                    )
                if staff_id not in scoreboard_data:
                    scoreboard_data[staff_id] = {
                        "staff_id": staff_id,
                        "staff_name": staff_name,
                        "housekeeping_record": [],
                        "room_type_score": room_type_score,
                    }
                scoreboard_data[staff_id]["housekeeping_record"].append(data)

            housekeeping_grouped = list(scoreboard_data.values())

            for staff_data in housekeeping_grouped:
                for record in staff_data["housekeeping_record"]:
                    for item in staff_data["room_type_score"]:
                        if item["type_name"] == record.room.room_type.type_name:
                            item["score"] += 1
                staff_data["total"] = len(staff_data["housekeeping_record"])

            scoreboard_data = {
                "room_type_list": available_type,
                "staff_score_list": housekeeping_grouped,
            }

            serializer = HouseKeepingScoreboardSerializer(scoreboard_data)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )
