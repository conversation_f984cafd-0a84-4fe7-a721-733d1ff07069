from rest_framework.permissions import BasePermission
from accounts.models import Account



class BookingsPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['BOOKINGS'])

class GuestPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['GUEST'])

class RoomsPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['ROOMS'])

class LockersPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['LOCKERS'])

class ShowerPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['SHOWER'])

class FinancePermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['FINANCE'])

class CustomerServicePermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['CUSTOMERSERVICE'])

class SupervisorPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['SUPERVISOR'])

class HouseKeepingPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['HOUSEKEEPING'])

class PosPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['POS'])

class ReportsPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['REPORTS'])

class AnalyticalReportsPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['ANALYTICALREPORTS'])
    
class AdminAccessPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['ADMINACCESS'])

class SystemSettingsPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['SYSTEMSETTINGS'])

class SuperAdminPermission(BasePermission):
    def has_permission(self, request, view):
        return permission_checking(request, ['SUPERADMIN'])
    




def permission_checking(request, access_permissions = []):
    try:
        account = Account.objects.get(pk=request.user.account_id)
    except Exception as e:
        return False
    
    permissions = account.role.permissions.all()
    have_permission = False
    for permission in permissions:
        if permission.codename in access_permissions:
            have_permission = True
            break
    
    if request.user.is_authenticated and have_permission:
        return True
    return False