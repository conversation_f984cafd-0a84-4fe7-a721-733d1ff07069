from .models import Shift
from django.utils import timezone
from django.db import transaction
def automatic_shift_end():
    
    # Cancel all shift that are only 12 hours away from star tdate
    with transaction.atomic():
        shifts = Shift.objects.filter(end_shift_datetime__isnull=True, \
                                      start_shift_datetime__lt=timezone.now() - timezone.timedelta(hours=12)) \
                               .select_for_update()
    
        for shift in shifts:
            shift.end_by_system()


