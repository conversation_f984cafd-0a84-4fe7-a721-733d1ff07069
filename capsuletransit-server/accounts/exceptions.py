

class AccountNotFound(Exception):
    pass

class UsernameUsed(Exception):
    pass 

class EmailUsed(Exception):
    pass

class SameAccount(Exception):
    pass


class RoleExist(Exception):
    pass

class RoleNotFound(Exception):
    pass

class NoPermission(Exception):
    pass

class RoleHaveActiveAccounts(Exception):
    pass


class PermissionNotFound(Exception):
    pass

class PermissionForbidden(Exception):
    pass