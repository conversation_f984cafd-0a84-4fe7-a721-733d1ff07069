from decimal import Decimal
from typing import List, Union
import uuid
from django.shortcuts import render
from salesreport.models import SalesTransaction
from django.db import transaction as db_transaction

from rest_framework import viewsets, status
from djangorestframework_camel_case.parser import Camel<PERSON><PERSON>J<PERSON>NParser
from djangorestframework_camel_case.render import Came<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from lot.models import Settings
from transaction.models import Transaction
from rest_framework.response import Response
from datetime import datetime
from django.utils import timezone
from django.db.models import Q, Prefetch

from .serializers import SalesReportSerializer, SalesReportSummaryGroupByBooking
from queryservice.sales import get_sales_report_grouped

# from transaction.
# TransactionItemCategory
from constant.enums import TransactionItemCategory


def percentage_to_decimal(percentage_str):
    # Remove the percentage sign
    percentage_str = percentage_str.replace("%", "")

    # Convert to float and divide by 100
    return Decimal(percentage_str) / 100


def create_sales_transction_from_transaction(transaction: Transaction):

    prev_sales_transaction_exist = SalesTransaction.objects.filter(
        transaction_head_id=transaction.pk
    ).exists()

    if prev_sales_transaction_exist:
        return

    items = transaction.items

    # Find first duration from items
    duration = 0

    # Get tax percentage
    adjustment_amount = (
        transaction.adjustements_amount
        if transaction.adjustements_amount != None
        else 0
    )
    promotion_amount = (
        transaction.promotion_amount if transaction.promotion_amount != None else 0
    )

    total_bill = (
        transaction.debit_amount
        if transaction.debit_amount != 0
        else transaction.credit_amount
    )

    tax_percentage = 0
    service_charge_percentage = 0

    if total_bill != 0:
        if transaction.tax_amount != 0:
            lot_id = transaction.booking.lot
            lot_settings_tax = Settings.objects.filter(
                lot_id=lot_id, settings_category="Tax"
            )
            tax_percentage = percentage_to_decimal(
                lot_settings_tax.first().settings_description
            ) if lot_settings_tax.first() else 0

        if transaction.service_charge_amount != 0:
            lot_id = transaction.booking.lot
            lot_settings_service_charge = Settings.objects.filter(
                lot_id=lot_id, settings_category="Service Charge"
            )
            service_charge_percentage = percentage_to_decimal(
                lot_settings_service_charge.first().settings_description
            ) if lot_settings_service_charge.first() else 0

    for item in items:
        duration = item.get("duration", 0)
        if int(duration) > 0:
            break

    for item in items:
        quantity = int(item.get("quantity", 1))
        category = item.get("category")
        item_subtotal = quantity * Decimal(item["price"])
        tax_value = item_subtotal * tax_percentage
        service_charge_value = item_subtotal * service_charge_percentage
        final_subtotal = item_subtotal + tax_value + service_charge_value

        if category not in [
            TransactionItemCategory.SERVICE_CHARGE,
            TransactionItemCategory.TAX,
        ]:

            sales_transaction = SalesTransaction(
                transaction_head=transaction,
                transaction_type=transaction.payment_type,
                payment_type=transaction.payment_type,
                transaction_date=transaction.transaction_datetime,
                shift=transaction.shift,
                invoice_no=transaction.invoice_no,
                currency=transaction.currency,
                item_name=item["item_name"],
                item_type=item["item_type"],
                item_price=item["price"],
                status=transaction.transaction_status,
                qty=quantity,
                item_category=item["category"],
                duration=duration,
                tax_value=tax_value,
                service_charge=service_charge_value,
                item_id=item["item_id"],
                subtotal=final_subtotal,
            )
            sales_transaction.save()

    adjustment_tax_value = tax_percentage * transaction.adjustements_amount
    adjustment_service_charge_value = (
        service_charge_percentage * transaction.adjustements_amount
    )
    # SalesTransaction(
    #     transaction_head=transaction,
    #     transaction_type=transaction.payment_type,
    #     transaction_date=transaction.transaction_datetime,
    #     payment_type=transaction.payment_type,
    #     status=transaction.transaction_status,
    #     shift=transaction.shift,
    #     invoice_no=transaction.invoice_no,
    #     currency=transaction.currency,
    #     item_id="Adjustment",
    #     item_name="Adjustment",
    #     item_type="Adjustment",
    #     item_category="Adjustment",
    #     item_price=transaction.adjustements_amount,
    #     duration=duration,
    #     tax_value=adjustment_tax_value,
    #     service_charge=service_charge_value,
    #     subtotal=adjustment_amount
    #     + adjustment_tax_value
    #     + adjustment_service_charge_value,
    # ).save()

    # SalesTransaction(
    #     transaction_head=transaction,
    #     transaction_type=transaction.payment_type,
    #     payment_type=transaction.payment_type,
    #     status=transaction.transaction_status,
    #     transaction_date=transaction.transaction_datetime,
    #     shift=transaction.shift,
    #     invoice_no=transaction.invoice_no,
    #     currency=transaction.currency,
    #     item_id="Promotions",
    #     item_name="Promotions",
    #     item_type="Promotion",
    #     item_category="Promotion",
    #     duration=duration,
    #     item_price=(
    #         transaction.promotion_amount if transaction.promotion_amount != None else 0
    #     ),
    #     tax_value=0,
    #     service_charge=0,
    #     subtotal=(
    #         transaction.promotion_amount if transaction.promotion_amount != None else 0
    #     ),
    # ).save()


# Convert transaction model to sales report
def convert_transaction_to_sales_report(transactions: List[Transaction]):
    for transaction in transactions:
        with db_transaction.atomic():
            create_sales_transction_from_transaction(transaction)
    return


def update_sales_report_to_status_paid(transaction: Transaction):

    with db_transaction.atomic():
        SalesTransaction.objects.select_for_update().filter(
            transaction_head_id=transaction.transaction_id
        ).update(
            status=transaction.transaction_status,
            transaction_type=str(transaction.payment_type),
            payment_type=transaction.payment_type,
            transaction_date=transaction.transaction_datetime,
            shift=transaction.shift,
        )
        return True


class SalesReportViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = SalesTransaction.objects.all().prefetch_related(
            "transaction_head", "transaction_head__booking"
        )
        return qs

    def list(self, request):

        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        report_type = request.GET.get("reportType", None)
        versions = request.GET.get("versions", None)
        group_by = request.GET.get("groupBy", None)
        booking_status = request.GET.get("bookingStatus", None)

        try:
            qs = self.get_queryset()

            if (
                report_type == "sales_and_collections"
                and not start_date_time.strip()
                and not end_date_time.strip()
            ):
                raise Exception(
                    "Make sure start date and end date is selected for Sales and Collection report"
                )

            if start_date_time and end_date_time:
                start_date = datetime.fromtimestamp(int(start_date_time))
                end_date = datetime.fromtimestamp(int(end_date_time))
                start_date = timezone.make_aware(
                    start_date, timezone.get_current_timezone()
                )
                end_date = timezone.make_aware(
                    end_date, timezone.get_current_timezone()
                )

                if report_type and report_type == "sales" in report_type:
                    qs = qs.filter(
                        Q(transaction_head__booking__booking_status__is_latest=True)
                        & Q(
                            transaction_head__booking__booking_status__check_in_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                    )

                if report_type == "collections":
                    qs = qs.filter(
                        transaction_head__transaction_datetime__range=(
                            start_date,
                            end_date,
                        )
                    )

            serializer = SalesReportSerializer(qs, many=True)

            if versions and versions == "summary":

                qs = get_sales_report_grouped(group_by=group_by)

                if group_by and group_by == "booking":
                    ...
                    # serializer = SalesReportSummaryGroupByBooking()

                if group_by and group_by == "payment-type":
                    ...

                for data in qs:
                    print(data)
                    print("-------------------------")

            if versions and versions == "detailed":
                ...

            return Response(
                {"status": "success", "data": "test"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
