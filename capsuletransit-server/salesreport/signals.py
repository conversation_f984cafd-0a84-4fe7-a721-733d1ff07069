import traceback
from django.dispatch import receiver
from django.db.models.signals import post_save
from salesreport.views import (
    update_sales_report_to_status_paid,
    create_sales_transction_from_transaction,
)
from transaction.models import Transaction


# Listen on the post_save signal of the Transaction model
@receiver(post_save, sender=Transaction)
def create_sales_report(sender, instance, created, **kwargs):
    # ---------------------------------------OLD FUNCTION---------------------------------------#
    # if instance.transaction_status == "Paid":
    #     create_sales_transction_from_transaction(instance)
    #     return

    # #  Update Sales report to status paid and relevant fields
    # update_sales_report_to_status_paid(instance)
    # ---------------------------------------OLD FUNCTION---------------------------------------#

    if instance.transaction_status == "Paid":
        create_sales_transction_from_transaction(instance)
        return

    if instance.transaction_status == "Pending Payment":
        return

    #  Update Sales report to status paid and relevant fields
    update_sales_report_to_status_paid(instance)
