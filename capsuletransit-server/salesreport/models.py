import datetime
from django.db import models
from transaction.models import Transaction

# Create your models here.


class SalesTransaction(models.Model):
    id = models.BigAutoField(primary_key=True)
    lot = models.ForeignKey('lot.Lot', models.DO_NOTHING, db_column='lot_id', blank=True, null=True)
    transaction_head = models.ForeignKey(Transaction, models.DO_NOTHING)
    transaction_type = models.CharField(max_length=50)
    payment_type = models.ForeignKey("payment.PaymentType", on_delete=models.SET_NULL, null=True, verbose_name="Payment Type ID")
    transaction_date = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=50, default="Paid")# Paid | Pending
    shift = models.ForeignKey('accounts.Shift', models.DO_NOTHING, db_column='shift_id')
    invoice_no = models.CharField(max_length=20)
    currency = models.ForeignKey('currency.Currency', models.DO_NOTHING, db_column='currency_id')
    item_name = models.CharField(max_length=100)
    item_type = models.CharField(max_length=50)
    item_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, default=0)
    qty = models.IntegerField(default=1)
    item_category = models.CharField(max_length=50, blank=True, null=True)
    item_id = models.CharField(max_length=50)
    duration = models.IntegerField(default=0)
    tax_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, default=0)
    service_charge = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, default=0)
    subtotal = models.DecimalField(max_digits=16, decimal_places=2, blank=True, null=True, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
