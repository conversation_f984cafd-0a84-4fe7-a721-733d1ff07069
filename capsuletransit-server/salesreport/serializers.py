
from rest_framework import serializers
from .models import SalesTransaction


class SalesReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesTransaction
        fields = "__all__"


class SalesReportSummary(serializers.Serializer):
    # date = serializers.DateTimeField()
    room_sales = serializers.DecimalField(max_digits=16, decimal_places=2)
    no_show = serializers.DecimalField(max_digits=16, decimal_places=2)
    pos = serializers.DecimalField(max_digits=16, decimal_places=2)
    gross = serializers.DecimalField(max_digits=16, decimal_places=2)
    service_charge = serializers.DecimalField(max_digits=16, decimal_places=2)
    tax = serializers.DecimalField(max_digits=16, decimal_places=2)
    promotion = serializers.DecimalField(max_digits=16, decimal_places=2)
    adjustments = serializers.DecimalField(max_digits=16, decimal_places=2)
    total = serializers.DecimalField(max_digits=16, decimal_places=2)


class SalesReportDetaild(serializers.Serializer):
    # TODO : Add rounding to serializer
    booking_id = serializers.UUIDField()
    booking_no = serializers.CharField()
    transfer_from_to = serializers.CharField()
    booking_date = serializers.DateTimeField()
    payment_date = serializers.DateTimeField()
    method = serializers.CharField()
    pan = serializers.CharField()
    payment_status = serializers.CharField()
    booking_status = serializers.CharField()
    check_in_date = serializers.DateTimeField()
    actual_check_in_date = serializers.DateTimeField()
    check_out_date = serializers.DateTimeField()
    actual_check_out_date = serializers.DateTimeField()
    ota = serializers.CharField()
    source = serializers.CharField()
    guest = serializers.CharField()
    total_rooms = serializers.IntegerField()
    interval = serializers.IntegerField()
    room_type = serializers.CharField()
    sales = serializers.DecimalField(max_digits=50, decimal_places=2)
    tax = serializers.DecimalField(max_digits=50, decimal_places=2)
    service_charge = serializers.DecimalField(max_digits=50, decimal_places=2)
    promotions = serializers.DecimalField(max_digits=50, decimal_places=2)
    adjustment_string = serializers.CharField(required=False)
    adjustments = serializers.DecimalField(max_digits=50, decimal_places=2)
    total = serializers.DecimalField(max_digits=50, decimal_places=2)
    booking_remarks = serializers.CharField()


class SalesReportSummaryGroupByBooking(SalesReportSummary):
    booking_id = serializers.UUIDField()
    booking_no = serializers.CharField()


class SalesReportSummaryGroupBypaymentType(SalesReportSummary):
    payment_type_id = serializers.UUIDField()
    payment_type_name = serializers.CharField()


class SalesReportSummaryGroupByBookingPlatform(SalesReportSummary):
    booking_platform_id = serializers.CharField()
    booking_platform = serializers.CharField()


class SalesReportSummaryGroupByBookingDuration(SalesReportSummary):
    booking_duration = serializers.CharField()


class SalesReportSummaryGroupByRoomType(SalesReportSummary):
    room_type_name = serializers.CharField()


class SalesReportDetailedGroupByTransaction(SalesReportDetaild):
    invoice_no = serializers.CharField()