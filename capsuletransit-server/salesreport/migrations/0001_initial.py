# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("payment", "0001_initial"),
        ("transaction", "0001_initial"),
        ("lot", "0001_initial"),
        ("currency", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SalesTransaction",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("transaction_type", models.CharField(max_length=50)),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                ("status", models.Char<PERSON>ield(default="Paid", max_length=50)),
                ("invoice_no", models.CharField(max_length=20)),
                ("item_name", models.CharField(max_length=100)),
                ("item_type", models.Char<PERSON><PERSON>(max_length=50)),
                (
                    "item_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                    ),
                ),
                ("qty", models.IntegerField(default=1)),
                (
                    "item_category",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("item_id", models.CharField(max_length=50)),
                ("duration", models.IntegerField(default=0)),
                (
                    "tax_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "service_charge",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "subtotal",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=16,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "currency",
                    models.ForeignKey(
                        db_column="currency_id",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="currency.currency",
                    ),
                ),
                (
                    "lot",
                    models.ForeignKey(
                        blank=True,
                        db_column="lot_id",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="lot.lot",
                    ),
                ),
                (
                    "payment_type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="payment.paymenttype",
                        verbose_name="Payment Type ID",
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        db_column="shift_id",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="accounts.shift",
                    ),
                ),
                (
                    "transaction_head",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="transaction.transaction",
                    ),
                ),
            ],
        ),
    ]
