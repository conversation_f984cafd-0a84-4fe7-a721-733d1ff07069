import datetime
from collections import defaultdict

from capsuletransit.utils import dictfetchall
from queryservice import room as room_queryA
from queryservice import sales as sales_query


def room_occupancy_revenue(
        cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    subtotal_check_in_results = sales_query.get_checked_in_booking_subtotal(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    subtotal_no_show_results = sales_query.get_no_show_booking_subtotal(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    subtotal_lounge_results = sales_query.get_lounge_booking_subtotal(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    occupancies = room_queryA.get_room_occupancy_by_roombooking(
        cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    actual_occupancies = room_queryA.get_room_occupancy_by_roombooking_with_checkin(
        cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    no_show_count = room_queryA.get_pax_count_noshow(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    checkin_count = room_queryA.get_pax_count_checkin(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )
    room_counts = room_queryA.get_rooms_list_zone_lot(cursor, lot_id=lot_id)

    lounge_count = room_queryA.get_pax_count_lounge(
        cursor=cursor, start_date=start_date, end_date=end_date, lot_id=lot_id
    )

    # Define the variables for aggregation
    subtotal_no_show = 0
    total_occupied_room = 0
    subtotal_check_in = 0
    subtotal_lounge = 0
    no_show_pax_count = 0
    checkin_pax_count = 0
    lounge_pax_count = 0
    total_rooms = 0

    def default_occ_map_info():
        return {
            "total_occupied_room": 0,
            "total_room_booked": 0,
            "total_room": 0,
            "color_code": "#fffff",
        }

    occupany_map_dict = defaultdict(default_occ_map_info)
    # Aggregate the results
    if len(subtotal_no_show_results) > 0:
        subtotal_no_show = subtotal_no_show_results[0]["sum"]
    if len(subtotal_check_in_results) > 0:
        subtotal_check_in = subtotal_check_in_results[0]["sum"]
    if len(subtotal_lounge_results) > 0:
        subtotal_lounge = subtotal_lounge_results[0]["sum"]

    if len(no_show_count) > 0:
        no_show_pax_count = no_show_count[0]["count"]
    if len(checkin_count) > 0:
        checkin_pax_count = checkin_count[0]["count"]
    if len(lounge_count) > 0:
        lounge_pax_count = lounge_count[0]["count"]

    # Pre generate the room type occupancy map
    for room in room_counts:
        type_name = room["type_name"]
        occupany_map_dict[type_name]["total_room"] += room["count"]
        occupany_map_dict[type_name]["color_code"] = room["color_tags"]
        total_rooms += room["count"]

    # Fill in the occupied room count
    for occ in occupancies:
        type_name = occ["type_name"]
        occupany_map_dict[type_name]["total_room_booked"] += occ["count"]

    for actual_occ in actual_occupancies:
        type_name = actual_occ["type_name"]
        occupany_map_dict[type_name]["total_occupied_room"] += actual_occ["count"]
        total_occupied_room += actual_occ["count"]

    # Remap the dictionary to list with room type name as key value pair as well
    occupancy_details = [
        {
            "room_type_name": k,
            "total_occupied_room": v["total_occupied_room"],
            "total_room_booked": v["total_room_booked"],
            "total_room": v["total_room"],
            "color_code": v["color_code"],
        }
        for k, v in occupany_map_dict.items()
    ]

    return {
        "total_room": total_rooms if total_rooms else 0,
        "total_occupied_room": total_occupied_room if total_occupied_room else 0,
        "guest_stayed": checkin_pax_count if checkin_pax_count else 0,
        "stayed_total_revenue": subtotal_check_in if subtotal_check_in else 0,
        "no_show_guests": no_show_pax_count if no_show_pax_count else 0,
        "no_show_revenue": subtotal_no_show if subtotal_no_show else 0,
        "lounge_guests": lounge_pax_count if lounge_pax_count else 0,
        "lounge_revenue": subtotal_lounge if subtotal_lounge else 0,
        "details": occupancy_details,
    }

# fetch_top_nationalities
def fetch_top_nationalities(cursor, start_date: datetime.datetime, end_date: datetime.datetime, top_n: int, lot_id: int = None):
    if lot_id == '':
        lot_id = None

    # Define the query with lot_id filter
    query = """
        SELECT 
            gc.country_name, 
            COUNT(*) AS visitor_count
        FROM bookings_booking AS bb
        JOIN guests_customer AS gcust ON bb.customer_staying_id = gcust.customer_id
        JOIN guests_country AS gc ON gcust.country_id = gc.country_id
        WHERE 
            bb.booking_made_datetime BETWEEN %s AND %s
            AND (%s IS NULL OR bb.lot_id = %s)
        GROUP BY gc.country_name
        ORDER BY visitor_count DESC
        LIMIT %s;
        """
    
    params = [start_date, end_date, lot_id, lot_id, top_n]

    # Execute the query
    cursor.execute(query, params)

    # Fetch the results
    results = dictfetchall(cursor)
    
    # query total count of bookings, then calculate the percentage
    query = """
        SELECT 
            COUNT(*) AS total_count
        FROM bookings_booking AS bb
        JOIN guests_customer AS gcust ON bb.customer_staying_id = gcust.customer_id
        JOIN guests_country AS gc ON gcust.country_id = gc.country_id
        WHERE 
            bb.booking_made_datetime BETWEEN %s AND %s
            AND (%s IS NULL OR bb.lot_id = %s)
    """
    
    params = [start_date, end_date, lot_id, lot_id]
    
    cursor.execute(query, params)
    total_count = cursor.fetchone()[0]
    
    for result in results:
        result['visitor_percentage'] = round(result['visitor_count'] / total_count * 100, 2) if total_count > 0 else 0

    return results

