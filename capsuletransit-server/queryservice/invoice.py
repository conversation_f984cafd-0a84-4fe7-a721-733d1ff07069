from constant.enums import TransactionItemCategory
from queryservice.utils import map_to_payment_mode
from transaction.models import Transaction
from decimal import Decimal, ROUND_UP


def transform_transaction_to_invoice(transaction: Transaction): 
    """
    Transform a transaction object to invoice format
    """
    # Transform items to new format
    transformed_items = []
    tax_details = []
    for item in transaction.items:
        category = item.get('category')
        price = Decimal(item.get('price', 0))
        quantity = int(item.get('quantity', 1))
        itemId = '';
        description = '';
       
        if category == TransactionItemCategory.TAX:
            continue

        if category == TransactionItemCategory.SERVICE_CHARGE and price == 0:
            continue

        if category == TransactionItemCategory.PROMOTION and price == 0:
            continue
    
        tax_type = '02'
        tax_rate = Decimal('0.08')
        tax = (price * quantity * tax_rate).quantize(Decimal('0.01'), rounding=ROUND_UP)

        if category == TransactionItemCategory.SERVICE_CHARGE:
            tax_type = '06'
            tax_rate = Decimal('0')
            tax = Decimal('0')

        if category == TransactionItemCategory.MERCH_SALES:
            itemId = item.get('item_name')
        elif category == TransactionItemCategory.ADJUSTMENT:
            # Adjustment item is not formatted in the same way as other items, so we need to handle it differently
            if item.get('item_id') == 'Adjustment':
                # adjust amount on the order
                itemId = 'Adjustment'
            elif item.get('item_type') == 'Adjustment' and item.get('quantity') is None:
                # Updated price (no quantity), override amount of the order
                itemId = 'Adjustment'
                description = item.get('item_name')
            else:
                # adjustment is a main item
                itemId = 'Adjustment'
                description = item.get('item_type')
        elif category == TransactionItemCategory.OVERSTAY:
            itemId = item.get('item_name')
        elif category == TransactionItemCategory.PROMOTION:
            itemId = item.get('item_type')
            description = item.get('item_name')
            tax = Decimal('0')
        elif category == TransactionItemCategory.ROUNDING:
            itemId = 'Rounding'
            tax = Decimal('0')
        else:
            itemId = item.get('item_type')

        transformed_item = {
            'item': itemId,
            'classification': '022',
            'description': description,
            'quantity': quantity,
            'unitCost': price,
            'uom': 'unit',
            'taxType': tax_type,
            'taxRate': tax_rate,
            'discount': Decimal('0'),
            'discountRate': Decimal('0'),
            'tax': tax,
            'taxExemptionDetails': None,
            'taxExemptionAmount': Decimal('0'),
            'chargeRate': Decimal('0'),
            'chargeAmount': Decimal('0'),
            'productTariffCode': None,
            'originCountry': None,
            'subTotal': price,
            'subTotalWithoutTax': price * quantity,
            'subTotalWithTax': price*quantity + tax,
            'total': price*quantity + tax,
            'invoiceVersion': None,
            'poNo': None
        }
        transformed_items.append(transformed_item)
        tax_details.append({
            'totalTaxableAmountPerTaxType': price,
            'totalAmountPerTaxType': tax,
            'type': transformed_item.get('taxType', '02'),
            'taxExemptionReason': None
        })

    # Prepare response data
    return {
        # Billing Fields
        'docNo': transaction.invoice_no,
        'type': 'invoice',
        'customerId': transaction.customer.customer_id if transaction.customer else None,
        'status': transaction.transaction_status,
        'currency': transaction.currency.currency_code if transaction.currency else None,
        'createdAt': transaction.transaction_datetime,
        'dueAt': None,
        'paidAt': transaction.transaction_datetime,
        'referenceDocNo': transaction.booking.booking_no if transaction.booking else None,

        # Exchange Rate Fields
        'exchange': None,

        # Billing Frequency
        'billingFrequency': None,

        # Payment Terms
        'paymentTerms': transaction.payment_type.payment_type if transaction.payment_type else None,
        'prePaymentAmount': None,
        'prePaymentAt': None,
        'prePaymentReferenceNumber': None,

        # Billing Period
        'billingPeriodStartDate': None,
        'billingPeriodEndDate': None,

        # Payment Mode
        'billReference': transaction.payment_reference,

        # Amount Fields
        'discount': Decimal(transaction.promotion_amount or 0),
        'additionalDiscount': None,
        'totalDiscount': Decimal(transaction.promotion_amount or 0),
        'totalCharge': Decimal(transaction.debit_amount or 0),
        'totalTax': Decimal(transaction.tax_amount or 0),
        'roundingAmount': Decimal(transaction.rounding or 0),
        'additionalFee': Decimal(transaction.service_charge_amount or 0),
        'subTotal': Decimal(transaction.sum or 0) + Decimal(transaction.adjustements_amount or 0) + Decimal(transaction.service_charge_amount or 0) - Decimal(transaction.promotion_amount or 0),
        'total': Decimal(transaction.debit_amount or 0),
        'totalExclTax': Decimal(transaction.sum or 0) + Decimal(transaction.adjustements_amount or 0) + Decimal(transaction.service_charge_amount or 0) - Decimal(transaction.promotion_amount or 0),
        'totalNetAmount': Decimal(transaction.sum or 0) + Decimal(transaction.adjustements_amount or 0) + Decimal(transaction.service_charge_amount or 0) - Decimal(transaction.promotion_amount or 0),
        'totalPayable': Decimal(transaction.sum or 0) + Decimal(transaction.adjustements_amount or 0) + Decimal(transaction.service_charge_amount or 0) - Decimal(transaction.promotion_amount or 0) + Decimal(transaction.tax_amount or 0),
        'totalInclTax':  Decimal(transaction.debit_amount or 0) - Decimal(transaction.rounding or 0),

        # Additional Fields
        'items': transformed_items,
        'billingDetail': None,
        'prePaymentDetail': None,
        'taxDetails': tax_details,
        'supplier': None,
        'recipient': None,
        'paymentMode': map_to_payment_mode(transaction.payment_type.payment_type, transaction.payment_type.payment_method.payment_method),
        'billReferenceNumber': transaction.payment_reference,
        'remarks': transaction.adjustments,
        'printType': None,
        'custom': None,
        'balance': None,
        'totalTaxExemption': None,
        'totalTaxExemptionReason': None,
        'totalTaxableSalesTax': None,
        'totalTaxableServiceTax': None,
        'additionalDetails': None,
        'additionChargesDetails': Decimal(transaction.adjustements_amount or 0),
        'poNo': None,
        'doNo': None,
        'source': transaction.booking.platform.platform,

        # Original Transaction Fields
        'originalTransaction': {
            # Transaction Model Fields
            "transactionId": transaction.transaction_id,
            "invoiceNo": transaction.invoice_no,
            "paymentDetails": transaction.payment_details,
            "sum": Decimal(transaction.sum or 0),
            "taxAmount": Decimal(transaction.tax_amount or 0),
            "serviceChargeAmount": Decimal(transaction.service_charge_amount or 0),
            "items": transaction.items,
            "transactionStatus": transaction.transaction_status,
            "transactionDatetime": transaction.transaction_datetime,
            "creditAmount": Decimal(transaction.credit_amount or 0),
            "debitAmount": Decimal(transaction.debit_amount or 0),
            "isLatest": bool(transaction.is_latest),
            "isRoomBooking": bool(transaction.is_room_booking),
            "adjustments": transaction.adjustments,
            "adjustmentsAmount": Decimal(transaction.adjustements_amount or 0),
            "promotionAmount": Decimal(transaction.promotion_amount or 0),
            "rounding": Decimal(transaction.rounding or 0),
            "paymentRemarks": transaction.payment_remarks,
            "paymentReference": transaction.payment_reference,
            "pan": transaction.pan,
            "guestGiven": Decimal(transaction.guest_given or 0),
            "guestChange": Decimal(transaction.guest_change or 0),
            "refunded": bool(transaction.refunded),
            "customerId": transaction.customer_id,
            "paymentTypeId": transaction.payment_type_id,
            "paymentType": transaction.payment_type.payment_type if transaction.payment_type else None,
            "bookingId": transaction.booking_id,
            "lotId": transaction.booking.lot_id,
            "currencyId": transaction.currency_id,
            "currencyCode": transaction.currency.currency_code if transaction.currency else None,

            # Related Models
            "customer": {
                "customerId": transaction.customer.customer_id if transaction.customer else None,
                "firstName": transaction.customer.firstname if transaction.customer else None,
                "lastName": transaction.customer.lastname if transaction.customer else None,
                "country": {
                    "countryCode": transaction.customer.country.country_code if transaction.customer and transaction.customer.country else None,
                    "countryName": transaction.customer.country.country_name if transaction.customer and transaction.customer.country else None
                }
            },
        }
    }
