import copy
from functools import reduce
from django.db import connection
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import response
from rest_framework import viewsets
from rest_framework.decorators import action, api_view
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.utils.timezone import datetime, timedelta
from common.pagination import DefaultPageNumberPagination
from constant.enums import TransactionItemCategory
from queryservice import aggregation as aggregate_query
from queryservice import room as room_query
from queryservice import sales as sales_query
from transaction.models import Transaction
from .serializers import (
    DashboardRevenueSerializer,
    DayShiftSerializer,
    DayShiftWithOverview,
    LockerSummarySerializer,
    MerchSummarySerializer,
    PaymentCollectedSerializer,
    RoomBookingCountyByRoomTypeSerializer,
    RoomBookingCountByPlatformSerializer,
    RoomOccupanyCountRevenueSerializer,
    RoomShiftReportSerializer,
    SalesSummaryaByDateSerializer,
    SalesSummaryaByDateSerializerV2,
    ShowerSummarySerializer,
    StaffShiftWithOverview,
    TransactionShiftReportSerializer, TopNationalitySerializer,
    RoomRevenueSerializer,
    RoomRevenueByPlatformSerializer,
    InvoiceResponseSerializer,
)
from queryservice import invoice
from .authentication import InvoiceAPIKeyAuthentication
from django.db.models import Q


class QueryRoomBookingViewSet(viewsets.ViewSet):
    authentication_classes = []
    permission_classes = []

    @swagger_auto_schema(
        operation_description="Get room booking count by room type",
        auhentication_classes=[],
        manual_parameters=[
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot id",
            ),
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomBookingCountyByRoomTypeSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-booking-count-by-room-type",
        url_name="room_booking_count_by_room_type",
    )
    def get_room_booking_by_room_type(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = room_query.query_total_room_book_by_platform(
            start_date, end_date, lot_id
        )
        serializer = RoomBookingCountyByRoomTypeSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get room occupany and Revenue",
        manual_parameters=[
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot id (optional)",
                required=False
            ),
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomOccupanyCountRevenueSerializer},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-occupancy-count-revenue",
        url_name="room_occupany_count_revenue",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_room_occupany_revenue(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        with connection.cursor() as cursor:
            data = aggregate_query.room_occupancy_revenue(
                cursor=cursor,
                start_date=start_date,
                end_date=end_date,
                lot_id=lot_id
            )
            serializer = RoomOccupanyCountRevenueSerializer(data=data)
            serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get room booking count by platform",
        manual_parameters=[
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot id",
            ),
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomBookingCountByPlatformSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-booking-count-by-platform",
        url_name="room_booking_acount_by_platform",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_room_booking_by_platform(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = room_query.query_total_room_book_by_platform(
            start_date, end_date, lot_id
        )
        serializer = RoomBookingCountByPlatformSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get Sales revenue",
        manual_parameters=[
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot id (optional)",
                required=False
            ),
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomRevenueSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="dashboard-revenue",
        url_name="dashboard_revenue",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_sales_revenue(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = sales_query.query_total_revenue( start_date, end_date, lot_id)
        serializer = DashboardRevenueSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get room booking revenue by platform",
        manual_parameters=[
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot id (optional)",
                required=False
            ),
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomRevenueByPlatformSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-booking-revenue-by-platform",
        url_name="room_booking_revenue_by_platform",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_room_revenue_by_platform(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = sales_query.query_room_revenue_by_platform(start_date, end_date, lot_id)
        serializer = RoomRevenueByPlatformSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)


class EndShiftSalesQueryView(viewsets.ViewSet):

    @swagger_auto_schema(
        operation_description="Get sales summary by shift settings",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
            # openapi.Parameter(
            #     name='shift_id',
            #     in_=openapi.IN_QUERY,
            #     type=openapi.TYPE_STRING,
            #     description='Shift ID',
            # ),
        ],
        responses={200: StaffShiftWithOverview()},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="sales-summary-by-shift-settings",
        url_name="sales_summary_by_shift_settings",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
        renderer_classes=[CamelCaseJSONRenderer],
    )
    def get_sales_by_staffshift(self, request):
        start_date = request.GET.get("start_date", None)
        end_date = request.GET.get("end_date", None)
        data = sales_query.staffshift_sales_query(
            start_date, end_date, request.user.lot_id
        )
        serializer = StaffShiftWithOverview(data=data)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get sales summary by day",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        # responses={200: SalesSummaryaByDateSerializer(many=True)},
        responses={200: DayShiftSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="sales-summary-by-day",
        url_name="sales_summary_by-day",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
        renderer_classes=[CamelCaseJSONRenderer],
    )
    def get_sales_by_day(self, request):
        start_date = request.GET.get("start_date", None)
        end_date = request.GET.get("end_date", None)
        data = sales_query.shift_per_day_query(
            start_date, end_date, request.user.lot_id
        )
        serializer = DayShiftWithOverview(data=data)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)


class SalesSummaryQueryView(viewsets.ViewSet):
    # ------------------------------ Dashboard API ------------------------------ #
    @swagger_auto_schema(
        operation_description="Get sales summary by date",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: SalesSummaryaByDateSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-sales-summary-by-date",
        url_name="room_sales_summary_by_date",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_sales_sales_by_date(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = sales_query.fetch_sales_room_sales_sumamry(start_date, end_date, lot_id)
        serializer = SalesSummaryaByDateSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get sales Average summary by date",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: SalesSummaryaByDateSerializerV2()},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="room-sales-average-summary-by-date",
        url_name="room_sales_average_summary_by_date",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_sales_avg_by_date(self, request):
        lot_id = request.GET.get("lot_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        data = sales_query.fetch_sales_room_avg_sales_sumamry(
            start_date, end_date, lot_id
        )
        serializer = SalesSummaryaByDateSerializerV2(data=data)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get Total Payment Collected By Shift ID, Start Date, and End Date",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="shift_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Shift ID",
            ),
        ],
        responses={200: PaymentCollectedSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="total-payment-collected-by-shift",
        url_name="total_payment_collected-by_shift",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_total_payment_collected(self, request):
        shift_id = request.GET.getlist("shift_id[]", None)
        date = request.GET.getlist("date[]", None)
        data = sales_query.query_total_payment_collected(shift_id, date)
        serializer = PaymentCollectedSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get sales Average summary by date",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
        ],
        responses={200: RoomShiftReportSerializer},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="shift-room-sales-report",
        url_name="shift_sales_report",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_sales_shift_by_shift_id(self, request):
        shift_id = request.GET.getlist("shift_id[]", None)
        date = request.GET.getlist("date[]", None)
        type = request.GET.get("type", None)
        (durations_grp, columns) = sales_query.staffshift_room_sales_summary_query(
            shift_id, date, type, request.user.lot_id
        )

        serializer = RoomShiftReportSerializer(
            data={"columns": columns, "duration_groups": durations_grp}
        )

        serializer.is_valid()
        return response.Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get sales summary by shift",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="shift_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Shift ID",
            ),
        ],
        responses={200: SalesSummaryaByDateSerializer(many=True)},
        # responses={200: StaffShiftSummarySerializer(many=True)},
    )
    # ------------------------------ Dashboard API ------------------------------ #

    # ------------------------------ End Shift Summary API ------------------------------ #
    # @action(
    #     methods=["get"],
    #     detail=False,
    #     url_path="room-sales-summary-by-shift",
    #     url_name="room_sales_summary_by_shift",
    #     authentication_classes=[JWTAuthentication],
    #     permission_classes=[IsAuthenticated],
    # )
    # def get_room_sales_by_shift(self, request):
    #     shift_id = request.GET.get("shift_id")
    #     data = sales_query.shift_sales_room_sales_sumamry(shift_id)
    #     # serializer = SalesShiftSummarySerializer(data=data, many=True)
    #     serializer = StaffShiftSummarySerializer(data=data, many=True)
    #     serializer.is_valid(raise_exception=True)
    #     return response.Response(serializer.data)

    # @swagger_auto_schema(
    #     operation_description="Get sales summary by day",
    #     manual_parameters=[
    #         openapi.Parameter(
    #             name="start_date",
    #             in_=openapi.IN_QUERY,
    #             type=openapi.TYPE_STRING,
    #             description="Start date in format YYYY-MM-DD HH:MM:SS",
    #         ),
    #         openapi.Parameter(
    #             name="end_date",
    #             in_=openapi.IN_QUERY,
    #             type=openapi.TYPE_STRING,
    #             description="End date in format YYYY-MM-DD HH:MM:SS",
    #         ),
    #     ],
    #     # responses={200: SalesSummaryaByDateSerializer(many=True)},
    #     responses={200: StaffShiftSummarySerializer(many=True)},
    # )
    # @action(
    #     methods=["get"],
    #     detail=False,
    #     url_path="room-sales-summary-by-day",
    #     url_name="room_sales_summary_by-day",
    #     authentication_classes=[JWTAuthentication],
    #     permission_classes=[IsAuthenticated],
    # )
    # def get_room_sales_by_day(self, request):
    #     # lot_id = request.GET.get("lot_id")
    #     start_date = request.GET.get("startDateTime")
    #     end_date = request.GET.get("endDateTime")
    #     # shift_id = request.GET.get("shift_id")
    #     data = sales_query.shift_per_day_query(start_date, end_date)
    #     serializer = DayShiftSummarySerializer(data=data, many=True)
    #     serializer.is_valid(raise_exception=True)
    #     return response.Response(serializer.data)

    @action(
        methods=["GET"],
        detail=False,
        url_path="locker-summary",
        url_name="locker-summary",
    )
    def locker_summary(self, request):
        shift_settings_id = request.GET.getlist("shift_id[]", None)
        datetime_given = request.GET.getlist("date[]", None)

        data = []

        if shift_settings_id and len(shift_settings_id) > 0:
            data = sales_query.get_summary_data_per_shift(
                shift_settings_id=shift_settings_id,
                sales=TransactionItemCategory.LOCKER_SALES,
            )

        if datetime_given and len(datetime_given) > 0:
            data = sales_query.get_summary_data_per_day(
                dates=datetime_given, sales=TransactionItemCategory.LOCKER_SALES
            )
            # filtered_data = []
            # for row in data:
            #     data_date = str(row["date"])
            #     if datetime_given != data_date:
            #         continue
            #     filtered_data.append(row)

            # data = filtered_data

        result_data = []
        for row in data:

            item_price = (
                float(row["total_item_price"]) if row["total_item_price"] else 0
            )
            service_charge = (
                float(row["total_service_charge"]) if row["total_service_charge"] else 0
            )
            tax_value = float(row["total_tax_value"]) if row["total_tax_value"] else 0

            total_value = item_price + service_charge + tax_value

            data_exist = False

            for res in result_data:
                if res["duration"] != row["duration"]:
                    continue

                data_exist = True

                res["total_pos_sales"] += (
                    row["count"] if row["count"] is not None else 0
                )
                res["total_transaction"] += total_value

            if data_exist:
                continue

            new_data = {
                "duration": row["duration"],
                "total_transaction": 0,
                "total_pos_sales": total_value,
            }
            new_data["total_pos_sales"] = (
                row["count"] if row["count"] is not None else 0
            )
            new_data["total_transaction"] = total_value

            result_data.append(new_data)

        serializer = LockerSummarySerializer(result_data, many=True)

        return response.Response(serializer.data)

    @action(
        methods=["GET"],
        detail=False,
        url_path="shower-summary",
        url_name="shower-summary",
    )
    def shower_summary(self, request):
        shift_settings_id = request.GET.getlist("shift_id[]", None)
        datetime_given = request.GET.getlist("date[]", None)

        data = []

        if shift_settings_id:
            data = sales_query.get_summary_data_per_shift(
                shift_settings_id=shift_settings_id,
                sales=TransactionItemCategory.SHOWER_SALES,
            )

        if datetime_given:
            data = sales_query.get_summary_data_per_day(
                dates=datetime_given, sales=TransactionItemCategory.SHOWER_SALES
            )
            # filtered_data = []
            # for row in data:
            #     data_date = str(row["date"])
            #     if datetime_given != data_date:
            #         continue
            #     filtered_data.append(row)

            # data = filtered_data

        result_data = []
        for row in data:

            item_price = (
                float(row["total_item_price"]) if row["total_item_price"] else 0
            )
            service_charge = (
                float(row["total_service_charge"]) if row["total_service_charge"] else 0
            )
            tax_value = float(row["total_tax_value"]) if row["total_tax_value"] else 0

            total_value = item_price + service_charge + tax_value

            data_exist = False

            for res in result_data:
                if res["duration"] != row["duration"]:
                    continue

                data_exist = True

                res["total_pos_sales"] += (
                    row["count"] if row["count"] is not None else 0
                )
                res["total_transaction"] += total_value

            if data_exist:
                continue

            new_data = {
                "duration": row["duration"],
                "total_transaction": 0,
                "total_pos_sales": total_value,
            }
            new_data["total_pos_sales"] = (
                row["count"] if row["count"] is not None else 0
            )
            new_data["total_transaction"] = total_value

            result_data.append(new_data)

        serializer = ShowerSummarySerializer(result_data, many=True)

        return response.Response(serializer.data)

    @action(
        methods=["GET"],
        detail=False,
        url_path="merch-summary",
        url_name="merch-summary",
    )
    def merch_summary(self, request):
        shift_settings_id = request.GET.getlist("shift_id[]", None)
        datetime_given = request.GET.getlist("date[]", None)

        data = []

        if shift_settings_id:
            data = sales_query.get_summary_data_per_shift(
                shift_settings_id=shift_settings_id,
                sales=TransactionItemCategory.MERCH_SALES,
            )

        if datetime_given:
            data = sales_query.get_summary_data_per_day(
                dates=datetime_given, sales=TransactionItemCategory.MERCH_SALES
            )
            # filtered_data = []
            # for row in data:
            #     data_date = str(row["date"])
            #     if datetime_given != data_date:
            #         continue
            #     filtered_data.append(row)

            # data = filtered_data

        result_data = []
        for row in data:

            item_price = (
                float(row["total_item_price"]) if row["total_item_price"] else 0
            )
            service_charge = (
                float(row["total_service_charge"]) if row["total_service_charge"] else 0
            )
            tax_value = float(row["total_tax_value"]) if row["total_tax_value"] else 0

            total_value = item_price + service_charge + tax_value

            data_exist = False
            print(row)

            for res in result_data:
                if res["item_name"] != row["item_name"]:
                    continue

                data_exist = True

                res["total_pos_sales"] += (
                    row["count"] if row["count"] is not None else 0
                )
                res["total_transaction"] += total_value

            if data_exist:
                continue

            new_data = {
                "item_name": row["item_name"],
                "total_transaction": 0,
                "total_pos_sales": total_value,
            }
            new_data["total_pos_sales"] = (
                row["count"] if row["count"] is not None else 0
            )
            new_data["total_transaction"] = total_value

            result_data.append(new_data)

        serializer = MerchSummarySerializer(result_data, many=True)

        return response.Response(serializer.data)

    @action(
        methods=["GET"],
        detail=False,
        url_path="transaction-summary",
        url_name="transaction-summary",
    )
    def transaction_summary(self, request):
        shift_settings_id = request.GET.getlist("shift_id[]", None)
        datetime_given = request.GET.getlist("date[]", None)

        data = sales_query.query_transactions(shift_settings_id, datetime_given)

        serializer = TransactionShiftReportSerializer(data=data, many=True)

        serializer.is_valid()

        return response.Response(serializer.data)

    # get Top 5 nationalities of visitors by day/week/month
    # input is: start_date, end_date, lot_id
    @swagger_auto_schema(
        operation_description="Get Top nationalities of visitors by day/week/month",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in format YYYY-MM-DD HH:MM:SS",
            ),
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot ID to filter results (optional)",
                required=False,
                example=1
            ),
        ],
        responses={200: TopNationalitySerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="top-nationalities",
        url_name="top-nationalities",
        authentication_classes=[JWTAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def get_top_nationalities(self, request):
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        lot_id = request.GET.get("lot_id")

        #print(start_date, end_date, lot_id)
        print(start_date, end_date, lot_id)
        with connection.cursor() as cursor:
            data = aggregate_query.fetch_top_nationalities(
                cursor=cursor,
                start_date=start_date,
                end_date=end_date,
                top_n=20,
                lot_id=lot_id
            )
            serializer = TopNationalitySerializer(data=data, many=True)
            serializer.is_valid(raise_exception=True)
        return response.Response(serializer.data)

    # ------------------------------ End Shift Summary API ------------------------------ #

class InvoiceQueryView(viewsets.ViewSet):
    authentication_classes = [InvoiceAPIKeyAuthentication]
    permission_classes = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        print("InvoiceQueryView initialized")

    @swagger_auto_schema(
        operation_description="Get invoices by date range with pagination",
        manual_parameters=[
            openapi.Parameter(
                name="start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Start date in DD-MM-YYYY format",
                required=True,
                example="01-03-2024"
            ),
            openapi.Parameter(
                name="end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="End date in DD-MM-YYYY format",
                required=True,
                example="31-03-2024"
            ),
            openapi.Parameter(
                name="lot_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Lot ID to filter results (optional)",
                required=False,
                example=1
            ),
            openapi.Parameter(
                name="page",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Page number",
                required=False,
                example=1
            ),
            openapi.Parameter(
                name="page_size",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Number of items per page",
                required=False,
                example=20
            ),
            openapi.Parameter(
                name="invoice_no",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Invoice number(s) to filter results. Accepts a single invoice number or multiple comma-separated values (e.g., INV-001,INV-002)",
                required=False,
                example="INV-20240301-001,INV-20240301-002"
            ),
            openapi.Parameter(
                name="source",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Platform source(s) to filter results. Accepts a single source or multiple comma-separated values (e.g., 'Booking.com,Walk-in,OTA')",
                required=False,
                example="Booking.com,Walk-in"
            ),
            openapi.Parameter(
                name="consolidate_start_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Consolidate start date in DD-MM-YYYY format (optional, defaults to start_date if not provided). Must be less than or equal to start_date. Used to limit the search for original paid transactions when processing refunds.",
                required=False,
                example="01-02-2024"
            ),
            openapi.Parameter(
                name="consolidate_end_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Consolidate end date in DD-MM-YYYY format (optional, defaults to end_date if not provided). Must be greater than or equal to end_date. Used to limit the search for original paid transactions when processing refunds.",
                required=False,
                example="31-03-2024"
            ),
        ],
        responses={
            200: openapi.Response(
                description="Paginated list of invoices",
                schema=DefaultPageNumberPagination().get_paginated_response_schema(InvoiceResponseSerializer(many=True).data)
            ),
            400: openapi.Response(
                description="Bad request",
                examples={"application/json": {"error": "Both start_date and end_date are required"}}
            ),
            401: openapi.Response(
                description="Unauthorized",
                examples={"application/json": {"error": "Invalid API key"}}
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={"application/json": {"error": "An unexpected error occurred while processing your request."}}
            )
        }
    )
    @action(detail=False, methods=['get'], url_path='by-date')
    def get_invoices_by_date(self, request):
        try:
            start_date = request.GET.get('start_date')
            end_date = request.GET.get('end_date')
            lot_id = request.GET.get('lot_id')

            # Get consolidate date range (optional)
            consolidate_start_date = request.GET.get('consolidate_start_date')
            consolidate_end_date = request.GET.get('consolidate_end_date')

            # Accept multiple invoice_no values (comma-separated or repeated params)
            invoice_no_param = request.GET.get('invoice_no')
            invoice_no_list = request.GET.getlist('invoice_no')
            invoice_nos = []
            if invoice_no_param:
                invoice_nos = [x.strip() for x in invoice_no_param.split(',') if x.strip()]
            elif invoice_no_list:
                invoice_nos = [x.strip() for x in invoice_no_list if x.strip()]

            # Get source filter
            source_param = request.GET.get('source')
            source_list = request.GET.getlist('source')
            sources = []
            if source_param:
                sources = [x.strip() for x in source_param.split(',') if x.strip()]
            elif source_list:
                sources = [x.strip() for x in source_list if x.strip()]

            if not start_date or not end_date:
                return Response(
                    {"error": "Both start_date and end_date are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Convert string dates from DD-MM-YYYY to datetime objects
            try:
                # Parse DD-MM-YYYY format and make timezone-aware
                start_date = datetime.strptime(start_date, '%d-%m-%Y')
                end_date = datetime.strptime(end_date, '%d-%m-%Y')

                # Set start_date to beginning of day (00:00:00)
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                # Set end_date to end of day (23:59:59)
                end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

                # Convert from Asia/Kuala_Lumpur to UTC by subtracting 8 hours
                start_date = start_date - timedelta(hours=8)
                end_date = end_date - timedelta(hours=8)

                # Make both datetimes timezone-aware
                start_date = timezone.make_aware(start_date)
                end_date = timezone.make_aware(end_date)

            except ValueError as e:
                return Response(
                    {"error": "Invalid date format. Use DD-MM-YYYY"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if start_date is not more a month ago
            limit_date = timezone.now() - timedelta(days=45)
            if start_date < limit_date:
                return Response(
                    {"error": "Start date cannot be more than 45 days ago"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Set consolidate date range - both must be provided or neither
            if consolidate_start_date and consolidate_end_date:
                try:
                    # Parse DD-MM-YYYY format and make timezone-aware
                    consolidate_start_date = datetime.strptime(consolidate_start_date, '%d-%m-%Y')
                    consolidate_end_date = datetime.strptime(consolidate_end_date, '%d-%m-%Y')

                    # Set consolidate_start_date to beginning of day (00:00:00)
                    consolidate_start_date = consolidate_start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                    # Set consolidate_end_date to end of day (23:59:59)
                    consolidate_end_date = consolidate_end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

                    # Convert from Asia/Kuala_Lumpur to UTC by subtracting 8 hours
                    consolidate_start_date = consolidate_start_date - timedelta(hours=8)
                    consolidate_end_date = consolidate_end_date - timedelta(hours=8)

                    # Make both datetimes timezone-aware
                    consolidate_start_date = timezone.make_aware(consolidate_start_date)
                    consolidate_end_date = timezone.make_aware(consolidate_end_date)

                    # Validate that consolidate range encompasses the main date range
                    if consolidate_start_date > start_date:
                        return Response(
                            {"error": "consolidate_start_date must be less than or equal to start_date"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    if consolidate_end_date < end_date:
                        return Response(
                            {"error": "consolidate_end_date must be greater than or equal to end_date"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                except ValueError as e:
                    return Response(
                        {"error": "Invalid consolidate date format. Use DD-MM-YYYY"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            elif consolidate_start_date or consolidate_end_date:
                # Only one consolidate date provided - raise error
                return Response(
                    {"error": "Both consolidate_start_date and consolidate_end_date must be provided together"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                # If consolidate dates not provided, use same as start_date and end_date
                consolidate_start_date = start_date
                consolidate_end_date = end_date

            queryset = Transaction.objects.select_related(
                'customer',
                'customer__country',
                'currency',
                'booking',
                'booking__platform',
                'payment_type',
                'payment_type__payment_method',
            ).filter(
                booking__booking_status__is_latest=True,
                transaction_datetime__range=(start_date, end_date),
                transaction_status__in=['Paid', 'Refund'],
            ).exclude(
                # exclude booking statuses: Cancelled, Transfer Booking
                booking__booking_status__booking_status__in=['Cancelled']
            ).exclude(
                # exclude Payment methods that start with "Transfer To"
                payment_type__payment_method__payment_method__startswith='Transfer To'
            ).exclude(
                # exclude Payment method : Recovery services, Complimentary, paylater
                payment_type__payment_method__payment_method__in=['Recovery Services', 'Complimentary', 'Paylater']
            )

            # Apply lot_id filter if provided
            if lot_id:
                queryset = queryset.filter(booking__lot_id=lot_id)

            if invoice_nos:
                queryset = queryset.filter(invoice_no__in=invoice_nos)

            if sources:
                queryset = queryset.filter(booking__platform__platform__in=sources)
            queryset = queryset.order_by('-transaction_datetime')

            # Execute the queryset to get the actual data
            transactions_list = list(queryset)

            # Handle refund transactions by finding original paid transactions and zeroing them out
            processed_transactions = []
            processed_refund_ids = set()
            processed_paid_ids = set()

            # Create lookup dictionaries from the existing transactions list
            # Key: (booking_id, debit_amount), Value: transaction
            paid_transactions_lookup = {}
            refund_transactions_lookup = {}
            
            for tx in transactions_list:
                if tx.transaction_status == 'Paid':
                    key = (tx.booking_id, tx.debit_amount)
                    paid_transactions_lookup[key] = tx
                elif tx.transaction_status == 'Refund':
                    key = (tx.booking_id, abs(tx.debit_amount))
                    refund_transactions_lookup[key] = tx

            # Collect queries to avoid N+1 problem
            refund_queries = []
            paid_refund_queries = []
            
            for transaction in transactions_list:
                if transaction.transaction_status == 'Refund' and transaction.transaction_id not in processed_refund_ids:
                    # Case 1: Both paid and refund in same date range - remove both
                    # Case 3: Only refund in date range - find original and zero it out
                    lookup_key = (transaction.booking_id, abs(transaction.debit_amount))
                    original_transaction = paid_transactions_lookup.get(lookup_key)

                    if original_transaction:
                        # Case 1: Both paid and refund in same date range - remove both
                        processed_refund_ids.add(transaction.transaction_id)
                        processed_paid_ids.add(original_transaction.transaction_id)
                        # Don't add either transaction to processed_transactions (remove both)
                    else:
                        # Case 3: Only refund in date range - collect query for later execution
                        refund_queries.append({
                            'transaction': transaction,
                            'booking_id': transaction.booking_id,
                            'debit_amount': abs(transaction.debit_amount)
                        })
                elif transaction.transaction_status == 'Paid':
                    # Case 2: Paid transaction with refunded=True - check for refund in same date range first, then consolidate range
                    if transaction.refunded and transaction.transaction_id not in processed_paid_ids:
                        # First check if there's a refund transaction in the same date range
                        refund_transaction_same_range = Transaction.objects.filter(
                            booking_id=transaction.booking_id,
                            transaction_status='Refund',
                            debit_amount=-transaction.debit_amount,  # refund amount is negative
                            transaction_datetime__range=(start_date, end_date)
                        ).first()
                        
                        if refund_transaction_same_range:
                            # Found refund in same date range - remove both (don't add either)
                            processed_paid_ids.add(transaction.transaction_id)
                            processed_refund_ids.add(refund_transaction_same_range.transaction_id)
                            # Don't add the paid transaction to processed_transactions
                        else:
                            # No refund in same date range, collect query for consolidate range check
                            paid_refund_queries.append({
                                'transaction': transaction,
                                'booking_id': transaction.booking_id,
                                'debit_amount': -transaction.debit_amount
                            })
                    elif transaction.transaction_id not in processed_paid_ids:
                        # Add non-refunded paid transactions
                        processed_transactions.append(transaction)
                else:
                    # Add other transactions (non-refund, non-paid)
                    processed_transactions.append(transaction)

            # Execute refund queries in batch
            if refund_queries:
                refund_query_conditions = []
                # it will not be more than 100 queries, no need to limit batch size
                for query in refund_queries:
                    refund_query_conditions.append(
                        Q(booking_id=query['booking_id']) &
                        Q(transaction_status='Paid') &
                        Q(debit_amount=query['debit_amount']) &
                        Q(refunded=True)
                    )
                
                original_transactions = Transaction.objects.filter(
                    reduce(lambda x, y: x | y, refund_query_conditions) &
                    Q(transaction_datetime__range=(consolidate_start_date, consolidate_end_date))
                ).select_related(
                    'customer',
                    'customer__country',
                    'currency',
                    'booking',
                    'booking__platform',
                    'payment_type',
                    'payment_type__payment_method',
                )

                # Create lookup for found original transactions
                original_lookup = {}
                for orig_tx in original_transactions:
                    key = (orig_tx.booking_id, orig_tx.debit_amount)
                    original_lookup[key] = orig_tx

                # Process refund transactions with found original transactions
                for query in refund_queries:
                    transaction = query['transaction']
                    lookup_key = (query['booking_id'], query['debit_amount'])
                    original_transaction = original_lookup.get(lookup_key)

                    if original_transaction:
                        # Create a copy of the original transaction with zeroed amounts
                        zeroed_transaction = copy.copy(original_transaction)
                        
                        # Zero out monetary amounts
                        zeroed_transaction.sum = 0
                        zeroed_transaction.tax_amount = 0
                        zeroed_transaction.service_charge_amount = 0
                        zeroed_transaction.adjustements_amount = 0
                        zeroed_transaction.promotion_amount = 0
                        zeroed_transaction.credit_amount = 0
                        zeroed_transaction.debit_amount = 0
                        zeroed_transaction.rounding = 0
                        
                        processed_transactions.append(zeroed_transaction)
                        processed_refund_ids.add(transaction.transaction_id)
                    else:
                        # If no original transaction found, keep the refund transaction as is
                        processed_transactions.append(transaction)

            # Execute paid refund queries in batch
            if paid_refund_queries:
                paid_refund_query_conditions = []
                # it will not be more than 100 queries, no need to limit batch size
                for query in paid_refund_queries:
                    paid_refund_query_conditions.append(
                        Q(booking_id=query['booking_id']) &
                        Q(transaction_status='Refund') &
                        Q(debit_amount=query['debit_amount'])
                    )
                
                refund_transactions = Transaction.objects.filter(
                    reduce(lambda x, y: x | y, paid_refund_query_conditions) &
                    Q(transaction_datetime__range=(consolidate_start_date, consolidate_end_date))
                )

                # Create lookup for found refund transactions
                refund_lookup = {}
                for refund_tx in refund_transactions:
                    key = (refund_tx.booking_id, refund_tx.debit_amount)
                    refund_lookup[key] = refund_tx

                # Process paid transactions with found refund transactions
                for query in paid_refund_queries:
                    transaction = query['transaction']
                    lookup_key = (query['booking_id'], query['debit_amount'])
                    refund_transaction = refund_lookup.get(lookup_key)

                    if refund_transaction:
                        # Found refund in consolidate range - remove both (don't add either)
                        processed_paid_ids.add(transaction.transaction_id)
                        processed_refund_ids.add(refund_transaction.transaction_id)
                        # Don't add the paid transaction to processed_transactions
                    else:
                        # No refund found in consolidate range - keep the paid transaction
                        processed_transactions.append(transaction)

            # Reorder processed transactions by transaction_datetime (descending) to maintain original sorting
            processed_transactions.sort(key=lambda x: x.transaction_datetime, reverse=True)

            # Paginate the processed transactions
            paginator = DefaultPageNumberPagination()
            paginated_qs = paginator.paginate_queryset(processed_transactions, request)

            # Transform and serialize
            invoice_data = [invoice.transform_transaction_to_invoice(tx) for tx in paginated_qs]
            serializer = InvoiceResponseSerializer(invoice_data, many=True)

            return paginator.get_paginated_response(serializer.data)

        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred while processing your request."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
