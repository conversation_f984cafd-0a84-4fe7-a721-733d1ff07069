def map_to_payment_mode(payment_type: str, payment_method: str) -> str:
    """
    Maps payment type and payment method to payment mode
    Returns payment mode code (01-08) based on payment type and method
    
    Payment Mode values:
    01 - Cash
    02 - Cheque
    03 - Bank Transfer
    04 - Credit Card
    05 - Debit Card
    06 - e-Wallet/Digital Wallet
    07 - Digital Bank
    08 - Others
    """
    # First check payment method
    if payment_method == "Cash":
        return "01"
    elif payment_method == "Bank Transfer":
        return "03"
    elif payment_method == "E-Wallet":
        return "06"
    elif payment_method == "Card":
        # For Card payment method, check payment type to determine if it's credit or debit
        if payment_type in ["Visa Card", "Master Card", "AMEX", "JCB", "Union Pay"]:
            return "04"  # Credit Card
        elif payment_type == "Debit Card":
            return "05"  # Debit Card
    elif payment_method == "iPay88":
        return "07"  # Digital Bank
    
    # Default to Others
    return "08"
