import datetime
from typing import Union
from bookings.models import RoomBooking
from django.db.models import Count, F
from django.db import connection
from capsuletransit.utils import dictfetchall


def query_room_occupancy_by_date_range(
    lot_id: int,
    start_date: Union[str, datetime.datetime],
    end_date: Union[str, datetime.datetime],
):
    query = """
		select count(br.room_id) as total, rr2.type_name as room_type_name from bookings_roombooking br 
		inner join bookings_booking bb 
			on br.booking_id  = bb.booking_id 
		inner join bookings_bookingstatus bb2 
			on bb2.booking_id  = br.booking_id 

		inner join rooms_room rr 
			on rr.room_id  = br.room_id 
		inner join rooms_roomtype rr2 
			on rr2.type_id = rr.room_type_id 
		inner join rooms_roomzone rrz
			on rrz.zone_id = rr2.roomzone_id			
		inner join lot_lot ll 
			on ll.lot_id = rrz.lot_id_id	
			where 
				bb2.is_latest = true 
				and 
				bb2.check_in_datetime >= %s
					and 
				bb2.check_in_datetime <= %s
				and 
					ll.lot_id = %s
			group by rr2.type_name  

	"""
    cursor = connection.cursor()
    cursor.execute(query, [start_date, end_date, lot_id])
    results = dictfetchall(cursor)
    return results


def query_total_room_book_by_platform(
    start_date: Union[str, datetime.datetime],
    end_date: Union[str, datetime.datetime],
    lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    query = """
    WITH UniqueTransactions AS (
        SELECT DISTINCT
            br.room_booking_id,
            bp.platform_id,
            bp.platform AS platform_name,
            bp.color_tags AS color_tags
        FROM 
            bookings_roombooking br
        INNER JOIN 
            bookings_booking bb ON br.booking_id = bb.booking_id      
        INNER JOIN 
            bookings_bookingstatus bb2 ON bb2.booking_id = br.booking_id     
        INNER JOIN 
            rooms_room rr ON rr.room_id = br.room_id
        INNER JOIN 
            rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id       
        INNER JOIN 
            rooms_roomzone rrz ON rrz.zone_id = rr2.roomzone_id       
        INNER JOIN 
            lot_lot ll ON ll.lot_id = rrz.lot_id_id
        INNER JOIN 
            bookings_platform bp ON bp.platform_id = bb.platform_id
        INNER JOIN
            transaction_transaction tt ON bb.booking_id = tt.booking_id
        WHERE
            bb2.is_latest = TRUE
            AND (
                (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
                OR
                (br.actual_checkin_date_time IS NULL AND (bb2.is_latest = TRUE AND bb2.check_in_datetime BETWEEN %s AND %s))
                OR
                (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
            )
            AND (%s IS NULL OR ll.lot_id = %s)
            AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
            AND rr2.type_name != 'Lounge'
            AND tt.transaction_status != 'Refund'
    )
    SELECT 
        COUNT(room_booking_id) AS total_booking,
        platform_id, 
        platform_name, 
        color_tags
    FROM 
        UniqueTransactions
    GROUP BY 
        platform_id, 
        platform_name, 
        color_tags
    ORDER BY 
        total_booking DESC;
   
    """
    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor = connection.cursor()
    cursor.execute(query, params)

    results = dictfetchall(cursor)
    return results


# TODO: Implement Room Booking Actual Check in Check
def get_room_occupancy_by_roombooking(
    cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    sql = """
    SELECT 
        rr2.type_id, 
        rr2.type_name, 
        rr3.zone_id, 
        rr3.zone_name, 
        COUNT(*)
    FROM 
        bookings_roombooking br
    INNER JOIN 
        bookings_booking bb ON bb.booking_id = br.booking_id 
    INNER JOIN 
        bookings_roombooking br2 ON bb.booking_id = br2.booking_id 
    INNER JOIN 
        bookings_bookingstatus bb2 ON bb.booking_id = bb2.booking_id
    INNER JOIN 
        rooms_room rr ON rr.room_id = br.room_id 
    INNER JOIN 
        rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
    INNER JOIN 
        rooms_roomzone rr3 ON rr2.roomzone_id = rr3.zone_id 
    INNER JOIN 
        lot_lot ll ON ll.lot_id = rr3.lot_id_id
    INNER JOIN
        transaction_transaction tt ON tt.booking_id = bb.booking_id  
    WHERE 
        bb2.is_latest = TRUE
        AND rr2.type_name != 'Lounge'
        AND (
            (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
            OR
            (br.actual_checkin_date_time IS NULL AND bb2.check_in_datetime BETWEEN %s AND %s)
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
        )
        AND (%s IS NULL OR ll.lot_id = %s)
    GROUP BY 
        rr2.type_id, 
        rr3.zone_id;
    """

    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor.execute(sql, params)
    results = dictfetchall(cursor)
    return results


# TODO: Implement Room Booking Actual Check in Check
def get_room_occupancy_by_roombooking_with_checkin(
    cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    sql = """
    SELECT 
        rr2.type_id, 
        rr2.type_name, 
        rr3.zone_id, 
        rr3.zone_name, 
        COUNT(DISTINCT br2.room_booking_id)
    FROM 
        bookings_roombooking br
    INNER JOIN 
        bookings_booking bb ON bb.booking_id = br.booking_id 
    INNER JOIN 
        bookings_roombooking br2 ON bb.booking_id = br2.booking_id 
    INNER JOIN 
        bookings_bookingstatus bb2 ON bb.booking_id = bb2.booking_id
    INNER JOIN 
        rooms_room rr ON rr.room_id = br.room_id 
    INNER JOIN 
        rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
    INNER JOIN 
        rooms_roomzone rr3 ON rr2.roomzone_id = rr3.zone_id 
    INNER JOIN 
        lot_lot ll ON ll.lot_id = rr3.lot_id_id
    INNER JOIN
        transaction_transaction tt ON tt.booking_id = bb.booking_id   
    WHERE 
        bb2.is_latest = TRUE
        AND rr2.type_name != 'Lounge'
        AND (
            (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
            OR
            (br.actual_checkin_date_time IS NULL AND bb2.check_in_datetime BETWEEN %s AND %s)
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
        )
        AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
        AND (%s IS NULL OR ll.lot_id = %s)
    GROUP BY 
        rr2.type_id, 
        rr3.zone_id;
    """

    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor.execute(sql, params)
    results = dictfetchall(cursor)
    return results


def get_pax_count_checkin(
    cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    query = """
    WITH UniqueTransactions AS (
        SELECT DISTINCT
            bb.booking_id
        FROM bookings_booking bb
        INNER JOIN bookings_bookingstatus bb2 ON bb.booking_id = bb2.booking_id
        INNER JOIN bookings_roombooking br ON bb.booking_id = br.booking_id
        INNER JOIN rooms_room rr ON rr.room_id = br.room_id
        INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
        INNER JOIN transaction_transaction tt ON tt.booking_id = bb.booking_id
        WHERE rr2.type_name != 'Lounge'
        AND bb2.is_latest = TRUE
        AND (
            (br.actual_checkin_date_time IS NOT NULL 
            AND br.actual_checkin_date_time BETWEEN %s AND %s)
            OR
            (br.actual_checkin_date_time IS NULL 
            AND bb2.check_in_datetime BETWEEN %s AND %s)
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
        )
        AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
        AND (%s IS NULL OR bb.lot_id = %s)
    )
    SELECT COUNT(DISTINCT br.room_booking_id)
    FROM bookings_roombooking br
    INNER JOIN UniqueTransactions ut ON br.booking_id = ut.booking_id;
    """

    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor.execute(query, params)
    results = dictfetchall(cursor)
    return results


def get_pax_count_lounge(
    cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    query = """
    WITH UniqueTransactions AS (
        SELECT DISTINCT
            bb.booking_id
        FROM bookings_booking bb
        INNER JOIN bookings_bookingstatus bb2 ON bb.booking_id = bb2.booking_id
        INNER JOIN bookings_roombooking br ON bb.booking_id = br.booking_id
        INNER JOIN rooms_room rr ON rr.room_id = br.room_id
        INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
        INNER JOIN transaction_transaction tt ON tt.booking_id = bb.booking_id
        WHERE rr2.type_name = 'Lounge'
        AND (
            (br.actual_checkin_date_time IS NOT NULL 
            AND br.actual_checkin_date_time BETWEEN %s AND %s)
            OR
            (br.actual_checkin_date_time IS NULL 
            AND bb2.check_in_datetime BETWEEN %s AND %s)
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
        )
        AND bb2.is_latest = TRUE
        AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
        AND (%s IS NULL OR bb.lot_id = %s)
    )
    SELECT COUNT(DISTINCT br.room_booking_id)
    FROM bookings_roombooking br
    INNER JOIN UniqueTransactions ut ON br.booking_id = ut.booking_id;
    """

    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor.execute(query, params)
    results = dictfetchall(cursor)
    return results


def get_pax_count_noshow(
    cursor, start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == '':
        lot_id = None
    query = """
    WITH UniqueTransactions AS (
        SELECT DISTINCT
            bb.booking_id
        FROM bookings_booking bb
        INNER JOIN bookings_bookingstatus bb2 ON bb.booking_id = bb2.booking_id
        INNER JOIN bookings_roombooking br ON bb.booking_id = br.booking_id
        INNER JOIN rooms_room rr ON rr.room_id = br.room_id
        INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
        INNER JOIN transaction_transaction tt ON tt.booking_id = bb.booking_id
        WHERE rr2.type_name != 'Lounge'
        AND (
            (br.actual_checkin_date_time IS NOT NULL 
            AND br.actual_checkin_date_time BETWEEN %s AND %s)
            OR
            (br.actual_checkin_date_time IS NULL 
            AND bb2.check_in_datetime BETWEEN %s AND %s)
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
        )
        AND bb2.is_latest = TRUE
        AND bb2.booking_status = 'No Show'
        AND (%s IS NULL OR bb.lot_id = %s)
    )
    SELECT COUNT(DISTINCT br.room_booking_id)
    FROM bookings_roombooking br
    INNER JOIN UniqueTransactions ut ON br.booking_id = ut.booking_id;
    """

    params = [start_date, end_date, start_date, end_date, start_date, end_date, lot_id, lot_id]

    cursor.execute(query, params)
    results = dictfetchall(cursor)
    return results


def get_rooms_list_zone_lot(cursor, lot_id: int = None):
    if lot_id == '':
        lot_id = None
    query = """
	SELECT 
		rr2.type_id, 
		rr2.roomzone_id, 
		rr3.zone_name, 
		rr2.type_name, 
		rr2.color_tags, 
		COUNT(*)
	FROM 
		rooms_room rr
	INNER JOIN 
		rooms_roomtype rr2 ON rr.room_type_id = rr2.type_id
	INNER JOIN 
		rooms_roomzone rr3 ON rr2.roomzone_id = rr3.zone_id
	INNER JOIN 
		lot_lot ll ON ll.lot_id = rr3.lot_id_id
	WHERE 
		(%s IS NULL OR ll.lot_id = %s)
		AND rr2.type_name != 'Lounge'
		AND rr.is_archived = FALSE
	GROUP BY 
		rr2.type_id, 
		rr2.roomzone_id, 
		rr3.zone_name, 
		rr2.type_name, 
		rr2.color_tags;
	"""
    cursor.execute(query, [lot_id, lot_id])
    results = dictfetchall(cursor)
    return results
