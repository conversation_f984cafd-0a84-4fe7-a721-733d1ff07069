from rest_framework import serializers


# Create your views here.


class RoomBookingCountyByRoomTypeSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    room_type_name = serializers.CharField()


class RoomBookingCountByPlatformSerializer(serializers.Serializer):
    total_booking = serializers.IntegerField()
    platform_id = serializers.UUIDField()
    platform_name = serializers.CharField()
    color_tags = serializers.CharField(default="#fffff")


class RoomRevenueSerializer(serializers.Serializer):
    value = serializers.DecimalField(max_digits=16, decimal_places=2)
    color_code = serializers.CharField()
    item_type = serializers.CharField()


class DashboardRevenueSerializer(serializers.Serializer):
    locker_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )
    shower_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )
    pos_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )
    room_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )
    lounge_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )
    misc_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, allow_null=True, default=0
    )


class StaffShiftSerializer(serializers.Serializer):
    shift_id = serializers.UUIDField()
    account_id = serializers.UUIDField()
    name = serializers.CharField()
    shift_name = serializers.CharField()
    start_hour = serializers.DateTimeField()
    end_hour = serializers.DateTimeField(required=False, default=None, allow_null=True)
    total_item_price = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_service_charge = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_tax_value = serializers.DecimalField(max_digits=16, decimal_places=2)

    class Meta:
        ref_name = "staffshiftserializeronqueryservice"


class EndShiftReportOverviewItem(serializers.Serializer):
    name = serializers.CharField()
    value = serializers.IntegerField()


class EndShiftReportOverviewSerializer(serializers.Serializer):
    left = EndShiftReportOverviewItem()
    right = EndShiftReportOverviewItem()


class SalesSummaryaByDateSerializer(serializers.Serializer):
    total = serializers.DecimalField(max_digits=16, decimal_places=2)
    date = serializers.DateField()


class SalesSummary(serializers.Serializer):
    month_to_date_sales = serializers.DecimalField(max_digits=16, decimal_places=2)
    average_sales = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_sales = serializers.DecimalField(max_digits=16, decimal_places=2)


class SalesSummaryaByDateSerializerV2(serializers.Serializer):
    yesterday = SalesSummary()
    today = SalesSummary()
    tomorrow = SalesSummary()


# class SalesShiftSummarySerializer(serializers.Serializer):
#     shift_id = serializers.UUIDField()
#     total_item_price = serializers.DecimalField(max_digits=16, decimal_places=2)
#     total_service_charge = serializers.DecimalField(max_digits=16, decimal_places=2)
#     total_tax_value = serializers.DecimalField(max_digits=16, decimal_places=2)
#     item_type = serializers.CharField()
#     duration = serializers.IntegerField()


# class RoomSummarySerializer(serializers.Serializer):


class LockerSummarySerializer(serializers.Serializer):
    duration = serializers.IntegerField()
    total_transaction = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_pos_sales = serializers.IntegerField(default=0)


class ShowerSummarySerializer(serializers.Serializer):
    duration = serializers.IntegerField()
    total_transaction = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_pos_sales = serializers.IntegerField(default=0)


class MerchSummarySerializer(serializers.Serializer):
    item_name = serializers.CharField()
    total_transaction = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_pos_sales = serializers.IntegerField(default=0)


class ShiftSummaryByItem(serializers.Serializer):
    item_name = serializers.CharField()
    sales = serializers.DecimalField(max_digits=16, decimal_places=2)


class RoomSalesShiftDurationGroupItem(serializers.Serializer):
    duration = serializers.IntegerField(help_text="Duration group")
    item_sales = ShiftSummaryByItem(many=True)
    count = serializers.IntegerField()


class RoomShiftReportSerializer(serializers.Serializer):
    # shift_id = serializers.UUIDField(help_text="Account sShift ID")
    columns = serializers.ListField()
    duration_groups = RoomSalesShiftDurationGroupItem(many=True)


class PaymentCollectedSerializer(serializers.Serializer):
    payment_type_id = serializers.IntegerField()
    payment_type = serializers.CharField()
    total = serializers.DecimalField(max_digits=16, decimal_places=2)


class TransactionShiftReportSerializer(serializers.Serializer):
    booking_id = serializers.CharField()
    booking_date = serializers.CharField()
    transaction_date = serializers.CharField()
    staff = serializers.CharField()
    payment_type = serializers.CharField()
    pan = serializers.CharField()
    payment_reference = serializers.CharField()
    payment_remarks = serializers.CharField()
    transaction_status = serializers.CharField()
    booking_status = serializers.CharField()
    check_in_date = serializers.CharField()
    check_out_date = serializers.CharField()
    ota_ref_code = serializers.CharField()
    booking_platform = serializers.CharField()
    guest = serializers.CharField()
    duration = serializers.CharField(required=False, default="")
    sales = serializers.DecimalField(max_digits=16, decimal_places=2, default=0)
    tax = serializers.DecimalField(max_digits=16, decimal_places=2, default=0)
    promotion = serializers.DecimalField(max_digits=16, decimal_places=2, default=0)
    adjustment = serializers.DecimalField(max_digits=16, decimal_places=2, default=0)
    total = serializers.DecimalField(max_digits=16, decimal_places=2, default=0)
    booking_remarks = serializers.CharField(required=False, default="")


class RoomOccupanyDetailSerializer(serializers.Serializer):
    total_occupied_room = serializers.IntegerField()
    total_room_booked = serializers.IntegerField()
    total_room = serializers.IntegerField()
    room_type_name = serializers.CharField()
    color_code = serializers.CharField()


class RoomOccupanyCountRevenueSerializer(serializers.Serializer):
    total_occupied_room = serializers.IntegerField(default=0)
    total_room = serializers.IntegerField(default=0)
    guest_stayed = serializers.IntegerField(default=0)
    stayed_total_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, default=0
    )
    no_show_guests = serializers.IntegerField(default=0)
    no_show_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, default=0
    )
    lounge_guests = serializers.IntegerField(default=0)
    lounge_revenue = serializers.DecimalField(
        max_digits=16, decimal_places=2, default=0
    )
    # details = serializers.ListSerializer(child=RoomOccupanyDetailSerializer())
    details = RoomOccupanyDetailSerializer(many=True)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if "guest_stayed" in data and data["guest_stayed"] is None:
            data["guest_stayed"] = 0
        return data


class StaffShiftWithOverview(serializers.Serializer):
    data = StaffShiftSerializer(many=True)
    overview = EndShiftReportOverviewSerializer(many=True)
    # details = TransactionShiftReportSerializer(many=True)


class DayShiftSerializer(serializers.Serializer):
    date = serializers.DateField()
    shift_count = serializers.IntegerField()
    total_item_price = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_service_charge = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_tax_value = serializers.DecimalField(max_digits=16, decimal_places=2)


class DayShiftWithOverview(serializers.Serializer):
    data = DayShiftSerializer(many=True)
    overview = EndShiftReportOverviewSerializer(many=True)
    # details = TransactionShiftReportSerializer(many=True)


class TopNationalitySerializer(serializers.Serializer):
    country_name = serializers.CharField()
    visitor_count = serializers.IntegerField()
    visitor_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)


class RoomRevenueByPlatformSerializer(serializers.Serializer):
    platform_id = serializers.UUIDField()
    total_revenue = serializers.DecimalField(max_digits=16, decimal_places=2)


class InvoiceItemSerializer(serializers.Serializer):
    item = serializers.CharField()
    classification = serializers.CharField()
    description = serializers.CharField()
    quantity = serializers.IntegerField()
    unitCost = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    uom = serializers.CharField()
    taxType = serializers.CharField()
    taxRate = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    discount = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    discountRate = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    tax = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    taxExemptionDetails = serializers.CharField(required=False, allow_null=True)
    taxExemptionAmount = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    chargeRate = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    chargeAmount = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    productTariffCode = serializers.CharField(required=False, allow_null=True)
    originCountry = serializers.CharField(required=False, allow_null=True)
    subTotal = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    subTotalWithoutTax = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    subTotalWithTax = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    total = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    invoiceVersion = serializers.IntegerField()
    poNo = serializers.CharField(required=False, allow_null=True)


class TaxDetailSerializer(serializers.Serializer):
    totalTaxableAmountPerTaxType = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    totalAmountPerTaxType = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    type = serializers.CharField()
    taxExemptionReason = serializers.CharField(required=False, allow_null=True)


class InvoiceResponseSerializer(serializers.Serializer):
    # Billing Fields
    docNo = serializers.CharField()
    type = serializers.CharField(default='invoice')
    customerId = serializers.CharField()
    status = serializers.CharField()
    currency = serializers.CharField(allow_null=True)
    createdAt = serializers.DateTimeField()
    dueAt = serializers.DateTimeField(required=False, allow_null=True)
    paidAt = serializers.DateTimeField(required=False, allow_null=True)
    referenceDocNo = serializers.CharField(required=False, allow_null=True)

    # Exchange Rate Fields
    exchange = serializers.DictField(required=False, allow_null=True)

    # Billing Frequency
    billingFrequency = serializers.CharField(required=False, allow_null=True)

    # Payment Terms
    paymentTerms = serializers.CharField(required=False, allow_null=True)
    prePaymentAmount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    prePaymentAt = serializers.DateTimeField(required=False, allow_null=True)
    prePaymentReferenceNumber = serializers.CharField(required=False, allow_null=True)

    # Billing Period
    billingPeriodStartDate = serializers.DateTimeField(required=False, allow_null=True)
    billingPeriodEndDate = serializers.DateTimeField(required=False, allow_null=True)

    # Payment Mode
    paymentMode = serializers.CharField(required=False, allow_null=True)
    billReference = serializers.CharField(required=False, allow_null=True)

    # Amount Fields
    discount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    additionalDiscount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalDiscount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalCharge = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalTax = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    roundingAmount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    additionalFee = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    subTotal = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    total = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalExclTax = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalNetAmount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalPayable = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalInclTax = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)

    # Additional Fields
    items = InvoiceItemSerializer(many=True, required=False, allow_null=True)
    billingDetail = serializers.DictField(required=False, allow_null=True)
    prePaymentDetail = serializers.DictField(required=False, allow_null=True)
    taxDetails = serializers.ListField(required=False, allow_null=True)
    supplier = serializers.DictField(required=False, allow_null=True)
    recipient = serializers.DictField(required=False, allow_null=True)
    billReferenceNumber = serializers.CharField(required=False, allow_null=True)
    remarks = serializers.CharField(required=False, allow_null=True)
    printType = serializers.CharField(required=False, allow_null=True)
    custom = serializers.DictField(required=False, allow_null=True)
    balance = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalTaxExemption = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalTaxExemptionReason = serializers.CharField(required=False, allow_null=True)
    totalTaxableSalesTax = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    totalTaxableServiceTax = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    additionalDetails = serializers.DictField(required=False, allow_null=True)
    additionChargesDetails = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True, coerce_to_string=False)
    poNo = serializers.CharField(required=False, allow_null=True)
    doNo = serializers.CharField(required=False, allow_null=True)
    source = serializers.CharField(required=False, allow_null=True)

    # Original Transaction Fields
    originalTransaction = serializers.DictField(required=False, allow_null=True)

    def get_originalTransaction(self, obj):
        if not isinstance(obj, dict) or 'originalTransaction' not in obj:
            return None

        originalTransaction = obj['originalTransaction']
        if not originalTransaction:
            return None

        return {
            # Transaction Model Fields
            "transactionId": originalTransaction.get('transactionId'),
            "invoiceNo": originalTransaction.get('invoiceNo'),
            "paymentDetails": originalTransaction.get('paymentDetails'),
            "sum": originalTransaction.get('sum'),
            "taxAmount": originalTransaction.get('taxAmount'),
            "serviceChargeAmount": originalTransaction.get('serviceChargeAmount'),
            "items": originalTransaction.get('items'),
            "transactionStatus": originalTransaction.get('transactionStatus'),
            "transactionDatetime": originalTransaction.get('transactionDatetime'),
            "creditAmount": originalTransaction.get('creditAmount'),
            "debitAmount": originalTransaction.get('debitAmount'),
            "isLatest": originalTransaction.get('isLatest'),
            "isRoomBooking": originalTransaction.get('isRoomBooking'),
            "adjustments": originalTransaction.get('adjustments'),
            "adjustmentsAmount": originalTransaction.get('adjustmentsAmount'),
            "promotionAmount": originalTransaction.get('promotionAmount'),
            "rounding": originalTransaction.get('rounding'),
            "paymentRemarks": originalTransaction.get('paymentRemarks'),
            "paymentReference": originalTransaction.get('paymentReference'),
            "pan": originalTransaction.get('pan'),
            "guestGiven": originalTransaction.get('guestGiven'),
            "guestChange": originalTransaction.get('guestChange'),
            "refunded": originalTransaction.get('refunded'),
            "customerId": originalTransaction.get('customerId'),
            "paymentTypeId": originalTransaction.get('paymentTypeId'),
            "paymentType": originalTransaction.get('paymentType'),
            "bookingId": originalTransaction.get('bookingId'),
            "lotId": originalTransaction.get('lotId'),
            "currencyId": originalTransaction.get('currencyId'),
            "currencyCode": originalTransaction.get('currencyCode'),
            "customer": originalTransaction.get('customer')
        }