import os
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed

class InvoiceAPIKeyAuthentication(BaseAuthentication):
    def authenticate(self, request):
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            raise AuthenticationFailed('No API key provided')
            
        expected_api_key = os.environ.get('INVOICE_API_KEY')
        if not expected_api_key:
            raise AuthenticationFailed('API key not configured')
            
        if api_key != expected_api_key:
            raise AuthenticationFailed('Invalid API key')
            
        # Return None to indicate this is an anonymous request
        # since we're not using user authentication
        return (None, None) 