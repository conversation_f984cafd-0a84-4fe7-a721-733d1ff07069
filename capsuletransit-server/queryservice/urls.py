from django.urls import path
from queryservice import views
from rest_framework.routers import DefaultRouter

app_name = "queryservice"


router = DefaultRouter()
router.register(
    r"v1/room-booking", views.QueryRoomBookingViewSet, basename="query-room-booking"
)
router.register(
    r"v1/sales-summary", views.SalesSummaryQueryView, basename="query-summary"
)
router.register(r"v1/sales", views.EndShiftSalesQueryView, basename="query-sales")
router.register(r"v1/invoices", views.InvoiceQueryView, basename="query-invoice")
urlpatterns = router.urls
