import calendar
from collections import defaultdict
import datetime
import decimal
import json
from typing import <PERSON>ple, Union
import hashlib

# import pytz
from datetime import timedelta, timezone
from capsuletransit.utils import dictfetchall
from django.db import connection
from django.db.models import Sum, Value, DecimalField, Q
from django.db.models.functions import Coalesce
from datetime import datetime as datetime2, time, timedelta

# from dateutil import tz
from django.db.models import Prefetch
from django.core.cache import cache

from constant.enums import TransactionItemCategory
from salesreport.models import SalesTransaction
from rooms.models import RoomType
from accounts.models import Shift
from bookings.models import Booking, BookingStatus, RoomBooking
from transaction.models import Transaction
from transaction.services.transaction import (
    check_need_reverse,
    convert_to_report_format,
)


def cache_sales_results(func):
    """
    Decorator to cache sales function results for 5 minutes
    """

    def wrapper(*args, **kwargs):
        # Create a cache key based on function name and arguments
        cache_key_parts = [func.__name__]

        # Add args and kwargs to cache key
        for arg in args:
            if isinstance(arg, datetime.datetime):
                cache_key_parts.append(arg.isoformat())
            else:
                cache_key_parts.append(str(arg))

        for key, value in sorted(kwargs.items()):
            if isinstance(value, datetime.datetime):
                cache_key_parts.append(f"{key}:{value.isoformat()}")
            else:
                cache_key_parts.append(f"{key}:{str(value)}")

        cache_key = hashlib.md5("|".join(cache_key_parts).encode()).hexdigest()
        cache_key = f"sales_cache_{cache_key}"

        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # If not in cache, execute function and cache result
        result = func(*args, **kwargs)
        cache.set(cache_key, result, 300)  # Cache for 5 minutes

        return result

    return wrapper


# ----------------------------------------END SHIFT PAGE SQL QUERY----------------------------------------#
def staffshift_sales_query(start_date=None, end_date=None, lot=None):
    converted_start_date = (
        datetime2.fromisoformat(start_date[:-1]).strftime("%Y-%m-%d %H:%M:%S")
        if start_date is not None
        else None
    )
    converted_end_date = (
        datetime2.fromisoformat(end_date[:-1]).strftime("%Y-%m-%d %H:%M:%S")
        if end_date is not None
        else None
    )

    query = """
        SELECT
            as2.shift_id,
            aa.account_id,
            aa.name,
            as3.shift_name,
            as2.start_shift_datetime as start_hour,
            as2.end_shift_datetime as end_hour,
            SUM(ss.item_price),
            SUM(ss.service_charge) AS total_service_charge,
            SUM(ss.tax_value) AS total_tax_value,
            SUM(ss.subtotal) AS total_item_price
        FROM
            salesreport_salestransaction ss
        LEFT JOIN
            accounts_shift as2 ON as2.shift_id = ss.shift_id
        LEFT JOIN
            accounts_staffshift stfshift ON stfshift.staffshift_id = as2.staffshift_id
        LEFT JOIN 
            accounts_shiftsettings as3 ON stfshift.shiftsettings_id = as3.shiftsettings_id 
        LEFT JOIN
            accounts_account aa ON aa.account_id = stfshift.staff_id
    """
    if start_date is not None or end_date is not None:
        if start_date is not None and end_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime >= '{converted_start_date}'
                    AND as2.start_shift_datetime <= '{converted_end_date}'
                    AND aa.lot_id = '{lot}'
            """
        elif start_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime >= '{converted_start_date}'
                    AND aa.lot_id = '{lot}'
            """
        elif end_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime <= '{converted_end_date}'
                    AND aa.lot_id = '{lot}'
            """
    else:
        query += f"""
                WHERE aa.lot_id = '{lot}'
            """
    query += """
        GROUP BY
            as2.shift_id,
            as3.shift_name,
            as2.start_shift_datetime,
            as2.end_shift_datetime,
            as3.end_hour,
            aa.account_id;
    """

    if start_date and end_date:
        query = query
    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    #               Overview Calculation                #
    total_active_shift = Shift.objects.filter(
        end_shift_datetime__isnull=True,
    )
    total_ended_shift = Shift.objects.filter(end_shift_datetime__isnull=False)
    total_bookings = Booking.objects.filter(
        lot=lot,
    ).exclude(booking_status__booking_status__in=["No Show", "Reservation"])

    total_transactions = Transaction.objects.filter(booking__lot=lot)
    total_check_in = BookingStatus.objects.filter(
        booking_status="Check In", booking__lot=lot
    )
    total_check_out = BookingStatus.objects.filter(
        booking_status="Check Out", booking__lot=lot
    )

    if start_date or end_date:
        total_active_shift = total_active_shift.filter(
            start_shift_datetime__range=(converted_start_date, converted_end_date)
        )
        total_ended_shift = total_ended_shift.filter(
            start_shift_datetime__range=(converted_start_date, converted_end_date)
        )
        total_bookings = total_bookings.filter(
            booking_made_datetime__range=(converted_start_date, converted_end_date)
        )
        total_transactions = total_transactions.filter(
            transaction_datetime__range=(converted_start_date, converted_end_date)
        )
        total_check_in = total_check_in.filter(
            status_datetime__range=(converted_start_date, converted_end_date)
        )
        total_check_out = total_check_out.filter(
            status_datetime__range=(converted_start_date, converted_end_date)
        )

    report_overview = [
        {
            "left": {"name": "Active Shift", "value": total_active_shift.count()},
            "right": {"name": "Ended Shift", "value": total_ended_shift.count()},
        },
        {
            "left": {
                "name": "Total Bookings",
                "value": total_bookings.count(),
            },
            "right": {
                "name": "Total Transactions",
                "value": total_transactions.count(),
            },
        },
        {
            "left": {
                "name": "Total Check In",
                "value": total_check_in.count(),
            },
            "right": {
                "name": "Total Check Out",
                "value": total_check_out.count(),
            },
        },
    ]

    # staffshifts = [item["staffshift_id"] for item in results]
    # details = query_transactions(staffshifts, None)

    # data = {"data": results, "overview": report_overview, "details": details}

    data = {"data": results, "overview": report_overview}

    return data


def shift_per_day_query(start_date=None, end_date=None, lot=None):
    converted_start_date = (
        datetime2.fromisoformat(start_date[:-1]).strftime("%Y-%m-%d %H:%M:%S")
        if start_date is not None
        else None
    )
    converted_end_date = (
        datetime2.fromisoformat(end_date[:-1]).strftime("%Y-%m-%d %H:%M:%S")
        if end_date is not None
        else None
    )
    query = f"""
        SELECT
			date(as2.start_shift_datetime) as date,
			COUNT(DISTINCT as2.shift_id) AS shift_count,
			SUM(ss.item_price) AS total_item_price,
			SUM(ss.service_charge) AS total_service_charge,
			SUM(ss.tax_value) AS total_tax_value
		FROM
			salesreport_salestransaction ss
		LEFT JOIN
			accounts_shift as2 ON as2.shift_id = ss.shift_id
		LEFT JOIN
			accounts_staffshift stfshift ON stfshift.staffshift_id = as2.staffshift_id
		left join 
			accounts_shiftsettings as3 on stfshift.shiftsettings_id = as3.shiftsettings_id 
		LEFT JOIN
			accounts_account aa ON aa.account_id = stfshift.staff_id
            
		"""
    if start_date is not None or end_date is not None:

        if start_date is not None and end_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime >= '{converted_start_date}'
                    AND as2.start_shift_datetime <= '{converted_end_date}'
            """
        elif start_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime >= '{converted_start_date}'
            """
        elif end_date is not None:
            query += f"""
                WHERE
                    as2.start_shift_datetime <= '{converted_end_date}'
            """
    query += """
		GROUP by
		--as2.start_shift_datetime 
			DATE(as2.start_shift_datetime);

        """

    if start_date and end_date:
        query = query

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    #               Overview Calculation                #
    total_active_shift = Shift.objects.filter(
        end_shift_datetime__isnull=True,
    )
    total_ended_shift = Shift.objects.filter(end_shift_datetime__isnull=False)
    total_bookings = Booking.objects.filter(lot=lot).exclude(
        booking_status__booking_status__in=["No Show", "Reservation"]
    )
    total_transactions = Transaction.objects.filter(booking__lot=lot)
    total_check_in = BookingStatus.objects.filter(
        booking_status="Check In", booking__lot=lot
    )
    total_check_out = BookingStatus.objects.filter(
        booking_status="Check Out", booking__lot=lot
    )

    if start_date or end_date:
        total_active_shift = total_active_shift.filter(
            start_shift_datetime__range=(converted_start_date, converted_end_date)
        )
        total_ended_shift = total_ended_shift.filter(
            start_shift_datetime__range=(converted_start_date, converted_end_date)
        )
        total_bookings = total_bookings.filter(
            booking_made_datetime__range=(converted_start_date, converted_end_date)
        )
        total_transactions = total_transactions.filter(
            transaction_datetime__range=(converted_start_date, converted_end_date)
        )
        total_check_in = total_check_in.filter(
            status_datetime__range=(converted_start_date, converted_end_date)
        )
        total_check_out = total_check_out.filter(
            status_datetime__range=(converted_start_date, converted_end_date)
        )

    report_overview = [
        {
            "left": {"name": "Active Shift", "value": total_active_shift.count()},
            "right": {"name": "Ended Shift", "value": total_ended_shift.count()},
        },
        {
            "left": {
                "name": "Total Bookings",
                "value": total_bookings.count(),
            },
            "right": {
                "name": "Total Transactions",
                "value": total_transactions.count(),
            },
        },
        {
            "left": {
                "name": "Total Check In",
                "value": total_check_in.count(),
            },
            "right": {
                "name": "Total Check Out",
                "value": total_check_out.count(),
            },
        },
    ]

    # dates = [item["date"] for item in results]
    # details = query_transactions(None, dates)

    # data = {"data": results, "overview": report_overview, "details": details}

    data = {"data": results, "overview": report_overview}

    return data


# ----------------------------------------Dashboard API SQL Query----------------------------------------#


def fetch_sales_room_sales_sumamry(
    start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    if lot_id == "":
        lot_id = None
    query = """
        SELECT 
            date,
            SUM(subtotal) AS total
        FROM (
            SELECT DISTINCT
                tt.transaction_id,
                CASE
                    WHEN tt.transaction_status = 'Refund' THEN (tt.transaction_datetime+ INTERVAL '8 hours')::DATE 
                    ELSE COALESCE(
                        (br.actual_checkin_date_time + INTERVAL '8 hours')::DATE,
                        (bb2.check_in_datetime + INTERVAL '8 hours')::DATE
                    )
                END AS date,
                CASE
                    WHEN bb2.booking_status = 'No Show' AND br.actual_checkin_date_time IS NOT NULL  AND br.actual_checkin_date_time - bb2.check_in_datetime  > INTERVAL '1 day' THEN
                        CASE
                            WHEN tt.transaction_status = 'Pending Payment' THEN tt.credit_amount * -1
                            ELSE tt.debit_amount * -1
                        END
                    ELSE
                        CASE
                            WHEN tt.transaction_status = 'Pending Payment' THEN tt.credit_amount
                            ELSE tt.debit_amount
                        END
                END AS subtotal
            FROM transaction_transaction tt
            INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
            LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id
            INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id     
            WHERE (
                (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
                OR
                (br.actual_checkin_date_time IS NULL AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN %s AND %s))
                OR
                (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
            )
            AND tt.is_room_booking = TRUE 
            AND tt.transaction_status != 'Void'
            AND bb2.is_latest = TRUE 
            AND (%s IS NULL OR bb.lot_id = %s)
        ) AS Subquery
        GROUP BY date
        ORDER BY date ASC;
    """

    params = [
        start_date,
        end_date,
        start_date,
        end_date,
        start_date,
        end_date,
        lot_id,
        lot_id,
    ]

    # Debugging: Print the query and parameters
    print("Executing query:", query)
    print("With parameters:", params)

    cursor = connection.cursor()
    cursor.execute(query, params)
    results = dictfetchall(cursor)
    return results


def is_date_less_recent_than_when(date_str, when):
    # Parse the input date string
    input_date = datetime.datetime.strptime(str(date_str), "%Y-%m-%d").date()

    # Get today's date
    today = datetime.datetime.now().astimezone(local_timezone).date()

    date_to_compare = None

    if when == "yesterday":
        date_to_compare = today - datetime.timedelta(days=1)
    elif when == "tomorrow":
        date_to_compare = today + datetime.timedelta(days=1)
    else:
        date_to_compare = today

    # Compare the input date with yesterday
    return input_date <= date_to_compare


@cache_sales_results
def fetch_sales_room_avg_sales_sumamry(
    start_date: datetime.datetime, end_date: datetime.datetime, lot_id: int = None
):
    # TODO: Line 452, 462-464 Might want to remove in the future
    if lot_id == "":
        lot_id = None

    # Optimized query with better structure and reduced complexity

    query = f"""
    WITH RankedTransactions AS (
        SELECT 
            tt.transaction_id,
            bb.booking_no,
            bb.booking_id,
            tt.is_room_booking,
            tt.transaction_datetime,
            CASE 
            WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
            ELSE COALESCE(
                br.actual_checkin_date_time,
                bb2.check_in_datetime
            )
            END AS date_time,
            case
            	when tt.transaction_status = 'Pending Payment' then tt.credit_amount else tt.debit_amount 
            end as subtotal,
            ROW_NUMBER() OVER (
                PARTITION BY tt.transaction_id
                ORDER BY COALESCE(
                    br.actual_checkin_date_time, 
                    bb2.check_in_datetime
                ) DESC
            ) AS rn
        FROM transaction_transaction tt
        INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
        LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id       
        INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id
        INNER JOIN bookings_platform bp ON bp.platform_id  = bb.platform_id    
        WHERE (
            (br.actual_checkin_date_time IS NOT NULL AND tt.transaction_status != 'Refund' AND br.actual_checkin_date_time BETWEEN '{start_date}' AND '{end_date}')
            OR
            (br.actual_checkin_date_time IS NULL AND tt.transaction_status != 'Refund' AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}'))
            or
            (tt.transaction_status = 'Refund' and tt.transaction_datetime between '{start_date}' AND '{end_date}')    
        )
        {f'AND bb.lot_id = {lot_id}' if lot_id else ''}
        AND (bb2.is_latest = true and bb2.booking_status != 'Booked' and bb2.booking_status NOT LIKE '%Transfer From%')
        AND NOT (
        bp.platform = 'Hotel Website' AND tt.transaction_status = 'Void'
        )
    )
    SELECT 
        transaction_id,
        booking_id,
        booking_no,
        date_time,
        transaction_datetime,
        is_room_booking,
        subtotal
    FROM RankedTransactions
    WHERE rn = 1;
    """

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    results = group_by_date_optimized(results)

    yesterday_month_to_date_sales = 0.00
    today_month_to_date_sales = 0.00
    tomorrow_month_to_date_sales = 0.00

    yesterday_average_sales = 0.00
    today_average_sales = 0.00
    tomorrow_average_sales = 0.00

    yesterday_total_sales = 0.00
    today_total_sales = 0.00
    tomorrow_total_sales = 0.00

    current_date = datetime.datetime.now().astimezone(local_timezone).date()

    last_day = calendar.monthrange(current_date.year, current_date.month)[1]
    last_date = current_date.replace(day=last_day)

    first_day_of_last_month, last_day_of_next_month = get_special_dates(current_date)

    value_for_yesterday = current_date - datetime.timedelta(days=1)
    value_for_tomorrow = current_date + datetime.timedelta(days=1)

    if current_date.day == 1:
        value_for_yesterday = first_day_of_last_month
    elif current_date.day == last_date.day:
        value_for_tomorrow = last_day_of_next_month

    print("results.items", results.items)
    for date, total_subtotal in results.items():

        if is_date_less_recent_than_when(date, "yesterday"):
            if (
                date.year == value_for_yesterday.year
                and date.month == value_for_yesterday.month
            ):
                yesterday_month_to_date_sales += float(total_subtotal)
                yesterday_average_sales = round(
                    yesterday_month_to_date_sales / (value_for_yesterday.day), 2
                )
            yesterday_total_sales = (
                total_subtotal
                if date == current_date - datetime.timedelta(days=1)
                else yesterday_total_sales
            )
        if is_date_less_recent_than_when(date, "today"):
            if date.year == current_date.year and date.month == current_date.month:
                today_month_to_date_sales += float(total_subtotal)
                today_average_sales = round(
                    today_month_to_date_sales / (current_date.day), 2
                )
            today_total_sales = (
                total_subtotal if date == current_date else today_total_sales
            )
        if is_date_less_recent_than_when(date, "tomorrow"):

            if (
                date.year == value_for_tomorrow.year
                and date.month == value_for_tomorrow.month
            ):
                tomorrow_month_to_date_sales += float(total_subtotal)
                tomorrow_average_sales = round(
                    tomorrow_month_to_date_sales / (value_for_tomorrow.day), 2
                )
            tomorrow_total_sales = (
                total_subtotal
                if date == current_date + datetime.timedelta(days=1)
                else tomorrow_total_sales
            )

    data = {
        "yesterday": {
            "month_to_date_sales": round(yesterday_month_to_date_sales, 2),
            "average_sales": yesterday_average_sales,
            "total_sales": yesterday_total_sales,
        },
        "today": {
            "month_to_date_sales": round(today_month_to_date_sales, 2),
            "average_sales": today_average_sales,
            "total_sales": today_total_sales,
        },
        "tomorrow": {
            "month_to_date_sales": round(tomorrow_month_to_date_sales, 2),
            "average_sales": tomorrow_average_sales,
            "total_sales": tomorrow_total_sales,
        },
    }

    return data


def query_total_revenue(
    start_date: Union[str, datetime.datetime],
    end_date: Union[str, datetime.datetime],
    lot_id: int = None,
):
    start_date = (
        datetime2.fromisoformat(start_date[:-1]) if start_date is not None else None
    )
    end_date = (
        datetime2.fromisoformat(end_date[:-1]) - timedelta(milliseconds=1)
        if start_date is not None
        else None
    )

    if lot_id == "":
        lot_id = None

    query = f"""
    WITH RankedTransactions AS (
        SELECT 
            tt.transaction_id,
            bb.booking_no,
            bb.booking_id,
            tt.transaction_datetime,
            CASE 
            WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
            ELSE COALESCE(
                br.actual_checkin_date_time,
                bb2.check_in_datetime
            )
            END AS date_time,
            case
                when tt.transaction_status = 'Pending Payment' then tt.credit_amount else tt.debit_amount 
            end as subtotal,
            tt.items,
            tt.is_room_booking,
            ROW_NUMBER() OVER (
                PARTITION BY tt.transaction_id
                ORDER BY COALESCE(
                    br.actual_checkin_date_time, 
                    bb2.check_in_datetime
                ) DESC
            ) AS rn
        FROM transaction_transaction tt
        INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
        LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id       
        INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id   
        WHERE (
            (br.actual_checkin_date_time IS NOT NULL AND tt.transaction_status != 'Refund' AND br.actual_checkin_date_time BETWEEN '{start_date}' AND '{end_date}')
            OR
            (br.actual_checkin_date_time IS NULL AND tt.transaction_status != 'Refund' AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}'))
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN '{start_date}' AND '{end_date}')
            OR
            (bb2.is_latest = false AND bb2.booking_status = 'No Show' AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}')
        )
        AND (
            (bb2.is_latest = true AND bb2.booking_status != 'Booked' AND bb2.booking_status NOT LIKE '%Transfer From%') 
            OR 
            (bb2.is_latest = false AND bb2.booking_status = 'No Show' AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}')
        )
        {f'AND bb.lot_id = {lot_id}' if lot_id else ''}
    )
    SELECT 
        transaction_id,
        booking_id,
        booking_no,
        date_time,
        transaction_datetime,
        subtotal,
        is_room_booking,
        items
    FROM RankedTransactions
    WHERE rn = 1;
    """
    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    locker_sum = 0

    shower_sum = 0

    pos_sum = 0

    room_sum = 0

    lounge_sum = 0

    misc_sum = 0

    start_date_date = (start_date + timedelta(hours=8)).day

    range_above_1_day = abs(end_date - start_date) > timedelta(days=1)

    for result in results:
        items = json.loads(result["items"])

        need_reverse = False

        booking_id = result["booking_id"]

        latest_booking_status = BookingStatus.objects.filter(
            booking=booking_id, is_latest=True
        )[0]

        room_bookings = RoomBooking.objects.filter(booking_id=booking_id)

        need_reverse = check_need_reverse(
            booking_id=booking_id,
            transaction_datetime=result["transaction_datetime"],
            is_room_booking=result["is_room_booking"],
            latest_booking_status=latest_booking_status,
            room_booking=room_bookings,
            # Code below is to check if the range is more than a day or not
            range_above_1_day=range_above_1_day,
        )

        amount_to_add = result["subtotal"] * -1 if need_reverse else result["subtotal"]

        if (
            need_reverse
            and latest_booking_status.has_no_show
            and latest_booking_status.booking_status
            in ["Confirm Booking", "Check In", "Check Out", "Overstay"]
        ):
            amount_to_add = 0

        if latest_booking_status.has_no_show:
            no_show_date = BookingStatus.objects.filter(
                booking=booking_id, booking_status="No Show"
            ).first()
            no_show_date = (no_show_date.check_in_datetime + timedelta(hours=8)).day
            if no_show_date == start_date_date:
                amount_to_add = result["subtotal"]

        if result["is_room_booking"]:
            if items[0]["item_type"] == "Lounge":
                lounge_sum += amount_to_add
            else:
                room_sum += amount_to_add
        else:
            if items[0]["category"] == TransactionItemCategory.LOCKER_SALES:
                locker_sum += amount_to_add
            elif items[0]["category"] == TransactionItemCategory.SHOWER_SALES:
                shower_sum += amount_to_add
            elif items[0]["category"] == TransactionItemCategory.MERCH_SALES:
                pos_sum += amount_to_add
            # elif any(
            #     "hrs extend" in item["item_type"].lower()
            #     or "add 1 hour" in item["item_type"].lower()
            #     or item["item_name"].lower() == "updated price"
            #     for item in items
            # ):
            #     room_sum += amount_to_add
            else:
                # misc_sum += amount_to_add
                room_sum += amount_to_add

    return {
        "locker_revenue": locker_sum,
        "shower_revenue": shower_sum,
        "pos_revenue": pos_sum,
        "room_revenue": room_sum,
        "lounge_revenue": lounge_sum,
        "misc_revenue": misc_sum,
    }


def query_room_revenue_by_platform(
    start_date: Union[str, datetime.datetime],
    end_date: Union[str, datetime.datetime],
    lot_id: int = None,
):
    start_date = (
        datetime2.fromisoformat(start_date[:-1]) if start_date is not None else None
    )
    end_date = (
        datetime2.fromisoformat(end_date[:-1]) - timedelta(milliseconds=1)
        if start_date is not None
        else None
    )

    if lot_id == "":
        lot_id = None

    query = f"""
    WITH RankedTransactions AS (
        SELECT 
            tt.transaction_id,
            bb.booking_no,
            bb.booking_id,
            bb.platform_id,
            tt.transaction_datetime,
            CASE 
            WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
            ELSE COALESCE(
                br.actual_checkin_date_time,
                bb2.check_in_datetime
            )
            END AS date_time,
            case
                when tt.transaction_status = 'Pending Payment' then tt.credit_amount else tt.debit_amount 
            end as subtotal,
            tt.items,
            tt.is_room_booking,
            ROW_NUMBER() OVER (
                PARTITION BY tt.transaction_id
                ORDER BY COALESCE(
                    br.actual_checkin_date_time, 
                    bb2.check_in_datetime
                ) DESC
            ) AS rn
        FROM transaction_transaction tt
        INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
        LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id       
        INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id
        INNER JOIN rooms_room rr ON rr.room_id = br.room_id
        INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id   
        WHERE (
            (br.actual_checkin_date_time IS NOT NULL AND tt.transaction_status != 'Refund' AND br.actual_checkin_date_time BETWEEN '{start_date}' AND '{end_date}')
            OR
            (br.actual_checkin_date_time IS NULL AND tt.transaction_status != 'Refund' AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}'))
            OR
            (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN '{start_date}' AND '{end_date}')
            OR
            (bb2.is_latest = false AND bb2.booking_status = 'No Show' AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}')
        )
        AND (
            (bb2.is_latest = true AND bb2.booking_status != 'Booked' AND bb2.booking_status NOT LIKE '%Transfer From%') 
            OR 
            (bb2.is_latest = false AND bb2.booking_status = 'No Show' AND bb2.check_in_datetime BETWEEN '{start_date}' AND '{end_date}')
        )
        AND tt.is_room_booking = true
        AND rr2.type_name != 'Lounge'
        {f'AND bb.lot_id = {lot_id}' if lot_id else ''}
    )
    SELECT 
        platform_id,
        SUM(subtotal) as total_revenue
    FROM RankedTransactions
    WHERE rn = 1
    GROUP BY platform_id;
    """
    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    # Process results to handle special cases like no-shows and refunds
    processed_results = []
    for result in results:
        platform_id = result["platform_id"]
        total_revenue = 0

        # Get all transactions for this platform
        platform_transactions = Transaction.objects.filter(
            booking__platform_id=platform_id,
            is_room_booking=True,
            transaction_datetime__range=(start_date, end_date),
        )

        for transaction in platform_transactions:
            booking_id = transaction.booking_id
            latest_booking_status = BookingStatus.objects.filter(
                booking=booking_id, is_latest=True
            ).first()

            if not latest_booking_status:
                continue

            room_bookings = RoomBooking.objects.filter(booking_id=booking_id)
            need_reverse = check_need_reverse(
                booking_id=booking_id,
                transaction_datetime=transaction.transaction_datetime,
                is_room_booking=True,
                latest_booking_status=latest_booking_status,
                room_booking=room_bookings,
                range_above_1_day=abs(end_date - start_date) > timedelta(days=1),
            )

            amount_to_add = (
                transaction.debit_amount * -1
                if need_reverse
                else transaction.debit_amount
            )

            if (
                need_reverse
                and latest_booking_status.has_no_show
                and latest_booking_status.booking_status
                in ["Confirm Booking", "Check In", "Check Out", "Overstay"]
            ):
                amount_to_add = 0

            if latest_booking_status.has_no_show:
                no_show_date = BookingStatus.objects.filter(
                    booking=booking_id, booking_status="No Show"
                ).first()
                if no_show_date:
                    no_show_date = (
                        no_show_date.check_in_datetime + timedelta(hours=8)
                    ).day
                    if no_show_date == (start_date + timedelta(hours=8)).day:
                        amount_to_add = transaction.debit_amount

            total_revenue += amount_to_add

        processed_results.append(
            {"platform_id": platform_id, "total_revenue": total_revenue}
        )

    return processed_results


# ----------------------------------------Dashboard API SQL Query----------------------------------------#

# ----------------------------------------END SHIFT PAGE DETAIL / SUMMARY SQL QUERY----------------------------------------#


def shift_sales_room_sales_summary_by_shift(shift_id: str):
    query = """
			select  ss.shift_id , sum(ss.item_price) as total_item_price, 
				SUM(ss.service_charge) as total_service_charge,
				SUM(ss.tax_value) as total_tax_value, 
				ss.item_type, 
				ss.duration 
				from salesreport_salestransaction ss 
				where ss.item_category='ROOM_SALES'
			group by ss.shift_id ,  ss.item_type, ss.duration
		"""
    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)
    return results


def get_summary_data_per_shift(shift_settings_id, sales):
    if len(shift_settings_id) > 1:
        shift_settings_tuple = tuple(shift_settings_id)
    else:
        shift_settings_tuple = f"('{shift_settings_id[0]}')"

    query = f"""
        SELECT 
            as2.shift_id, 
            ss.item_type, 
            SUM(ss.item_price) AS total_item_price, 
            SUM(ss.service_charge) AS total_service_charge, 
            SUM(ss.tax_value) AS total_tax_value,
            COUNT(ss.item_type) AS count,
    """

    # Handle different cases based on the 'sales' variable
    if sales in [
        TransactionItemCategory.LOCKER_SALES,
        TransactionItemCategory.SHOWER_SALES,
    ]:
        query += "ss.duration,"
    elif sales == TransactionItemCategory.MERCH_SALES:
        query += "ss.item_name,"

    query += f"""
            bb.customer_booked
        FROM
            salesreport_salestransaction ss 
        INNER JOIN
            transaction_transaction tt ON ss.transaction_head_id = tt.transaction_id
        INNER JOIN
            bookings_booking bb ON tt.booking_id = bb.booking_id 
        INNER JOIN
            accounts_shift as2 ON as2.shift_id = ss.shift_id
        INNER JOIN
            accounts_staffshift stfshift ON stfshift.staffshift_id = as2.staffshift_id 
        INNER JOIN
            accounts_shiftsettings ass ON ass.shiftsettings_id = stfshift.shiftsettings_id
        WHERE
            ss.item_category='{sales}'
        AND
            as2.shift_id IN {shift_settings_tuple}
        GROUP BY
            as2.shift_id,
            ss.item_type,
    """

    # Handle different cases based on the 'sales' variable for GROUP BY clause
    if sales in [
        TransactionItemCategory.LOCKER_SALES,
        TransactionItemCategory.SHOWER_SALES,
    ]:
        query += "ss.duration,"
    elif sales == TransactionItemCategory.MERCH_SALES:
        query += "ss.item_name,"

    query += """
            bb.customer_booked;
    """
    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    return results


def get_summary_data_per_day(dates, sales):
    date_value = [str(datetime2.strptime(dt, "%Y-%m-%d").date()) for dt in dates]

    query = f"""
        SELECT 
            DATE(as2.start_shift_datetime) AS date, 
            SUM(ss.item_price) AS total_item_price, 
            SUM(ss.service_charge) AS total_service_charge, 
            SUM(ss.tax_value) AS total_tax_value, 
            COUNT(ss.item_type) AS count,
    """

    # Handle different cases based on the 'sales' variable
    if sales in [
        TransactionItemCategory.LOCKER_SALES,
        TransactionItemCategory.SHOWER_SALES,
    ]:
        query += "ss.duration,"
    elif sales == TransactionItemCategory.MERCH_SALES:
        query += "ss.item_name,"

    query += f"""
            bb.customer_booked
        FROM 
            salesreport_salestransaction ss 
        INNER JOIN 
            transaction_transaction tt ON ss.transaction_head_id = tt.transaction_id
        INNER JOIN 
            bookings_booking bb ON tt.booking_id = bb.booking_id 
        INNER JOIN 
            accounts_shift as2 ON as2.shift_id = ss.shift_id
        INNER JOIN 
            accounts_staffshift stfshift ON stfshift.staffshift_id = as2.staffshift_id 
        INNER JOIN 
            accounts_shiftsettings ass ON ass.shiftsettings_id = stfshift.shiftsettings_id
        WHERE 
            ss.item_category = '{sales}' 
        GROUP BY 
            DATE(as2.start_shift_datetime),
            ss.item_type, 
            bb.customer_booked,
    """

    # Handle different cases based on the 'sales' variable for GROUP BY clause
    if sales in [
        TransactionItemCategory.LOCKER_SALES,
        TransactionItemCategory.SHOWER_SALES,
    ]:
        query += "ss.duration;"
    elif sales == TransactionItemCategory.MERCH_SALES:
        query += "ss.item_name;"

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    filtered_data = []

    if isinstance(results, list):
        for data in results:
            if str(data["date"]) in date_value:
                filtered_data.append(data)
    return filtered_data


# ----------------------------------------END SHIFT PAGE DETAIL / SUMMARY SQL QUERY----------------------------------------#


def staffshift_room_sales_summary_query(
    shift_id, date, type, lot_id
) -> Tuple[dict, list]:
    query = f"""
        SELECT 
        """
    if (
        shift_id is not None and shift_id != "" and len(shift_id) > 0
    ) or type == "shift":
        query += f"""
            as2.shift_id,
            """
    elif (date is not None and date != "" and len(date) > 0) or type == "date":
        query += f"""
            DATE(as2.start_shift_datetime) AS date,
            """

    query += f"""
        ss.item_type, 
	    SUM(ss.item_price) AS total_item_price, 
	    SUM(ss.service_charge) AS total_service_charge, 
	    SUM(ss.tax_value) AS total_tax_value,
        count(ss.item_type) as count,
	    ss.duration
        FROM 
            salesreport_salestransaction ss 
        inner JOIN 
            accounts_shift as2 ON as2.shift_id = ss.shift_id 
    """
    where_query = ""
    if (
        shift_id is not None and shift_id != "" and len(shift_id) > 0
    ) or type == "shift":
        if len(shift_id) > 1:
            where_query = tuple(shift_id)
        else:
            where_query = f"('{shift_id[0]}')"
        query += f"""
        
            inner JOIN 
                accounts_staffshift stfshift ON stfshift.staffshift_id = as2.staffshift_id 
            WHERE 
	            ss.item_category = 'ROOM_SALES' and as2.shift_id  IN  {where_query}
            GROUP BY  
                as2.shift_id, ss.item_type, ss.duration 
            order by ss.duration 
            """
    elif (date is not None and date != "" and len(date) > 0) or type == "date":
        date_value = tuple([datetime2.strptime(dt, "%Y-%m-%d").date() for dt in date])

        query += f"""
            WHERE
                ss.item_category = 'ROOM_SALES'
            GROUP BY
                ss.item_type, ss.duration , DATE(as2.start_shift_datetime)
            order by ss.duration
            """

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)

    if (date is not None and date != "" and len(date) > 0) or type == "date":

        filtered_data = []
        date_value = [str(datetime2.strptime(dt, "%Y-%m-%d").date()) for dt in date]

        if isinstance(results, list):
            for data in results:
                if str(data["date"]) in date_value:
                    filtered_data.append(data)

        results = filtered_data

    query = f"""
       select DISTINCT rr.type_name from rooms_roomtype rr  
	    inner join rooms_roomzone rr2 on rr.roomzone_id  = rr2.zone_id 
	
	    where rr2.lot_id_id = {lot_id} and rr.archived = false

    """
    cursor.execute(query)
    room_types = dictfetchall(cursor)
    room_type_names = [room_type["type_name"] for room_type in room_types]

    # Build THe serializers
    # Dictionary to hold Room Type sales grouped by duration
    durations_grp = defaultdict(list)
    for result in results:
        sales = (
            result["total_item_price"]
            + result["total_tax_value"]
            + result["total_service_charge"]
        )
        item_name = result["item_type"]
        duration = result["duration"]
        count = result["count"]
        # Check if item_name already exists in durations_grp[duration]
        existing_item = next(
            (
                item
                for item in durations_grp[duration]
                if item["item_name"] == item_name
            ),
            None,
        )

        if existing_item:
            # Item already exists, update sales and count
            existing_item["sales"] += sales
            existing_item["count"] += count
        else:
            # Item doesn't exist, append new item
            durations_grp[duration].append(
                {"item_name": item_name, "sales": sales, "count": count}
            )

    room_type_names.sort(reverse=False)  # A - Z
    # M X N where M is duration N is room_typ count
    for duration in durations_grp:
        items_list = durations_grp[duration]
        item_names = [item["item_name"] for item in items_list]
        for room_type_name in room_type_names:
            if room_type_name in item_names:
                continue

            durations_grp[duration].append(
                {"item_name": room_type_name, "sales": 0, "count": 0}
            )
        durations_grp[duration].sort(key=lambda x: x["item_name"], reverse=False)
        # Do sorting

    # Do Sorting

    cursor.close()
    return (durations_grp, room_type_names)


# from queryservice.sales import *
def query_total_payment_collected(shift_id=None, date=None):
    where_query = ""
    query = """
    select 
        DATE(as2.start_shift_datetime) as date, pp.payment_type_id, pp.payment_type ,SUM(ss.subtotal) as total
    from salesreport_salestransaction ss 
    inner join payment_paymenttype pp on ss.payment_type_id = pp.payment_type_id 
    inner join accounts_shift as2 on ss.shift_id = as2.shift_id  
    """

    if shift_id is not None and shift_id != "" and len(shift_id) > 0:
        if len(shift_id) > 1:
            where_query = tuple(shift_id)
        else:
            where_query = f"('{shift_id[0]}')"
        query += f"""   
            inner join accounts_staffshift as3 on as3.staffshift_id = as2.staffshift_id 
            where as2.shift_id IN {where_query} AND ss.item_category != 'Adjustment' AND ss.item_category != 'Promotion'
        """
    query += """
    group by pp.payment_type_id, DATE(as2.start_shift_datetime)
    order by pp.payment_type_id asc
    """

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)
    cursor.close()

    if date is not None and date != "" and len(date) > 0:
        filtered_data = []
        date_value = [str(datetime2.strptime(dt, "%Y-%m-%d").date()) for dt in date]

        if isinstance(results, list):
            for data in results:
                if str(data["date"]) in date_value:
                    filtered_data.append(data)

        results = filtered_data

    payment_totals = {}

    # Calculate total amount for each payment type
    for entry in results:
        payment_type_id = entry["payment_type_id"]
        total = float(
            entry["total"]
        )  # Convert total to float for arithmetic operations
        if payment_type_id not in payment_totals:
            payment_totals[payment_type_id] = {
                "payment_type": entry["payment_type"],
                "total": float(format(total, ".2f")),
            }
        else:
            payment_totals[payment_type_id]["total"] += float(format(total, ".2f"))
            payment_totals[payment_type_id]["total"] = float(
                format(payment_totals[payment_type_id]["total"], ".2f")
            )

    if shift_id is not None and shift_id != "" and len(shift_id) > 0:
        selected_shifts = Shift.objects.filter(shift_id__in=shift_id).aggregate(
            total_overcharge_shortage=Sum("overflow_shortage")
        )
    elif date is not None and date != "" and len(date) > 0:
        selected_shifts = Shift.objects.filter(
            start_shift_datetime__date__in=[
                datetime2.strptime(dt, "%Y-%m-%d").date() for dt in date
            ]
        ).aggregate(total_overcharge_shortage=Sum("overflow_shortage"))

    if (shift_id is not None and shift_id != "" and len(shift_id) > 0) or (
        date is not None and date != "" and len(date) > 0
    ):
        if selected_shifts["total_overcharge_shortage"] is not None:
            payment_totals[0] = {
                "payment_type": "Overflow/Shortage",
                "total": (selected_shifts["total_overcharge_shortage"]),
            }

    # Convert the payment_totals dictionary to a list of dictionaries
    results = [{"payment_type_id": k, **v} for k, v in payment_totals.items()]
    return results


# from queryservice.sales import *
def query_transactions(shift_id=None, date=None):
    qs = Transaction.objects.all()

    if shift_id is not None and len(shift_id) > 0 and shift_id[0] != "":
        qs = qs.filter(shift__in=shift_id)

    if date is not None and len(date) > 0 and date[0] != "":
        combined_query = Q()

        for date_obj in date:
            combined_query |= Q(shift__start_shift_datetime__date=date_obj)
        qs = qs.filter(combined_query)
        # qs = qs.filter(transaction_datetime__date=date)

    qs = qs.order_by("transaction_datetime")

    latest_booking_status = Prefetch(
        "booking__booking_status",
        queryset=BookingStatus.objects.filter(is_latest=True),
        to_attr="latest_booking_status",
    )
    qs = qs.prefetch_related(latest_booking_status)

    return convert_to_report_format(qs)


def get_sales_report_grouped(group_by):

    query = """"
        SELECT
            SUM(ss.item_price) as total,
            ss.item_category,
            tt.is_room_booking,
            SUM(ss.tax_value) as tax,
            SUM(ss.service_charge) as service_charge,
            ss.transaction_head_id,
            bb2.booking_status
    """

    add_on_query = ""

    if group_by == "booking":
        add_on_query = """
            bb.booking_id,
	        bb.booking_no,
        """

    if group_by == "payment-type":
        add_on_query = """
            ss.payment_type_id,
	        pp.payment_type,
        """

    if group_by == "booking-platform":
        add_on_query = """
            bp.platform_id,
	        bp.platform,
        """

    if group_by == "booking-duration":
        add_on_query = """
            ss.duration,
        """

    if group_by == "room-type":
        add_on_query = """
            ss.item_type,
        """

    query = f"""
        SELECT
            {add_on_query}
            SUM(ss.item_price) as total,
            ss.item_category,
            tt.is_room_booking,
            SUM(ss.tax_value) as tax,
            SUM(ss.service_charge) as service_charge,
            ss.transaction_head_id,
            bb2.booking_status
        from
            salesreport_salestransaction ss 
        inner join
            transaction_transaction tt on
            ss.transaction_head_id = tt.transaction_id
        inner join
            bookings_booking bb on
            tt.booking_id = bb.booking_id 
        inner join
            bookings_platform bp on
            bb.platform_id = bp.platform_id 
        inner join
            bookings_bookingstatus bb2 on
	        bb2.booking_id = bb.booking_id
        inner join
            payment_paymenttype pp on
            ss.payment_type_id = pp.payment_type_id 
        group by
            {add_on_query}
            ss.item_category,
            tt.is_room_booking,
            ss.transaction_head_id,
            bb2.booking_status
    """

    cursor = connection.cursor()
    cursor.execute(query)
    results = dictfetchall(cursor)
    cursor.close()
    return results


def get_checked_in_booking_subtotal(
    cursor,
    start_date: datetime.datetime,
    end_date: datetime.datetime,
    lot_id: int = None,
):
    if lot_id == "":
        lot_id = None
    query = """
        SELECT SUM(subtotal)
        FROM (
            SELECT DISTINCT ON (tt.transaction_id)
                tt.transaction_id,
                CASE
                    WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
                    ELSE COALESCE(
                        br.actual_checkin_date_time,
                        bb2.check_in_datetime
                    )
                END AS date_time,
                CASE
                    WHEN tt.transaction_status = 'Pending Payment' THEN tt.credit_amount
                    ELSE tt.debit_amount
                END AS subtotal
            FROM transaction_transaction tt
            INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
            LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id
            INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id
            INNER JOIN rooms_room rr ON rr.room_id = br.room_id
            INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
            WHERE (
                (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
                OR
                (br.actual_checkin_date_time IS NULL AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN %s AND %s))
                OR
                (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
            )
            AND bb2.is_latest = TRUE
            AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
            AND rr2.type_name != 'Lounge'
            AND (%s IS NULL OR bb.lot_id = %s)
        ) AS Subquery;
    """

    params = [
        start_date,
        end_date,
        start_date,
        end_date,
        start_date,
        end_date,
        lot_id,
        lot_id,
    ]

    cursor.execute(query, params)
    results = dictfetchall(cursor)
    return results


def get_no_show_booking_subtotal(
    cursor,
    start_date: datetime.datetime,
    end_date: datetime.datetime,
    lot_id: int = None,
):
    if lot_id == "":
        lot_id = None
    query = """
        SELECT SUM(subtotal)
        FROM (
            SELECT DISTINCT ON (tt.transaction_id)
                tt.transaction_id,
                CASE
                    WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
                    ELSE COALESCE(
                        br.actual_checkin_date_time,
                        bb2.check_in_datetime
                    )
                END AS date_time,
                CASE
                    WHEN tt.transaction_status = 'Pending Payment' THEN tt.credit_amount
                    ELSE tt.debit_amount
                END AS subtotal
            FROM transaction_transaction tt
            INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
            LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id
            INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id
            INNER JOIN rooms_room rr ON rr.room_id = br.room_id
            INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
            WHERE (
                (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
                OR
                (br.actual_checkin_date_time IS NULL AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN %s AND %s))
                OR
                (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
            )
            AND bb2.is_latest = TRUE
            AND bb2.booking_status = 'No Show'
            AND (%s IS NULL OR bb.lot_id = %s)
        ) AS Subquery;
     """

    cursor.execute(
        query,
        [
            start_date,
            end_date,
            start_date,
            end_date,
            start_date,
            end_date,
            lot_id,
            lot_id,
        ],
    )
    results = dictfetchall(cursor)
    return results


def get_lounge_booking_subtotal(
    cursor,
    start_date: datetime.datetime,
    end_date: datetime.datetime,
    lot_id: int = None,
):
    if lot_id == "":
        lot_id = None
    query = """
        SELECT SUM(subtotal)
        FROM (
            SELECT DISTINCT ON (tt.transaction_id)
                tt.transaction_id,
                CASE
                    WHEN tt.transaction_status = 'Refund' THEN tt.transaction_datetime
                    ELSE COALESCE(
                        br.actual_checkin_date_time,
                        bb2.check_in_datetime
                    )
                END AS date_time,
                CASE
                    WHEN tt.transaction_status = 'Pending Payment' THEN tt.credit_amount
                    ELSE tt.debit_amount
                END AS subtotal
            FROM transaction_transaction tt
            INNER JOIN bookings_booking bb ON tt.booking_id = bb.booking_id
            LEFT JOIN bookings_roombooking br ON br.booking_id = bb.booking_id
            INNER JOIN bookings_bookingstatus bb2 ON bb2.booking_id = bb.booking_id
            INNER JOIN rooms_room rr ON rr.room_id = br.room_id
            INNER JOIN rooms_roomtype rr2 ON rr2.type_id = rr.room_type_id
            WHERE (
                (br.actual_checkin_date_time IS NOT NULL AND br.actual_checkin_date_time BETWEEN %s AND %s)
                OR
                (br.actual_checkin_date_time IS NULL AND (bb2.is_latest = true AND bb2.check_in_datetime BETWEEN %s AND %s))
                OR
                (tt.transaction_status = 'Refund' AND tt.transaction_datetime BETWEEN %s AND %s)
            )
            AND bb2.is_latest = TRUE
            AND bb2.booking_status IN ('Check In', 'Check Out', 'Overstayed')
            AND rr2.type_name = 'Lounge'
            AND (%s IS NULL OR bb.lot_id = %s)
        ) AS Subquery;
     """

    cursor.execute(
        query,
        [
            start_date,
            end_date,
            start_date,
            end_date,
            start_date,
            end_date,
            lot_id,
            lot_id,
        ],
    )
    results = dictfetchall(cursor)
    return results


def get_special_dates(current_date):
    # First day of the current month
    first_day_of_current_month = current_date.replace(day=1)

    # First day of last month
    if first_day_of_current_month.month == 1:
        # If current month is January, go to December of the previous year
        first_day_of_last_month = first_day_of_current_month.replace(
            year=current_date.year - 1, month=12
        )
    else:
        # Otherwise, just go back one month
        first_day_of_last_month = first_day_of_current_month.replace(
            month=current_date.month - 1
        )

    # Last day of the next month
    if current_date.month == 12:
        # If current month is December, add two months and set year accordingly
        first_day_of_month_after_next = current_date.replace(
            year=current_date.year + 1, month=2, day=1
        )
    else:
        # Otherwise, add two months and handle year change if necessary
        next_month = current_date.month + 2
        if next_month > 12:
            next_month -= 12
            first_day_of_month_after_next = current_date.replace(
                year=current_date.year + 1, month=next_month, day=1
            )
        else:
            first_day_of_month_after_next = current_date.replace(
                month=next_month, day=1
            )

    # Last day of next month is one day before the first day of the month after next
    last_day_of_next_month = first_day_of_month_after_next - timedelta(days=1)

    return first_day_of_last_month, last_day_of_next_month


# target_timezone = pytz.timezone("local")
local_timezone = timezone(timedelta(hours=8))


def group_by_date(results):
    grouped_data = defaultdict(decimal.Decimal)
    for result in results:
        # Parse the date_time string and make it timezone-aware (assuming the original timezone is UTC)
        date_time_str = str(result["date_time"]).replace("Z", "+00:00")
        date_time = datetime.datetime.fromisoformat(date_time_str)
        # Convert to the Asia/Kuala_Lumpur timezone
        date_time_aware = date_time.astimezone(local_timezone)
        # Extract the date part
        date = date_time_aware.date()

        need_reverse = False

        booking_id = result["booking_id"]

        latest_booking_status = BookingStatus.objects.filter(
            booking=booking_id, is_latest=True
        )[0]

        room_bookings = RoomBooking.objects.filter(booking_id=booking_id)

        need_reverse = check_need_reverse(
            booking_id=booking_id,
            transaction_datetime=result["transaction_datetime"],
            is_room_booking=result["is_room_booking"],
            latest_booking_status=latest_booking_status,
            room_booking=room_bookings,
            range_above_1_day=False,
        )

        amount_to_add = result["subtotal"] * -1 if need_reverse else result["subtotal"]

        if (
            latest_booking_status.has_no_show
            and latest_booking_status.booking_status
            in ["Confirm Booking", "Check In", "Check Out", "Overstay"]
            and need_reverse is True
        ):
            amount_to_add = 0

        if latest_booking_status.has_no_show:
            no_show_date = BookingStatus.objects.filter(
                booking=booking_id, booking_status="No Show"
            ).first()
            no_show_date = (no_show_date.check_in_datetime + timedelta(hours=8)).date()
            if no_show_date != date:
                grouped_data[no_show_date] += result["subtotal"]

        # Sum the subtotal for each date
        grouped_data[date] += amount_to_add

    return grouped_data


def group_by_date_optimized(results):
    grouped_data = defaultdict(decimal.Decimal)

    # 1. Extract all unique booking_ids upfront
    booking_ids = list(set(result["booking_id"] for result in results))
    print("booking_ids length", len(booking_ids))
    # 2. Bulk fetch all required data
    # Get all latest booking statuses in one query
    latest_booking_statuses = {
        bs.booking_id: bs
        for bs in BookingStatus.objects.filter(
            booking_id__in=booking_ids, is_latest=True
        ).select_related("booking")
    }

    # Get all latest booking no show
    latest_booking_statuses_no_show = {
        bs.booking_id: bs
        for bs in BookingStatus.objects.filter(
            booking_id__in=booking_ids, booking_status="No Show"
        )
        .only("booking_id", "check_in_datetime")
        .distinct("booking_id")
    }

    # Get all room bookings in one query
    room_bookings_dict = defaultdict(list)
    for rb in RoomBooking.objects.filter(booking_id__in=booking_ids):
        room_bookings_dict[rb.booking_id].append(rb)

    for result in results:
        # Parse the date_time string and make it timezone-aware (assuming the original timezone is UTC)
        date_time_str = str(result["date_time"]).replace("Z", "+00:00")
        date_time = datetime.datetime.fromisoformat(date_time_str)
        # Convert to the Asia/Kuala_Lumpur timezone
        date_time_aware = date_time.astimezone(local_timezone)
        # Extract the date part
        date = date_time_aware.date()

        need_reverse = False

        booking_id = result["booking_id"]

        latest_booking_status = latest_booking_statuses.get(booking_id)
        if not latest_booking_status:
            continue

        room_bookings = room_bookings_dict.get(booking_id, [])

        need_reverse = check_need_reverse(
            booking_id=booking_id,
            transaction_datetime=result["transaction_datetime"],
            is_room_booking=result["is_room_booking"],
            latest_booking_status=latest_booking_status,
            room_booking=room_bookings,
            range_above_1_day=False,
        )

        amount_to_add = result["subtotal"] * -1 if need_reverse else result["subtotal"]

        if (
            latest_booking_status.has_no_show
            and latest_booking_status.booking_status
            in ["Confirm Booking", "Check In", "Check Out", "Overstay"]
            and need_reverse is True
        ):
            amount_to_add = 0

        if latest_booking_status.has_no_show:
            no_show_date = latest_booking_statuses_no_show.get(booking_id)
            if no_show_date:
                no_show_date = (no_show_date.check_in_datetime + timedelta(hours=8)).date()
                if no_show_date != date:
                    grouped_data[no_show_date] += result["subtotal"]

        # Sum the subtotal for each date
        grouped_data[date] += amount_to_add

    return grouped_data
