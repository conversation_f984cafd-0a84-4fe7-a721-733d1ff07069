from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.decorators import action
from django.utils.timezone import timedelta
from datetime import datetime
from django.utils import timezone
from bookings.models import ROOM_TYPES_RANKING, Booking, Platform
from bookings.services.booking import (
    exist_room_booking,
    get_available_upgrade,
    get_guest_room_booking_info,
    upgrade_current_room,
)

# Permissions
from accounts import permissions
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

# exceptions
from rooms import exceptions

# services
from rooms.services.zone import (
    create_zone,
    update_zone,
    archive_zone,
    get_all_zone,
    get_zone,
    get_all_filtered_zone,
)
from rooms.services.roomtypes import (
    create_room_types,
    update_room_types,
    archive_room_types,
    get_all_room_types,
    get_room_type,
)
from rooms.services.room import (
    create_room,
    bulk_create_rooms,
    archive_room,
    update_room,
    get_all_room_list,
    get_single_room,
    get_available_rooms_by_zone,
    get_distinct_type_per_zone,
    get_all_zone_types,
    get_occupied_rooms_by_zone,
    get_unique_room_types,
)
from rooms.services.serializers import RoomSerializer

from drf_yasg.utils import swagger_auto_schema
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from rooms.models import (
    ROOMSTATUS,
    PlatformTier,
    Room,
    RoomRate,
    RoomType,
    RoomZone as RoomZoneModels,
    Tiers,
)

from rooms.serializers import (
    ArchiveRoomSerializer,
    ArchiveRoomTypeSerializer,
    ArchiveRoomZoneSerializer,
    AssignPlatformTierSerializer,
    ChangeRateRoomRateSerializer,
    CreateRoomTypeSerializer,
    CreateTierSerializer,
    EditRoomRemarksSerializer,
    EditRoomStatusSerializer,
    EditRoomTypeWithNameSerializer,
    EditZoneSerializer,
    PlatformTierSerializer,
    RoomRateSerializer,
    RoomSerializer,
    RoomStatusListSerializer,
    RoomTypeDetailSerializer,
    RoomTypeForColsSeralizer,
    RoomTypeNameOnlySerializer,
    RoomZoneSerializer,
    TiersSerializer,
    WriteRoomSerializer,
    WriteZoneSerializer,
    ZoneManagementDisplaySerializer,
)
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from drf_yasg import openapi

from django.db.models import Max, OuterRef, Subquery, Q

from accounts.services.housekeeping import create_housekeeping_record

from django.db import transaction
from rooms.utils import process_color_tag

from lot.models import Lot


class Rooms(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-rooms",
        url_name="get-all-rooms",
        permission_classes=[],
    )
    def get_all_rooms(self, request):
        try:
            data = get_all_room_list()
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-room",
        url_name="get-room",
        permission_classes=[],
    )
    def get_room(self, request):
        room_id = request.query_params.get("roomId")
        try:
            data = get_single_room(room_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomNotFound as e:
            return Response(
                {"status": "failed", "msg": "room not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-room",
        url_name="create-room",
        permission_classes=[],
    )
    def create_room(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            create_room(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "room created successfuly",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomExist as e:
            return Response(
                {"status": "failed", "msg": "room code already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoomTypeNotFound as e:
            return Response(
                {"status": "failed", "msg": "type not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="bulk-create-rooms",
        url_name="bulk-create-rooms",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def bulk_create_rooms(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            bulk_create_rooms(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "rooms created successfuly",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomExist as e:
            return Response(
                {"status": "failed", "msg": "there is a room code that already used"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.SameBulkRoomStartandEnd as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "bulk create must have different start number and end number",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoomCodeNotNumber as e:
            return Response(
                {"status": "failed", "msg": "start and end number must be number"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoomTypeNotFound as e:
            return Response(
                {"status": "failed", "msg": "type is not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.RoomCodeLessThanZero as e:
            return Response(
                {"status": "failed", "msg": "room number must be more than zero"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-room",
        url_name="edit-room",
        permission_classes=[permissions.RoomsPermission],
    )
    def edit_room(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            update_room(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "room info edited",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomNotFound as e:
            return Response(
                {"status": "failed", "msg": "room not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="archive-room",
        url_name="archive-room",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def archive_room(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            archive_room(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "room archived",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomNotFound as e:
            return Response(
                {"status": "failed", "msg": "room not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-room-upgrades",
        url_name="get-room-upgrades",
        permission_classes=[],
    )
    def get_room_upgrades(self, request):
        try:
            # Get client's requirements from the request, you can modify this as needed
            booking = get_guest_room_booking_info(
                request.query_params.get("booking_id")
            )
            room_types = get_unique_room_types()
            current_ranking = ROOM_TYPES_RANKING[booking["room_type"]]

            room_rankings = {}
            for room_type in room_types:
                room_rankings[room_type["typeId"]] = ROOM_TYPES_RANKING[
                    room_type["typeName"]
                ]

            available_rooms = []
            for k, v in room_rankings.items():
                if v <= current_ranking:
                    room_upgrade = get_available_upgrade(
                        k,
                        booking["checkin_datetime"],
                        booking["checkout_datetime"],
                        booking["room_id"],
                    )
                    if room_upgrade:
                        available_rooms.append(room_upgrade)

            # Serialize the available rooms using RoomSerializer
            availability_data = RoomSerializer(available_rooms, many=True).data

            return Response(
                {
                    "status": "success",
                    "message": "Room availability generated",
                    "data": availability_data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "Failed to generate room availability",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            200: RoomStatusListSerializer,
            500: "internal server error",
        }
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-room-status",
        url_name="get-all-room-status",
        permission_classes=[],
    )
    def get_all_room_status(self, request):

        try:

            room_status = []
            for stat in list(ROOMSTATUS.choices):
                room_status.append(stat[0])

            serializer = RoomStatusListSerializer({"status_list": room_status})

            return Response(
                {"status": "success", "data": serializer.data["status_list"]},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "falied", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=EditRoomStatusSerializer,
        responses={
            200: "success, room status updated",
            400: "bad request",
            500: "internal server error",
        },
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="change-room-status",
        url_name="change-room-status",
        permission_classes=[],
    )
    def change_room_status(self, request):
        try:
            serializer = EditRoomStatusSerializer(data=request.data)

            if not serializer.is_valid():
                return Response({"status": "failed", "message": serializer.errors})

            validated_data = serializer.validated_data

            room = Room.objects.get(room_id=validated_data["room_id"])

            if room.status != validated_data["new_status"]:
                old_status = room.status
                updated_room = room.update_status(validated_data["new_status"])

                if validated_data["new_status"] == "vacant, cleaned":
                    create_housekeeping_record(
                        new_status=updated_room.status,
                        old_status=old_status,
                        staff_id=request.user.account_id,
                        room_id=updated_room.pk,
                    )

            return Response(
                {"status": "success", "message": "room status changed"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "falied", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=EditRoomRemarksSerializer,
        responses={
            200: "success, room remarks updated",
            400: "bad request",
            500: "internal server error",
        },
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="change-room-remarks",
        url_name="change-room-remarks",
        permission_classes=[],
    )
    def change_room_remarks(self, request):
        try:
            serializer = EditRoomRemarksSerializer(data=request.data)

            if not serializer.is_valid():
                return Response({"status": "failed", "message": serializer.errors})

            validated_data = serializer.validated_data

            room = Room.objects.get(room_id=validated_data["room_id"])
            room.update_remarks(validated_data["new_remarks"])

            return Response(
                {"status": "success", "message": "room remarks changed"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "falied", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Zone
class RoomZone(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-zone",
        url_name="get-zone",
        permission_classes=[],
    )
    def get_zone(self, request):
        zone_id = request.query_params.get("zoneId")
        try:
            data = get_zone(zone_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.ZoneNotFound as e:
            return Response(
                {"status": "failed", "msg": "zone not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-zone-distinct-room-type",
        url_name="get-zone-distinct-room-type",
        permission_classes=[],
    )
    def get_distinct_room_type(self, request):
        try:
            data = get_distinct_type_per_zone(request)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-room-type",
        url_name="get-all-room-type",
        permission_classes=[],
    )
    def get_all_room_type(self, request):
        try:
            data = get_all_zone_types()
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-filtered-zone",
        url_name="get-all-filtered-zone",
        permission_classes=[],
    )
    def get_all_zone_for_filter(self, request):
        try:
            data = get_all_filtered_zone(request)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "specificTypeName",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="filter zone by specific room type name",
            )
        ]
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-zone",
        url_name="get-all-zone",
        permission_classes=[],
    )
    def get_all_zone(self, request):

        specific_type_name = request.GET.get("specificTypeName", None)
        no_duplicate = request.GET.get("noDuplicate", None)
        get_room_lot = request.GET.get("getRoomLot", None)

        try:
            data = get_all_zone(
                user=request.user,
                type_name=specific_type_name,
                no_dupe=no_duplicate,
                get_room_lot=get_room_lot,
            )

            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-occupied-data-details",
        url_name="get-occupied-data-details",
        permission_classes=[],
    )
    def get_occupied_rooms(self, request):
        try:
            data = get_occupied_rooms_by_zone(request)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-available-data-details",
        url_name="get-available-data-details",
        permission_classes=[],
    )
    def get_available_rooms(self, request):
        data = get_available_rooms_by_zone(request)
        try:
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-zone",
        url_name="create-zone",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def create_zone(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            create_zone(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "zone created successfuly",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.ZoneExsist as e:
            return Response(
                {"status": "failed", "msg": "zone already exist"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-zone",
        url_name="edit-zone",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def edit_zone(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            update_zone(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "zone info edited",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.ZoneExsist as e:
            return Response(
                {"status": "failed", "msg": "zone already exist"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.ZoneNotFound as e:
            return Response(
                {"status": "failed", "msg": "zone not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="archive-zone",
        url_name="archive-zone",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def archive_zone(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            archive_zone(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "zone archived",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.ZoneNotFound as e:
            return Response(
                {"status": "failed", "msg": "zone not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.ZoneHaveActiveType as e:
            return Response(
                {"status": "failed", "msg": "zone still have active type"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Types
class RoomTypes(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-type",
        url_name="get-type",
        permission_classes=[],
    )
    def get_room_type(self, request):
        type_id = request.query_params.get("typeId")
        try:
            data = get_room_type(type_id)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomTypeNotFound as e:
            return Response(
                {"status": "failed", "msg": "type not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-unique-type",
        url_name="get-all-unique-type",
        permission_classes=[],
    )
    def get_all_unique_type(self, request):
        try:
            data = get_unique_room_types(user=request.user)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-type",
        url_name="get-all-type",
        permission_classes=[],
    )
    def get_all_type(self, request):
        no_distinct = request.GET.get("noDistinct", False)
        get_room_lot = request.GET.get("getRoomLot", False)
        try:
            data = get_all_room_types(no_distinct=no_distinct, user=request.user, get_room_lot=get_room_lot)
            return Response(
                {"status": "success", "msg": "success", "data": data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-type",
        url_name="create-type",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def create_type(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            create_room_types(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "type created successfuly",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomTypeExist as e:
            return Response(
                {"status": "failed", "msg": "type already exist at selected zone"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.ZoneNotFound as e:
            return Response(
                {"status": "failed", "msg": "zone is not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit-type",
        url_name="edit-type",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def edit_type(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            update_room_types(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "type info edited",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomTypeExist as e:
            return Response(
                {"status": "failed", "msg": "type already exist at selected zone"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoomTypeNotFound as e:
            return Response(
                {"status": "failed", "msg": "type not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="archive-type",
        url_name="archive-type",
        permission_classes=[permissions.AdminAccessPermission],
    )
    def archive_type(self, request):
        req_data = {key: value for key, value in request.POST.items()}

        try:
            archive_room_types(req_data)
            return Response(
                {
                    "status": "success",
                    "msg": "type archived",
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomTypeNotFound as e:
            return Response(
                {"status": "failed", "msg": "type not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.TypeHaveActiveRoom as e:
            return Response(
                {"status": "failed", "msg": "type still have active room"},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="check-availability-3-hours",
        url_name="check-availability-3-hours",
        permission_classes=[],
    )
    def check_availability_3_hours(self, request):
        # Implement your logic to check room availability for 3 hours here
        # You can use the `request` object to get any necessary data
        # Example logic:
        # 1. Query the database to check which rooms are available for 3 hours.
        # 2. Create a list of available rooms and their details.
        # 3. Return this data as "availability_data".

        # TLNOTE : You can combine the availability to one api only with one path parameter
        #          /check-availability/{duration :int }/

        availability_data = {}  # Dictionary to store actual data

        return Response(
            {"status": "success", "msg": "success", "data": availability_data},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="check-availability-6-hours",
        url_name="check-availability-6-hours",
        permission_classes=[],
    )
    def check_availability_6_hours(self, request):
        # Implement your logic to check room availability for 6 hours here

        availability_data = {}

        return Response(
            {"status": "success", "msg": "success", "data": availability_data},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="check-availability-9-hours",
        url_name="check-availability-9-hours",
        permission_classes=[],
    )
    def check_availability_9_hours(self, request):
        # Implement your logic to check room availability for 9 hours here

        availability_data = {}

        return Response(
            {"status": "success", "msg": "success", "data": availability_data},
            status=status.HTTP_200_OK,
        )


class RoomViewSet(viewsets.ModelViewSet):
    queryset = Room.objects.all()
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = RoomSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = self.queryset.prefetch_related("room_type")
        return qs

    @swagger_auto_schema(
        responses={"200": RoomSerializer, "500": "server error"},
        manual_parameters=[
            openapi.Parameter(
                "availableOnly",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter rooms by availability. Set to true to only get available rooms.",
            ),
            openapi.Parameter(
                "roomTypeName",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter rooms by room type name.",
            ),
            openapi.Parameter(
                "archived",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter for not archived room (value : true / false)",
            ),
            openapi.Parameter(
                "zoneId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter for specific zone. Provide zone id (multiple add ':' in between. example = <zone id>:<zone id>)",
            ),
            openapi.Parameter(
                "roomStatus",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter for specific room status (multiple add ':' in between. example = <room status>:<room status>)",
            ),
            openapi.Parameter(
                "withTime",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="get the data with last time its updated time count in seconds",
            ),
            openapi.Parameter(
                "bedLevel",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="filter data by bed level (value : true / false)",
            ),
            openapi.Parameter(
                "quietZone",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="filter data by quiet zone (value : true / false)",
            ),
            openapi.Parameter(
                "remarks",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="filter data by remarks available or not (value : true / false)",
            ),
            openapi.Parameter(
                "sort_archived",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="indicate if archive filter is for sort (value : true / false)",
            ),
        ],
    )
    def list(self, request):

        available_only = request.GET.get("availableOnly", None)
        roomtypeId = request.GET.get("roomTypeId", None)
        archived = request.GET.get("archived", None)
        zone_id = request.GET.get("zoneId", None)
        room_status = request.GET.get("roomStatus", None)
        with_time = request.GET.get("withTime", None)
        bed_level = request.GET.get("bedLevel", None)
        quiet_zone = request.GET.get("quietZone", None)
        remarks = request.GET.get("remarks", None)
        sort_archived = request.GET.get("sortArchived", None)

        try:
            qs = self.get_queryset().order_by("is_archived")

            qs = qs.filter(room_type__roomzone__lot_id_id=request.user.lot_id)

            if available_only:
                qs = qs.filter(status=ROOMSTATUS.VACANT_CLEANED)

            if roomtypeId:
                qs = qs.filter(room_type_id=roomtypeId)

            # archived filter
            if archived and archived == "true":
                if sort_archived:
                    qs = qs.order_by("-is_archived")
                else:
                    qs = qs.filter(is_archived=True)
            if archived and archived == "false":
                if sort_archived:
                    qs = qs.order_by("is_archived")
                else:
                    qs = qs.filter(is_archived=False)

            # quiet zone filter
            if quiet_zone == "true":
                qs = qs.filter(quiet_zone=True)
            if quiet_zone == "false":
                qs = qs.filter(quiet_zone=False)

            if zone_id:
                splited_zone_id = zone_id.split(":")
                qs = qs.filter(room_type__roomzone__zone_id__in=splited_zone_id)

            if room_status:
                splited_room_status = room_status.split(":")
                qs = qs.filter(status__in=splited_room_status)

            # bed level filter
            if bed_level and bed_level == "up":
                qs = qs.filter(is_upper=True)
            if bed_level and bed_level == "down":
                qs = qs.filter(is_upper=False)

            # remarks filter
            if remarks == "true":
                qs = qs.exclude(Q(remarks=""))
            if remarks == "false":
                qs = qs.filter(remarks="")

            if with_time:
                qs = qs.annotate(
                    latest_housekeeping_datetime=Max(
                        "housekeeping__housekeeping_datetime"
                    )
                ).order_by("latest_housekeeping_datetime")

                for data in qs:
                    if not data.latest_housekeeping_datetime:
                        data.last_updated_second = 0
                        continue

                    time_range = timezone.now() - data.latest_housekeeping_datetime
                    time_range = int(time_range.total_seconds())
                    data.last_updated_second = time_range

            serializer = RoomSerializer(qs, many=True)

            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteRoomSerializer,
        responses={
            "201": "room created",
            "400": "bad request",
            "404": "not found",
            "500": "server error",
        },
    )
    def create(self, request):
        serializer = WriteRoomSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                room_type = RoomType.objects.get(
                    type_name=validated_data["room_type_name"],
                    roomzone_id=validated_data["zone_id"],
                )
            except:
                return Response(
                    {"status": "failed", "message": "room type not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if room_type.roomzone.lot_id_id != request.user.lot_id:
                return Response(
                    {
                        "status": "failed",
                        "message": "you dont have permission to create room for this lot",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            room_code_exist = Room.objects.filter(
                room_type__roomzone__lot_id_id=request.user.lot_id,
                room_code=validated_data["room_code"],
            ).exists()

            if room_code_exist:
                return Response(
                    {"status": "failed", "message": "room code already used"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            new_room = Room.objects.create(
                room_code=validated_data["room_code"],
                status="vacant, cleaned",
                is_archived=False,
                remarks=validated_data["remarks"],
                room_type_id=room_type.pk,
                quiet_zone=validated_data["quiet_zone"],
                is_upper=validated_data["is_upper"],
            )

            return Response(
                {"status": "success", "message": "room created"},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteRoomSerializer,
        responses={
            "200": "room edited",
            "400": "bad request",
            "404": "not found",
            "500": "server error",
        },
    )
    @action(
        detail=False,
        methods=["PUT"],
        url_name="edit-room",
        url_path="edit/(?P<room_id>[a-zA-Z0-9-]+)",
    )
    def edit_room(self, request, room_id):
        serializer = WriteRoomSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                room = Room.objects.get(pk=room_id)
            except:
                raise exceptions.RoomNotFound("room not found")

            try:
                room_type = RoomType.objects.get(
                    type_name=validated_data["room_type_name"],
                    roomzone_id=validated_data["zone_id"],
                )
            except:
                raise exceptions.RoomNotFound("room type not found")

            if (
                room_type.roomzone.lot_id_id != request.user.lot_id
                or room.room_type.roomzone.lot_id_id != request.user.lot_id
            ):
                return Response(
                    {
                        "status": "failed",
                        "message": "you dont have permission to edit room from this lot",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            room_code_exist = (
                Room.objects.filter(
                    room_type__roomzone__lot_id_id=request.user.lot_id,
                    room_code=validated_data["room_code"],
                )
                .exclude(pk=room.pk)
                .exists()
            )

            if room_code_exist:
                return Response(
                    {"status": "failed", "message": "room code already used"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            room.room_code = validated_data["room_code"]
            room.remarks = validated_data["remarks"]
            room.quiet_zone = validated_data["quiet_zone"]
            room.room_type_id = room_type.pk
            room.is_upper = validated_data["is_upper"]
            room.save()

            return Response(
                {"status": "success", "message": "room updated"},
                status=status.HTTP_200_OK,
            )

        except exceptions.RoomNotFound as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=ArchiveRoomSerializer,
        responses={
            "200": "success",
            "400": "bad request",
            "404": "room not found",
            "500": "server error",
        },
    )
    @action(
        detail=False,
        methods=["PATCH"],
        url_name="archive-room",
        url_path="archive",
    )
    def archive_room_viewset(self, request):
        serializer = ArchiveRoomSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                room = Room.objects.get(pk=validated_data["room_id"])
            except:
                return Response(
                    {"status": "failed", "message": "room not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            room.is_archived = validated_data["archive"]
            room.save()

            return Response(
                {
                    "status": "success",
                    "message": f"room {'archived' if validated_data['archive'] == True else 'unarchived'}",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={200: RoomSerializer, 500: "server error"},
        manual_parameters=[
            openapi.Parameter(
                "roomTypeId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter rooms by room type id.",
            ),
        ],
    )
    @action(
        detail=False,
        methods=["GET"],
        url_name="generate-available",
        url_path="generate-available",
    )
    def generate_available_room(self, request):

        room_type_name = request.GET.get("roomTypeName", None)
        start_date = request.GET.get("startDate", None)
        end_date = request.GET.get("endDate", None)

        zone_id = request.GET.get("zoneId", None)

        booking_lot = request.GET.get("bookingLot", None)

        try:
            qs = self.get_queryset()
            qs = qs.filter(room_type__roomzone__lot_id_id=booking_lot)
            qs = qs.filter(
                Q(session_lock__isnull=True)
                | Q(session_lock__expiry_at__lte=timezone.now())
            )

            if zone_id:
                qs = qs.filter(room_type__roomzone__zone_id=zone_id)

            if room_type_name:
                qs = qs.filter(room_type__type_name=room_type_name)

            if start_date and end_date:
                booking_exist = exist_room_booking(
                    start_date=start_date,
                    end_date=end_date,
                )
                exclude_room_ids = [room_book.room_id for room_book in booking_exist]

                qs = qs.exclude(room_id__in=exclude_room_ids)

            serializer = RoomSerializer(qs, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


from rest_framework import generics


class RoomTypeViewList(generics.ListAPIView):
    queryset = RoomType.objects.all()
    serializer_class = RoomTypeDetailSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def list(self, request):
        # available_only = request.GET.get('availableOnly', None)
        # roomtypeId = request.GET.get('roomTypeId', None)
        # qs = self.get_queryset()
        # if available_only:
        #     qs = qs.filter(status=ROOMSTATUS.VACANT_CLEANED)

        # if roomtypeId:
        #     qs = qs.filter(room_type_id=roomtypeId)

        # TOFIX : get distinct type name. User Can select the type name and zone name
        qs = self.get_queryset()
        qs = qs.filter(
            archived=False, roomzone__lot_id__lot_id=request.user.lot_id
        ).distinct("type_name")
        serializer = RoomTypeDetailSerializer(qs, many=True)
        return Response(serializer.data)


class RoomTypeViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        qs = RoomType.objects.all()
        return qs

    @swagger_auto_schema(
        responses={200: RoomTypeDetailSerializer, 500: "server error"},
        manual_parameters=[
            openapi.Parameter(
                "notArchived",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="archive filter",
            ),
            openapi.Parameter(
                "zoneName",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="filter by zone name",
            ),
        ],
    )
    def list(self, request):

        not_archived = request.GET.get("notArchived", None)
        zone_name = request.GET.get("zoneName", None)

        try:
            qs = self.get_queryset().prefetch_related("roomzone")

            qs = qs.filter(roomzone__lot_id=request.user.lot_id)

            # archive filter
            if not_archived == "true":
                qs = qs.filter(archived=False)
            if not_archived == "false":
                qs = qs.filter(archived=True)

            if zone_name:
                splited_zone_name = zone_name.split(":")
                qs = qs.filter(roomzone__zone_name__in=splited_zone_name)

            serializer = RoomTypeDetailSerializer(qs, many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def create(self, request):
        serializer = CreateRoomTypeSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            if not validated_data["abbreviation"].isalpha():
                return Response(
                    {
                        "status": "failed",
                        "message": "abbreviation must not contain any number or symbol",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            type_props_exist = RoomType.objects.filter(
                Q(archived=False)
                & Q(roomzone_id=validated_data["zone_id"])
                & Q(
                    Q(type_name__iexact=validated_data["type_name"].lower())
                    | Q(RoomTypeDetails__iexact=validated_data["abbreviation"].lower())
                )
            ).exists()

            if type_props_exist:
                return Response(
                    {
                        "status": "failed",
                        "message": "room type name or abbreviation already exist",
                    },
                    status=status.HTTP_409_CONFLICT,
                )

            processed_color_tag = process_color_tag(
                color_tag=validated_data["color_tag"]
            )
            if not processed_color_tag:
                return Response(
                    {"status": "failed", "message": "color tag not valid"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            RoomType.objects.create(
                type_name=validated_data["type_name"],
                color_tags=processed_color_tag,
                RoomTypeDetails=validated_data["abbreviation"],
                roomzone_id=validated_data["zone_id"],
                max_pax=validated_data["max_pax"],
            )

            return Response(
                {"status": "success", "message": "room type createdd"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=EditRoomTypeWithNameSerializer,
        responses={
            200: "edited",
            404: "room type not found",
            400: "bad request",
            409: "room type name or abbreviation already exist",
            500: "server error",
        },
    )
    @action(
        detail=False,
        methods=["PUT"],
        url_name="edit-with-name",
        url_path="edit-with-name",
    )
    def edit_room_type(self, request):
        serializer = EditRoomTypeWithNameSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            qs = self.get_queryset()

            room_types = qs.filter(
                type_name=validated_data["type_name"], archived=False
            )
            if room_types.count() == 0:
                return Response(
                    {"status": "failed", "message": "room type not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            edit_room_type_ids = [item.type_id for item in room_types]

            if not validated_data["abbreviation"].isalpha():
                return Response(
                    {
                        "status": "failed",
                        "message": "abbreviation must not contain any number or symbol",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            processed_color_tag = process_color_tag(
                color_tag=validated_data["color_tag"]
            )
            if not processed_color_tag:
                return Response(
                    {"status": "failed", "message": "color tag not valid"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            type_exist = (
                qs.filter(
                    Q(archived=False)
                    & Q(
                        Q(type_name__iexact=validated_data["new_type_name"].lower())
                        | Q(
                            RoomTypeDetails__iexact=validated_data[
                                "abbreviation"
                            ].lower()
                        )
                    )
                )
                .exclude(type_id__in=edit_room_type_ids)
                .exists()
            )

            if type_exist:
                return Response(
                    {
                        "status": "failed",
                        "message": "room type name or abbreviation already exist",
                    },
                    status=status.HTTP_409_CONFLICT,
                )

            with transaction.atomic():
                for type in room_types:
                    type.type_name = validated_data["new_type_name"]
                    type.RoomTypeDetails = validated_data["abbreviation"]
                    type.color_tags = processed_color_tag
                    type.max_pax = validated_data["max_pax"]
                    type.save()

            return Response(
                {"status": "success", "message": f"room type edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_name="archive",
        url_path="archive",
    )
    def archive_room_type(self, request):
        serializer = ArchiveRoomTypeSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                room_type = self.get_queryset().get(pk=validated_data["room_type"])
            except:
                return Response(
                    {"status": "failed", "message": "room type not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            room_type.archived = validated_data["archive"]
            room_type.save()

            return Response(
                {
                    "status": "success",
                    "message": f"room type {'archived' if validated_data['archive'] else 'unarchived'}",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={200: RoomTypeNameOnlySerializer, 500: "server error"}
    )
    @action(
        detail=False,
        methods=["GET"],
        url_name="type-name",
        url_path="type-name",
    )
    def room_type_name_list(self, request):
        lot = request.user.lot_id
        try:
            qs = self.get_queryset().filter(archived=False, roomzone__lot_id=lot)

            room_type_name_list = []
            for data in qs:
                if data.type_name in room_type_name_list:
                    continue
                room_type_name_list.append(data.type_name)

            serializer = RoomTypeNameOnlySerializer(
                {"room_type_name": room_type_name_list}
            )

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["GET"], url_name="type-cols", url_path="type-cols")
    def room_type_cols_list(self, request):

        try:

            qs = self.get_queryset().filter(
                archived=False, roomzone__lot_id_id=request.user.lot_id
            )

            room_type_cols_list = []
            encountered_type_names = set()

            for data in qs:
                if data.type_name not in encountered_type_names:
                    room_type_cols_list.append(
                        {"type_name": data.type_name, "color_tag": data.color_tags}
                    )
                    encountered_type_names.add(data.type_name)

            serializer = RoomTypeForColsSeralizer(room_type_cols_list, many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class RoomZoneViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        qs = RoomZoneModels.objects.all()
        return qs

    @swagger_auto_schema(
        responses={200: ZoneManagementDisplaySerializer, 500: "server error"}
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="zone-management",
        url_name="zone-management",
        permission_classes=[],
    )
    def zone_management_list(self, request):

        try:
            qs = self.get_queryset()

            account_lot = request.user.lot_id
            # qs = qs.filter(lot_id_id = account_lot['lot_id'], archived = False)
            qs = qs.filter(lot_id_id=account_lot)

            zone_management_list = []
            for data in qs:
                # all_zone_type = data.room_types.filter(archived=False)

                # zone_rooms = Room.objects.filter(
                #     is_archived = False,
                #     room_type__roomzone_id = data.zone_id
                # )

                all_zone_type = data.room_types

                zone_rooms = Room.objects.filter(room_type__roomzone_id=data.zone_id)

                max_pax = 0
                quiet_zone_available = False
                for room in zone_rooms:
                    max_pax += room.room_type.max_pax

                    if room.quiet_zone and not quiet_zone_available:
                        quiet_zone_available = True

                total_room = zone_rooms.count()

                zone_management_list.append(
                    {
                        "zone_id": data.zone_id,
                        "zone_name": data.zone_name,
                        "quiet_zone_available": quiet_zone_available,
                        "total_room": total_room,
                        "room_types": all_zone_type,
                        "max_pax": max_pax,
                        "archived": data.archived,
                    }
                )

            serializer = ZoneManagementDisplaySerializer(
                zone_management_list, many=True
            )
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteZoneSerializer,
        responses={
            201: "created",
            400: "bad request",
            409: "already exist",
            500: "server error",
        },
    )
    def create(self, request):
        serializer = WriteZoneSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data
            account_lot = request.user.lot_id
            zone_exist = (
                self.get_queryset()
                .filter(
                    zone_name=validated_data["zone_name"],
                    lot_id_id=account_lot,
                    archived=False,
                )
                .exists()
            )

            if zone_exist:
                return Response(
                    {
                        "status": "failed",
                        "message": "zone already exist at the same lot",
                    },
                    status=status.HTTP_409_CONFLICT,
                )

            RoomZoneModels.objects.create(
                zone_name=validated_data["zone_name"],
                lot_id_id=account_lot,
                RoomZoneDetails="",
            )

            return Response(
                {"status": "success", "message": "zone created"},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteZoneSerializer,
        responses={
            201: "created",
            400: "bad request",
            404: "zone not found",
            409: "already exist",
            500: "server error",
        },
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit",
        url_name="edit",
        permission_classes=[],
    )
    def edit_zone(self, request):
        serializer = EditZoneSerializer(data=request.data)

        try:

            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data
            account_lot = request.user.lot_id
            zone_exist = (
                self.get_queryset()
                .filter(
                    zone_name=validated_data["zone_name"],
                    lot_id_id=account_lot,
                    archived=False,
                )
                .exclude(zone_id=validated_data["zone_id"])
                .exists()
            )

            if zone_exist:
                return Response(
                    {"status": "failed", "message": "zone name already used"},
                    status=status.HTTP_409_CONFLICT,
                )

            try:
                update_zone = RoomZoneModels.objects.get(
                    zone_id=validated_data["zone_id"]
                )
            except:
                return Response(
                    {"status": "failed", "message": "zone not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            update_zone.zone_name = validated_data["zone_name"]
            update_zone.save()

            return Response(
                {"status": "success", "message": "zone edited"},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_name="archive",
        url_path="archive",
    )
    def archive_room_zone(self, request):
        serializer = ArchiveRoomZoneSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                room_zone = self.get_queryset().get(pk=validated_data["room_zone"])
            except:
                return Response(
                    {"status": "failed", "message": "room zone not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            room_zone.archived = validated_data["archive"]
            room_zone.save()

            return Response(
                {
                    "status": "success",
                    "message": f"room zone {'archived' if validated_data['archive'] else 'unarchived'}",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class RoomRateViewSet(viewsets.ViewSet):
    queryset = RoomRate.objects.all()
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = RoomRate
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def list(self, request):
        qs = self.queryset.all().prefetch_related("tiers", "room_type")

        specific_platform_tier_booking_id = request.GET.get(
            "specificPlatformTierBookingId", None
        )
        tier_id = request.GET.get("tierId", None)
        latest = request.GET.get("latest", None)

        try:
            qs = qs.filter(room_type__roomzone__lot_id=request.user.lot_id)

            if latest:
                qs = qs.filter(is_latest=True)

            if specific_platform_tier_booking_id:
                try:
                    booking = Booking.objects.get(pk=specific_platform_tier_booking_id)
                except:
                    return Response(
                        {"status": "failed", "message": "booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                lot_id = request.user.lot_id

                platformtier = PlatformTier.objects.get(
                    tiers__lot_id=lot_id, platform_id=booking.platform.pk
                )
                tier = platformtier.tiers
                qs = qs.filter(tiers_id=tier.pk, is_latest=True)

            if tier_id:
                qs = (
                    qs.filter(tiers_id=tier_id)
                    .order_by("room_type__type_name", "hours_of_stay")
                    .distinct("room_type__type_name", "hours_of_stay")
                )

            serializer = RoomRateSerializer(qs, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PATCH"],
        detail=False,
        url_path="change-rate",
        url_name="change-rate",
        permission_classes=[],
    )
    def room_rate_change_rate(self, request):
        serializer = ChangeRateRoomRateSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                new_room_rate_value = validated_data["new_rate"]
            except:
                new_room_rate_value = None

            with transaction.atomic():
                current_room_rate = RoomRate.objects.filter(
                    is_latest=True,
                    room_type__type_name=validated_data["room_type_name"],
                    hours_of_stay=validated_data["hours_of_stay"],
                    tiers_id=validated_data["tiers_id"],
                    room_type__roomzone__lot_id=request.user.lot_id,
                )

                room_rate = current_room_rate.first()

                if current_room_rate.count() > 0:

                    if not new_room_rate_value:
                        for room_rate_data in current_room_rate:
                            room_rate_data.is_latest = False
                            room_rate_data.save()

                        return Response(
                            {"status": "success", "message": "rate changed"},
                            status=status.HTTP_200_OK,
                        )

                    for room_rate_data in current_room_rate:
                        room_rate_data.room_rate = new_room_rate_value
                        room_rate_data.save()
                        room_rate = room_rate_data
                else:
                    if not new_room_rate_value:
                        return Response(
                            {"status": "success", "message": "rate changed"},
                            status=status.HTTP_200_OK,
                        )

                    lots = Lot.objects.all()
                    lot_ids = [lot.pk for lot in lots]

                    room_types = RoomType.objects.filter(
                        type_name=validated_data["room_type_name"],
                        roomzone__lot_id__lot_id__in=lot_ids,
                        archived=False,
                    )

                    if room_types.count() == 0:
                        return Response(
                            {
                                "status": "failed",
                                "message": f"type {validated_data['room_type_name']} not found",
                            },
                            status=status.HTTP_404_NOT_FOUND,
                        )

                    room_rates = []
                    for room_type in room_types:
                        room_rates.append(
                            RoomRate(
                                hours_of_stay=validated_data["hours_of_stay"],
                                room_rate=new_room_rate_value,
                                room_type_id=room_type.pk,
                                tiers_id=validated_data["tiers_id"],
                            )
                        )
                    new_room_rates = RoomRate.objects.bulk_create(room_rates)
                    room_rate = new_room_rates[0]

            return_serializer = RoomRateSerializer(room_rate)

            return Response(
                {
                    "status": "success",
                    "message": "rate changed",
                    "data": return_serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class TiersViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = Tiers.objects.all()
        return qs

    def list(self, request):

        with_account_lot = request.GET.get("withAccountLot", None)

        try:
            qs = self.get_queryset()

            if with_account_lot:
                account_lot = request.user.lot_id
                qs = qs.filter(lot_id=account_lot)

            serializer = TiersSerializer(qs, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def create(self, request):
        serializer = CreateTierSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                if validated_data["platform_id"]:
                    Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            account_lot = request.user.lot_id

            tier_exist = Tiers.objects.filter(
                tier=validated_data["tier"], lot_id=account_lot
            ).exists()

            if tier_exist:
                return Response(
                    {"status": "failed", "message": "tier already exist"},
                    status=status.HTTP_409_CONFLICT,
                )

            new_tier = Tiers.objects.create(
                tier=validated_data["tier"], lot_id=account_lot
            )

            if validated_data["platform_id"]:
                platform_tier = PlatformTier.objects.filter(
                    platform_id=validated_data["platform_id"], tiers__lot_id=account_lot
                ).first()

                if platform_tier:
                    platform_tier.tiers_id = new_tier.pk
                    platform_tier.save()
                else:
                    new_platform_tier = PlatformTier.objects.create(
                        tiers_id=new_tier.pk, platform_id=validated_data["platform_id"]
                    )

            return_serializer = TiersSerializer(new_tier)

            return Response(
                {"status": "", "message": "created", "data": return_serializer.data},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PlatformTierViewSet(viewsets.ViewSet):
    queryset = PlatformTier.objects.all()
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = PlatformTier
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = self.queryset.prefetch_related("tiers", "platform")
        return qs

    def list(self, request):

        with_account_lot = request.GET.get("withAccountLot", None)

        try:
            qs = self.get_queryset()

            if with_account_lot:
                account_lot = request.user.lot_id
                qs = qs.filter(tiers__lot_id=account_lot)

            serializer = PlatformTierSerializer(qs, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            200: PlatformTierSerializer,
            404: "not found",
            500: "internal server error",
        }
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-by-platform/(?P<platform_id>[a-zA-Z0-9-]+)",
        url_name="get-by-platform",
        permission_classes=[],
    )
    def get_platformtier_by_platform_id(self, request, platform_id):

        try:

            qs = self.get_queryset()

            lot = request.user.lot_id

            try:
                qs = qs.get(tiers__lot_id=lot, platform_id=platform_id)
            except:
                return Response(
                    {"status": "failed", "message": "not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = PlatformTierSerializer(qs)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="assign-tier",
        url_name="assign-tier",
        permission_classes=[],
    )
    def assign_platform_tier(self, request):
        serializer = AssignPlatformTierSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            account_lot = request.user.lot_id

            tier = Tiers.objects.get(pk=validated_data["new_tier_id"])
            if tier.lot_id != account_lot:
                return Response(
                    {"status": "failed", "message": "not allowed to use tiers"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            platform_tier = PlatformTier.objects.filter(
                platform_id=validated_data["platform_id"], tiers__lot_id=account_lot
            ).first()

            if platform_tier:
                platform_tier.tiers_id = validated_data["new_tier_id"]
                platform_tier.save()
            else:
                PlatformTier.objects.create(
                    platform_id=validated_data["platform_id"],
                    tiers_id=validated_data["new_tier_id"],
                )

            return Response(
                {"status": "success", "message": "tier assigned"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
