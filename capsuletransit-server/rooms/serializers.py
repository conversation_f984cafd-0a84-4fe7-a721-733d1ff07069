from rooms.models import Room, RoomRate, RoomType, Room<PERSON>one, Tiers, PlatformTier
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers

from bookingplatform.models import Platform

class RoomZoneSerializer(ModelSerializer):
    class Meta:
        model = RoomZone
        fields = "__all__"

class ArchiveRoomZoneSerializer(serializers.Serializer):
    room_zone = serializers.UUIDField()
    archive = serializers.BooleanField()


class RoomTypeSerializer(ModelSerializer):
    class Meta:
        model = RoomType
        fields = "__all__"


class ArchiveRoomTypeSerializer(serializers.Serializer):
    room_type = serializers.UUIDField()
    archive = serializers.BooleanField()


class ZoneManagementDisplaySerializer(serializers.Serializer):
    zone_id = serializers.UUIDField()
    zone_name = serializers.CharField()
    quiet_zone_available = serializers.BooleanField()
    total_room = serializers.IntegerField()
    room_types = RoomTypeSerializer(many=True)
    max_pax = serializers.IntegerField()
    archived = serializers.<PERSON>olean<PERSON>ield()


class WriteZoneSerializer(serializers.Serializer):
    zone_name = serializers.CharField()

class EditZoneSerializer(WriteZoneSerializer):
    zone_id = serializers.UUIDField()



class RoomSerializer(ModelSerializer):
    color_code = serializers.ReadOnlyField(source="room_type.color_tags")
    room_type_details = serializers.ReadOnlyField(source="room_type.RoomTypeDetails")
    room_type_name = serializers.ReadOnlyField(source="room_type.type_name")
    zone = serializers.ReadOnlyField(source="room_type.roomzone.zone_name")
    last_updated_second = serializers.IntegerField(required=False)
    zone_id = serializers.ReadOnlyField(source="room_type.roomzone.zone_id")
    class Meta:
        model = Room
        fields = "__all__"

class WriteRoomSerializer(serializers.Serializer):
    room_code = serializers.CharField()
    room_type_name = serializers.CharField()
    zone_id = serializers.UUIDField()
    quiet_zone = serializers.BooleanField()
    is_upper = serializers.BooleanField()
    remarks = serializers.CharField(allow_blank=True, allow_null=True)


class ArchiveRoomSerializer(serializers.Serializer):
    room_id = serializers.UUIDField()
    archive = serializers.BooleanField()

class RoomTypeDetailSerializer(ModelSerializer):
    roomzone = RoomZoneSerializer()

    class Meta:
        model = RoomType
        fields = "__all__"

class RoomTypeNameOnlySerializer(serializers.Serializer):
    room_type_name = serializers.ListField(child=serializers.CharField())

class RoomTypeForColsSeralizer(serializers.Serializer):
    type_name = serializers.CharField()
    color_tag = serializers.CharField()


class EditRoomTypeWithNameSerializer(serializers.Serializer):
    type_name = serializers.CharField()
    new_type_name = serializers.CharField()
    abbreviation = serializers.CharField()
    color_tag = serializers.CharField()
    max_pax = serializers.IntegerField()

class CreateRoomTypeSerializer(serializers.Serializer):
    type_name = serializers.CharField()
    abbreviation = serializers.CharField()
    color_tag = serializers.CharField()
    max_pax = serializers.IntegerField()
    zone_id = serializers.UUIDField()


class RoomDetailSerializer(ModelSerializer):
    room_type = RoomTypeDetailSerializer()

    class Meta:
        model = Room
        fields = ("room_id", "room_code", "room_type")


class RoomSerializerForExpressBookingRequest(serializers.ModelSerializer):
    room_type = RoomTypeDetailSerializer()

    class Meta:
        model = Room
        fields = "__all__"


class PlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = Platform
        ref_name = "PlatformSerializer for room module"
        fields = "__all__"
class TiersSerializer(serializers.ModelSerializer):

    class Meta:
        model = Tiers
        fields = "__all__"


class CreateTierSerializer(serializers.Serializer):
    platform_id = serializers.UUIDField(allow_null=True)
    tier = serializers.IntegerField()

class PlatformTierSerializer(serializers.ModelSerializer):
    tiers = TiersSerializer()
    platform = PlatformSerializer()
    class Meta:
        model = PlatformTier
        fields = "__all__"

class AssignPlatformTierSerializer(serializers.Serializer):
    platform_id = serializers.UUIDField()
    new_tier_id = serializers.UUIDField()

class RoomRateSerializer(serializers.ModelSerializer):
    room_type_name = serializers.ReadOnlyField(source="room_type.type_name")
    room_type_color = serializers.ReadOnlyField(source="room_type.color_tags")
    tiers = TiersSerializer()

    class Meta:
        model = RoomRate
        fields = "__all__"

class ChangeRateRoomRateSerializer(serializers.Serializer):
    new_rate = serializers.DecimalField(max_digits=16, decimal_places=2, allow_null=True)
    hours_of_stay = serializers.IntegerField()
    room_type_name = serializers.CharField()
    tiers_id = serializers.UUIDField()


class EditRoomStatusSerializer(serializers.Serializer):
    room_id = serializers.UUIDField()
    new_status = serializers.CharField()


class EditRoomRemarksSerializer(serializers.Serializer):
    room_id = serializers.UUIDField()
    new_remarks = serializers.CharField(allow_blank=True)


class RoomStatusListSerializer(serializers.Serializer):
    status_list = serializers.ListField(child=serializers.CharField())