from datetime import timedelta, datetime
from django.utils.timezone import datetime, make_aware, timedelta
from django.utils import timezone
from rooms.models import Room, RoomType, RoomZone
from bookings.models import ROOM_TYPES_RANKING, Booking, BookingStatus, RoomBooking
from django.db import connection
from django.db.models import Count, Q, F, Subquery
import re

from bookings.services.booking import exist_room_booking

# exceptions
from rooms import exceptions


def check_room_availability(room_id, duration_hours):
    try:
        room = Room.objects.get(pk=room_id, archived=False)
    except Room.DoesNotExist:
        raise exceptions.RoomNotFound("Room Not Found")

    now = timezone.now()
    end_time = now + timedelta(hours=duration_hours)

    # Check if the room is booked during the specified duration
    conflicting_bookings = RoomBooking.objects.filter(
        Q(room=room)
        & (
            Q(start_time__lte=now, end_time__gte=now)
            | Q(start_time__lte=end_time, end_time__gte=end_time)
        )
    ).exclude(booking_status__booking_status="Cancelled")

    if conflicting_bookings.exists():
        return False
    else:
        return True


def get_single_room(room_id):
    try:
        room = Room.objects.get(pk=room_id, is_archived=False)
    except:
        raise exceptions.RoomNotFound("room not found")

    bed_level = "-"
    try:
        if isinstance(int(room.room_code), int):
            if int(room.room_code) % 2 == 0:
                bed_level = "up"
            else:
                bed_level = "bottom"
    except:
        pass

    data = {
        "roomId": room.room_id,
        "roomNo": room.room_code,
        "roomType": room.room_type.type_name,
        "roomColor": room.room_type.color_tags,
        "roomStatus": room.status,
        "roomZone": str(room.room_type.roomzone),
        "roomRemarks": room.remarks,
        "roomQuietZone": room.quiet_zone,
        "bedLevel": bed_level,
        "isArchived": room.is_archived,
        "maxPax": room.room_type.max_pax,
    }

    return data


def get_distinct_type_per_zone(request):
    """
    filtered by status = vacant, dirty
    """
    received_roomtype = request.query_params.getlist("zone_roomtype_filter[]")

    account_lot = request.user.lot_id
    rooms = (
        RoomType.objects.filter(
            type_name__in=received_roomtype, roomzone__lot_id_id=account_lot
        )
        .annotate(
            num_dirty_rooms=Count(
                "rooms__status",
                filter=(
                    (Q(rooms__status__in=["vacant, dirty", "maintentance"]))
                    & Q(archived=False)
                    # & Q(roomzone=F("roomzone__zone_id"))
                ),
            )
        )
        .values(
            "roomzone__zone_id",
            "roomzone__zone_name",
            "type_name",
            "type_id",
            "color_tags",
            "num_dirty_rooms",
        )
        .distinct()
    )
    return rooms


def get_all_zone_types():
    roomTypes = RoomType.objects.filter(archived=False)
    data = []
    for rtype in roomTypes:
        data.append(
            {
                "zoneId": rtype.roomzone_id,
                "typeName": rtype.type_name,
                "typeId": rtype.type_id,
                "colorTags": rtype.color_tags,
            }
        )
    return data


def get_unique_room_types(user):
    room_types = RoomType.objects.filter(archived=False).distinct("type_name")
    room_types = room_types.filter(roomzone__lot_id=user.lot_id)
    data = []
    for rtype in room_types:
        type_name = rtype.type_name
        ranking = ROOM_TYPES_RANKING.get(type_name, None)
        data.append(
            {
                "zoneId": rtype.roomzone_id,
                "typeName": rtype.type_name,
                "typeId": rtype.type_id,
                "colorTags": rtype.color_tags,
                "ranking": ranking,
            }
        )
    return data


def get_all_room_list():
    rooms = Room.objects.filter(is_archived=False).select_related(
        "room_type", "room_type__roomzone"
    )

    data = []
    number_data = []
    non_number_data = []

    for room in rooms:
        bed_level = "-"
        try:
            if isinstance(int(room.room_code), int):
                if int(room.room_code) % 2 == 0:
                    bed_level = "up"
                else:
                    bed_level = "bottom"
        except:
            pass

        data.append(
            {
                "roomId": room.room_id,
                "roomNo": room.room_code,
                "roomType": room.room_type.type_name,
                "roomColor": room.room_type.color_tags,
                "roomStatus": room.status,
                "roomZone": str(room.room_type.roomzone),
                "roomRemarks": room.remarks,
                "roomQuietZone": room.quiet_zone,
                "bedLevel": bed_level,
                "isArchived": room.is_archived,
                "maxPax": room.room_type.max_pax,
            }
        )

    for item in data:
        if re.match(r"^\d+$", item["roomNo"]):  # Check if roomNo is a number
            number_data.append(item)
        else:
            non_number_data.append(item)

    sorted_number_data = sorted(number_data, key=lambda x: int(x["roomNo"]))
    sorted_non_number_data = sorted(non_number_data, key=lambda x: x["roomNo"])

    sorted_data = sorted_number_data + sorted_non_number_data
    return sorted_data


def create_room(data):
    try:
        type_available = RoomType.objects.get(pk=data["room_type_id"])
    except:
        raise exceptions.RoomTypeNotFound("type not found")

    room_available = Room.objects.filter(room_code=data["room_code"])

    if len(room_available) > 0 and room_available.first().archived:
        unarchive = room_available.first().unarchive_room(
            room_type_id=data["room_type_id"]
        )
        return unarchive

    if len(room_available) > 0 and not room_available.first().archived:
        raise exceptions.RoomExist("room code already used")

    created_room = Room.objects.create(
        room_code=data["room_code"], room_type_id=data["room_type_id"]
    )
    return created_room


def get_occupied_rooms_by_zone(request):
    """
    - filtered by room that haveing roomBookings in range of the durations
    - filtered ny room status = occupied, cleaned, occupied, dirty, maintenance
    """
    received_year = int(request.query_params.get("year"))
    received_month = int(request.query_params.get("month"))
    received_day = int(request.query_params.get("day"))
    received_hour = int(request.query_params.get("hour"))
    received_minute = int(request.query_params.get("minute"))
    duration = int(request.query_params.get("duration"))
    bed_type = request.query_params.get("bed_type")
    bed_type_boolean = True if bed_type == "top" else False

    rooms = Room.objects.all()

    if bed_type != "all":
        rooms.filter(is_upper=bed_type_boolean)

    filtered_rooms = rooms.filter(
        # status="vacant, cleaned",
        is_archived=False,
    )

    end_of_the_day = datetime.combine(
        timezone.localdate(), datetime.min.time()
    ) + timedelta(hours=23, minutes=59, seconds=59)
    end_of_the_day = timezone.make_aware(
        end_of_the_day, timezone.get_current_timezone()
    )

    current_datetime = make_aware(
        datetime(
            year=received_year,
            month=received_month,
            day=received_day,
            hour=received_hour,
            minute=received_minute,
        ),
        timezone=timezone.utc,
    )

    seconds_to_next_15_minutes = (
        15 - current_datetime.minute % 15
    ) * 60 - current_datetime.second
    current_datetime += timedelta(seconds=seconds_to_next_15_minutes)
    rounded_datetime = current_datetime.replace(second=0, microsecond=0)

    added_duration_datetime = rounded_datetime + timedelta(hours=duration)

    included_room_bookings = exist_room_booking(
        start_date=current_datetime,
        end_date=added_duration_datetime
    )

    occupied_room = Room.objects.filter(
        status__in=("occupied, cleaned", "occupied, dirty"),
    )
    occupied_room_ids = [room.pk for room in occupied_room]
    included_room_ids = [
        room_booking.room_id for room_booking in included_room_bookings
    ]

    final_occupied_room_ids = list(set(occupied_room_ids + included_room_ids))

    queryset = filtered_rooms.filter(pk__in=final_occupied_room_ids)

    test = Room.objects.filter(
        room_type_id="edb82850-2ed0-878b-5763-c8da3d12bfbf"
    ).count()
    print("count", test)

    zone_occupied_room = queryset.values(
        "room_type__roomzone__zone_id", "room_type__type_id", "room_type__type_name"
    ).annotate(
        room_count=Count("room_type"),
        zone_id=F("room_type__roomzone__zone_id"),
        type_id=F("room_type__type_id"),
        type_name=F("room_type__type_name"),
    )

    return zone_occupied_room


def get_available_rooms_by_zone(request):
    """
    - filtered by room that doesn't have any roomBookings in range of the durations
    - filtered ny room status = vacant,cleaned
    """
    received_year = int(request.query_params.get("year"))
    received_month = int(request.query_params.get("month"))
    received_day = int(request.query_params.get("day"))
    received_hour = int(request.query_params.get("hour"))
    received_minute = int(request.query_params.get("minute"))
    duration = int(request.query_params.get("duration"))
    bed_type = request.query_params.get("bed_type")
    bed_type_boolean = True if bed_type == "top" else False
    # filter room based on base validation
    rooms = Room.objects.all()

    if bed_type != "all":
        rooms.filter(is_upper=bed_type_boolean)

    filtered_rooms = rooms.filter(
        # status="vacant, cleaned",
        is_archived=False,
    )
    end_of_the_day = datetime.combine(
        timezone.localdate(), datetime.min.time()
    ) + timedelta(hours=23, minutes=59, seconds=59)
    end_of_the_day = timezone.make_aware(
        end_of_the_day, timezone.get_current_timezone()
    )

    current_datetime = make_aware(
        datetime(
            year=received_year,
            month=received_month,
            day=received_day,
            hour=received_hour,
            minute=received_minute,
        )
    )

    seconds_to_next_15_minutes = (
        15 - current_datetime.minute % 15
    ) * 60 - current_datetime.second
    current_datetime += timedelta(seconds=seconds_to_next_15_minutes)
    rounded_datetime = current_datetime.replace(second=0, microsecond=0)

    added_duration_datetime = rounded_datetime + timedelta(hours=duration)

    exclude_room_bookings = exist_room_booking(
        start_date=current_datetime,
        end_date=added_duration_datetime
    )

    excluded_room_ids = [room_booking.room_id for room_booking in exclude_room_bookings]
    queryset = filtered_rooms.exclude(pk__in=excluded_room_ids)

    queryset = queryset.filter(status="vacant, cleaned")

    zone_available_room = queryset.values(
        "room_type__roomzone__zone_id", "room_type__type_id", "room_type__type_name"
    ).annotate(
        room_count=Count("room_type"),
        zone_id=F("room_type__roomzone__zone_id"),
        type_id=F("room_type__type_id"),
        type_name=F("room_type__type_name"),
    )

    return zone_available_room


def bulk_create_rooms(data):
    try:
        start_num = int(data["start_number"])
        end_num = int(data["end_number"])

        if start_num < 0 or end_num < 0:
            raise exceptions.RoomCodeLessThanZero("room number must be more than zero")
    except:
        raise exceptions.RoomCodeNotNumber("start and end number must be number")

    if start_num == end_num:
        raise exceptions.SameBulkRoomStartandEnd(
            "bulk create must have different start number and end number"
        )

    try:
        type_available = RoomType.objects.get(pk=data["room_type_id"])
    except:
        raise exceptions.RoomTypeNotFound("type not found")

    rooms_arr = []
    for num in range(start_num, end_num + 1):
        duplicate = Room.objects.filter(room_code=num).first()

        if duplicate and duplicate.archived:
            duplicate.unarchive_room(room_type_id=data["room_type_id"])
            continue

        if duplicate and not duplicate.archived:
            raise exceptions.RoomExist("there is a room code that already used")

        rooms_arr.append(Room(room_code=num, room_type_id=data["room_type_id"]))

    created_rooms = Room.objects.bulk_create(rooms_arr)
    return created_rooms


def archive_room(data):
    try:
        room = Room.objects.get(pk=data["room_id"], archived=False)
    except:
        raise exceptions.RoomNotFound("room not found")

    room.archived = True
    updated_room = room.save()
    return updated_room


def update_room(data):
    try:
        room = Room.objects.get(pk=data["room_id"], archived=False)
    except:
        raise exceptions.RoomNotFound("room not found")

    room.status = data["status"]
    room.remarks = data["remarks"]
    updated_room = room.save()

    return updated_room



def generate_available(
    start_date : datetime,
    end_date : datetime,
    room_type_name : str,
    quantity : int,
    lot_id : int
):
    
    available_rooms = Room.objects.select_related(
        "room_type", "room_type__roomzone"
    ).filter(
        Q(room_type__type_name = room_type_name) &
        Q(room_type__roomzone__lot_id_id=lot_id) &
        Q(is_archived=False) &
        Q(
            Q(session_lock__isnull=True) |
            Q(session_lock__expiry_at__lte=timezone.now())
        )
    )

    # Exclude rooms that are already booked
    exclude_room_bookings = exist_room_booking(
        start_date=start_date,
        end_date=end_date,
        room_type_name=room_type_name
    )

    excluded_ids = [
        room_booking.room_id for room_booking in exclude_room_bookings
    ]
    available_rooms = available_rooms.exclude(
        room_id__in=excluded_ids
    ).order_by("room_code")

    if available_rooms.count() < quantity:
        return []
    
    available_rooms = available_rooms[:quantity]

    return available_rooms
