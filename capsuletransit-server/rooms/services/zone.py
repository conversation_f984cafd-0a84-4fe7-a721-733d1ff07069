from rooms.models import RoomZone, RoomType
from django.db import transaction
from django.db.models import Count, Q

# exceptions
from rooms import exceptions


def get_zone(zone_id):
    try:
        zone = RoomZone.objects.get(pk=zone_id, archived=False)
    except:
        raise exceptions.ZoneNotFound("zone not found")

    data = {
        "zoneId": zone.zone_id,
        "zoneName": zone.zone_name,
    }
    return data


def get_all_zone(user, type_name=None, no_dupe=None, get_room_lot=None):
    account_lot = user.lot_id

    lot_to_use = get_room_lot if get_room_lot else account_lot

    zones = RoomZone.objects.filter(archived=False, lot_id_id=lot_to_use).order_by(
        "zone_name"
    )

    if type_name:
        room_types = RoomType.objects.filter(
            type_name=type_name, roomzone__lot_id=lot_to_use, archived=False
        )
        zone_ids = [type.roomzone_id for type in room_types]

        zones = zones.filter(pk__in=zone_ids)

    if no_dupe:
        no_dupe_list = []
        no_dupe_name_list = []
        for zone in zones:
            if zone.zone_name in no_dupe_name_list:
                continue
            no_dupe_list.append(zone)
            no_dupe_name_list.append(zone.zone_name)

        zones = no_dupe_list

    data = []
    for zone in zones:
        data.append(
            {
                "zoneId": zone.zone_id,
                "zoneName": zone.zone_name,
            }
        )

    return data


def get_all_filtered_zone(request):
    received_zones = request.query_params.getlist("zone_filter[]")

    account_lot = request.user.lot_id
    zones = (
        RoomZone.objects.filter(
            zone_name__in=received_zones, archived=False, lot_id_id=account_lot
        )
        .order_by("zone_name")
        .distinct()
    )

    data = []
    for zone in zones:
        data.append(
            {
                "zoneId": zone.zone_id,
                "zoneName": zone.zone_name,
            }
        )

    return data


def create_zone(data):
    zone_available = RoomZone.objects.filter(zone_name=data["zone_name"])

    exist = False
    for zone in zone_available:
        if not zone.archived:
            exist = True
            break

    if len(zone_available) > 0 and not exist:
        unarchive = zone_available.first().unarchive_zone()
        return unarchive

    if len(zone_available) > 0 and exist:
        raise exceptions.ZoneExsist("Zone already exist")

    created_zone = RoomZone.objects.create(zone_name=data["zone_name"])

    return created_zone


def update_zone(data):
    zone_available = RoomZone.objects.filter(
        zone_name=data["zone_name"], archived=False
    )
    if len(zone_available) > 0:
        raise exceptions.ZoneExsist("Zone already exist")

    try:
        zone = RoomZone.objects.get(pk=data["zone_id"], archived=False)
    except:
        raise exceptions.ZoneNotFound("zone not found")

    zone.zone_name = data["zone_name"]
    updated_zone = zone.save()
    return updated_zone


def archive_zone(data):
    try:
        zone = RoomZone.objects.get(pk=data["zone_id"], archived=False)
    except:
        raise exceptions.ZoneNotFound("zone not found")

    active_type = RoomType.objects.filter(type_zone_id=data["zone_id"], archived=False)
    if len(active_type) > 0:
        raise exceptions.ZoneHaveActiveType("zone still have active type")

    zone.archived = True
    updated_zone = zone.save()
    return updated_zone
