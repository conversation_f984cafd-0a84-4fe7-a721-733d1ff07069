from rest_framework import serializers
from bookings.serializer import BookingPeriodSerializer


from rooms.models import Room, RoomType, RoomZone


class RoomTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomType
        exclude = ("type_zone",)


class RoomZoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomZone
        fields = "__all__"


class RoomSerializer(serializers.ModelSerializer):
    room_type_name = serializers.CharField()
    roomzone = serializers.CharField()
    color_code = serializers.CharField()
    booking_periods = BookingPeriodSerializer(many=True)
    room_type_details = serializers.CharField()
    max_pax = serializers.IntegerField()

    class Meta:
        model = Room
        exclude = ("is_archived",)
