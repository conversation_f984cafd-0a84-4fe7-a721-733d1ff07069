from rooms.models import RoomType, RoomZone, Room
from django.db import transaction
import uuid

from bookings.models import ROOM_TYPES_RANKING

# exceptions
from rooms import exceptions


def get_room_type(type_id):
    try:
        type = RoomType.objects.get(pk=type_id, archived=False)
    except:
        raise exceptions.RoomTypeNotFound("room type not found")

    data = {
        "typeId": type.type_id,
        "typeName": type.type_name,
        "typeZone": type.roomzone.zone_name,
    }
    return data


def get_all_room_types(user, no_distinct=False, get_room_lot=None):
    types = RoomType.objects.filter(
        archived=False, roomzone__lot_id=get_room_lot if get_room_lot else user.lot_id
    )

    if not no_distinct:
        types = types.distinct("type_name")

    data = []
    for type in types:
        ranking = ROOM_TYPES_RANKING.get(type.type_name, None)
        data.append(
            {
                "typeId": type.type_id,
                "typeZone": type.roomzone.zone_name,
                "typeName": type.type_name,
                "colorTags": type.color_tags,
                "maxPax": type.max_pax,
                "zoneId": type.roomzone.pk,
                "ranking": ranking,
            }
        )

    return data


def create_room_types(data):
    try:
        zone_available = RoomZone.objects.get(pk=data["zone_id"])
    except:
        raise exceptions.ZoneNotFound("zone not found")

    type_available = RoomType.objects.filter(type_name=data["type_name"])

    for type in type_available:
        if type.type_zone.zone_id == uuid.UUID(data["zone_id"]) and type.archived:
            unarchived = type.unarchive_room_type()
            return unarchived

        if type.type_zone.zone_id == uuid.UUID(data["zone_id"]) and not type.archived:
            raise exceptions.RoomTypeExist("type already exist at selected zone")

    created_type = RoomType.objects.create(
        type_name=data["type_name"], type_zone_id=data["zone_id"]
    )

    return created_type


def update_room_types(data):
    try:
        type = RoomType.objects.get(pk=data["room_type_id"], archived=False)
    except:
        raise exceptions.RoomTypeNotFound("type not found")

    type_available = RoomType.objects.filter(
        type_name=data["type_name"], archived=False
    )
    for type_ in type_available:
        if type_.type_zone.zone_id == type.type_zone.zone_id:
            raise exceptions.RoomTypeExist("type already exist at selected zone")

    type.type_name = data["type_name"]
    updated_type = type.save()
    return updated_type


def archive_room_types(data):
    try:
        type = RoomType.objects.get(pk=data["room_type_id"], archived=False)
    except:
        raise exceptions.RoomTypeNotFound("type not found")

    active_rooms = Room.objects.filter(
        room_type_id=data["room_type_id"], archived=False
    )
    if len(active_rooms) > 0:
        raise exceptions.TypeHaveActiveRoom("type still have active room")

    type.archived = True
    updated_type = type.save()
    return updated_type
