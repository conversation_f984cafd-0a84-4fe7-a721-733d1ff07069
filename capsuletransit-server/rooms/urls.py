from rest_framework.routers import DefaultRouter
from django.urls import path, include

# Route
from .views import RoomZoneViewSet, Rooms, RoomZone, RoomTypes, RoomViewSet, RoomTypeViewList, RoomRateViewSet, PlatformTierViewSet, RoomTypeViewSet, TiersViewSet

router = DefaultRouter()
router.register("", Rooms, basename="rooms")
router.register("v1/rooms", RoomViewSet, basename="room")
router.register("v1/types", RoomTypeViewSet, basename="room-type-viewset")
router.register("v1/zone", RoomZoneViewSet, basename="room-zone-viewset")
router.register("zone", RoomZone, basename="room-zone")
router.register("type", RoomTypes, basename="room-type")
router.register("rate", RoomRateViewSet, basename="room-rate")
router.register("tiers", TiersViewSet , basename="tier")
router.register("platform-tier", PlatformTierViewSet , basename="platform-tier")

urlpatterns = router.urls

# Define your custom action URLs
custom_action_urls = [
    path(
        "check-availability-3-hours/",
        Rooms.as_view({"get": "check_availability_3_hours"}),
        name="check-availability-3-hours",
    ),
    path(
        "check-availability-6-hours/",
        Rooms.as_view({"get": "check_availability_6_hours"}),
        name="check-availability-6-hours",
    ),
    path(
        "check-availability-9-hours/",
        Rooms.as_view({"get": "check_availability_9_hours"}),
        name="check-availability-9-hours",
    ),
    path('v1/room-type/list/', RoomTypeViewList.as_view(), name='room-type-list'),
]

# Include the router and custom action URLs in your urlpatterns
urlpatterns = [
    path("", include(router.urls)),
] + custom_action_urls
