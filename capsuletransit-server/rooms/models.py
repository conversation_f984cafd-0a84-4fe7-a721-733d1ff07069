from typing import List
import uuid
from django.db import models
from lot.models import Lot
from uuid import uuid4
from django.db.models import Manager
from django.db.models import Q

class AvailbleO<PERSON><PERSON><PERSON>anager(Manager):
    def get_queryset(self):
        queries = {
            'is_archived': False,
            'status' : ROOMSTATUS.VACANT_CLEANED,
        }
        qs1 = super().get_queryset().filter(**queries)
        return qs1


class RoomZone(models.Model):
    zone_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    lot_id = models.ForeignKey(
        Lot,
        verbose_name="Room Zone Lot",
        on_delete=models.RESTRICT,
        related_name="RoomZone",
    )

    zone_name = models.CharField(max_length=50)
    archived = models.BooleanField(default=False, null=True, blank=True)

    # New Field for RoomZoneDetails
    RoomZoneDetails = models.TextField(
        verbose_name="Room Zone Details", null=True, blank=True
    )

    def __str__(self):
        return self.zone_name

    def unarchive_zone(self):
        self.archived = False
        self.save()
        return self


class RoomType(models.Model):
    # New Field for roomzone
    roomzone = models.ForeignKey(
        "RoomZone", on_delete=models.CASCADE, verbose_name="RoomZone ID", null=True, related_name="room_types"
    )
    type_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    type_name = models.CharField(max_length=50)
    archived = models.BooleanField(default=False, null=True, blank=True)

    # New field for color tags
    color_tags = models.CharField(
        max_length=7, null=False, default="#FFFFFF", verbose_name="Color Tags"
    )

    # New Field for RoomTypeDetails
    RoomTypeDetails = models.TextField(
        verbose_name="Room Type Details", null=True, blank=True
    )

    # max guest pax for each room type
    max_pax = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return self.type_name

    def unarchive_room_type(self):
        self.archived = False
        self.save()
        return self


ROOM_STATUS = (
    ("vacant, cleaned", "vacant, cleaned"),
    ("vacant, dirty", "vacant, dirty"),
    ("occupied, cleaned", "occupied, cleaned"),
    ("occupied, dirty", "occupied, dirty"),
    ("maintenance", "maintenance"),
)
class ROOMSTATUS:
    VACANT_CLEANED = "vacant, cleaned"
    VACANT_DIRTY = "vacant, dirty"
    OCCUPIED_CLEANED =  "occupied, cleaned"
    OCCUPIED_DIRTY = "occupied, dirty"
    MAINTENANCE = "maintenance"
    choices = (
        (VACANT_CLEANED, "vacant, cleaned"),
        (VACANT_DIRTY, "vacant, dirty"),
        (OCCUPIED_CLEANED, "occupied, cleaned"),
        (OCCUPIED_DIRTY, "occupied, dirty"),
        (MAINTENANCE, "maintenance"),
    )




class Room(models.Model):
    room_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    room_code = models.CharField(max_length=50)
    room_type = models.ForeignKey(
        RoomType, on_delete=models.CASCADE, verbose_name=("Room Type"), related_name="rooms"
    )
    status = models.CharField(
        choices=ROOMSTATUS.choices, max_length=50, default="Available", blank=True, null=True
    )
    is_archived = models.BooleanField(default=False, null=True, blank=True)
    remarks = models.CharField(max_length=100, default="", blank=True, null=True)
    quiet_zone = models.BooleanField(default=False, null=True, blank=True)
    is_upper = models.BooleanField(default=False)

    available_objects = AvailbleOnlyManager()
    objects = models.Manager()

    @property
    def is_available(self):
        return self.status == ROOMSTATUS.VACANT_CLEANED

    def __str__(self):
        return self.room_code

    def unarchive_room(self, room_type_id):
        self.archived = False
        self.room_type_id = room_type_id
        self.status = "Available"
        self.remarks = ""
        self.save()
        return self
    
    def related_room_rate(self, platfrom_id):
        lot_id = self.room_type.roomzone.lot_id_id
        platformtier = PlatformTier.objects.get(
            tiers__lot_id = lot_id,
            platform_id = platfrom_id
        )
        tier = platformtier.tiers

        room_rates = RoomRate.objects.filter(
            room_type_id = self.room_type_id,
            tiers_id = tier.pk,
            is_latest = True
        ).order_by('hours_of_stay')
        
        return room_rates
    
    def update_status(self, new_status):
        self.status = new_status
        self.save()
        return self
    
    def update_remarks(self, new_remarks):
        self.remarks = new_remarks
        self.save()
        return self



class Tiers(models.Model):
    tiers_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    tier = models.IntegerField(null = False)
    lot = models.ForeignKey(
        Lot,
        verbose_name="Tiers Lot",
        on_delete=models.RESTRICT
    )

class PlatformTier(models.Model):
    platformtier_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    tiers = models.ForeignKey(
        Tiers,
        verbose_name="Tiers",
        on_delete=models.CASCADE,
        null=True,
        default=None
    )
    platform = models.ForeignKey(
        "bookingplatform.Platform",
        verbose_name="Booking Platform",
        on_delete=models.CASCADE
    )

class RoomRate(models.Model):
    room_rate_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    room_type = models.ForeignKey(RoomType, on_delete=models.CASCADE)
    hours_of_stay = models.IntegerField(null=False)
    room_rate = models.DecimalField(max_digits=100, decimal_places=2)
    is_latest = models.BooleanField(default=True)
    tiers = models.ForeignKey(
        Tiers,
        verbose_name="Tiers",
        on_delete=models.CASCADE,
        null=True
    )

