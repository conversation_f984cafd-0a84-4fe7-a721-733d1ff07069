from .models import Room

def get_total_room(not_archived : bool = False):
    rooms = Room.objects.all()

    if not_archived:
        rooms = rooms.filter(is_archived = False)

    return rooms.count()


def process_color_tag(color_tag):
    color_tag = color_tag.strip()

    # Check if the string contains "#"
    if "#" not in color_tag:
        color_tag = "#" + color_tag
    
    if color_tag.count("#") > 1:
        color_tag = color_tag.lstrip("#")

    # Ensure the length is exactly 6 characters (excluding "#")
    if len(color_tag) == 7 and color_tag[0] == "#":
        return color_tag
    else:
        return None