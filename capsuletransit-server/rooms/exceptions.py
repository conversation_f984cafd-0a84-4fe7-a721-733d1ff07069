

class ZoneExsist(Exception):
    pass

class ZoneNotFound(Exception):
    pass

class ZoneHaveActiveType(Exception):
    pass


class RoomTypeExist(Exception):
    pass

class RoomTypeNotFound(Exception):
    pass

class TypeHaveActiveRoom(Exception):
    pass



class RoomExist(Exception):
    pass

class RoomNotFound(Exception):
    pass

class SameBulkRoomStartandEnd(Exception):
    pass

class RoomCodeNotNumber(Exception):
    pass

class RoomCodeLessThanZero(Exception):
    pass