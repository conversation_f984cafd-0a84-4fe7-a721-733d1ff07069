# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("lot", "0001_initial"),
        ("bookingplatform", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Tiers",
            fields=[
                (
                    "tiers_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("tier", models.IntegerField()),
                (
                    "lot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="lot.lot",
                        verbose_name="Tiers Lot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RoomZone",
            fields=[
                (
                    "zone_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("zone_name", models.CharField(max_length=50)),
                ("archived", models.BooleanField(blank=True, default=False, null=True)),
                (
                    "RoomZoneDetails",
                    models.TextField(
                        blank=True, null=True, verbose_name="Room Zone Details"
                    ),
                ),
                (
                    "lot_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="RoomZone",
                        to="lot.lot",
                        verbose_name="Room Zone Lot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RoomType",
            fields=[
                (
                    "type_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("type_name", models.CharField(max_length=50)),
                ("archived", models.BooleanField(blank=True, default=False, null=True)),
                (
                    "color_tags",
                    models.CharField(
                        default="#FFFFFF", max_length=7, verbose_name="Color Tags"
                    ),
                ),
                (
                    "RoomTypeDetails",
                    models.TextField(
                        blank=True, null=True, verbose_name="Room Type Details"
                    ),
                ),
                ("max_pax", models.IntegerField(blank=True, null=True)),
                (
                    "roomzone",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="room_types",
                        to="rooms.roomzone",
                        verbose_name="RoomZone ID",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RoomRate",
            fields=[
                (
                    "room_rate_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("hours_of_stay", models.IntegerField()),
                ("room_rate", models.DecimalField(decimal_places=2, max_digits=100)),
                ("is_latest", models.BooleanField(default=True)),
                (
                    "room_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="rooms.roomtype"
                    ),
                ),
                (
                    "tiers",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rooms.tiers",
                        verbose_name="Tiers",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Room",
            fields=[
                (
                    "room_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("room_code", models.CharField(max_length=50, unique=True)),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("vacant, cleaned", "vacant, cleaned"),
                            ("vacant, dirty", "vacant, dirty"),
                            ("occupied, cleaned", "occupied, cleaned"),
                            ("occupied, dirty", "occupied, dirty"),
                            ("maintenance", "maintenance"),
                        ],
                        default="Available",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "is_archived",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "remarks",
                    models.CharField(blank=True, default="", max_length=100, null=True),
                ),
                (
                    "quiet_zone",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "room_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rooms",
                        to="rooms.roomtype",
                        verbose_name="Room Type",
                    ),
                ),
            ],
            managers=[
                ("available_objects", django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name="PlatformTier",
            fields=[
                (
                    "platformtier_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "platform",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="bookingplatform.platform",
                        verbose_name="Booking Platform",
                    ),
                ),
                (
                    "tiers",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rooms.tiers",
                        verbose_name="Tiers",
                    ),
                ),
            ],
        ),
    ]
