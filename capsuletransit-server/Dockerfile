# FROM node:16-alpine as builder
# WORKDIR /app
# COPY capsuletransit-client/package.json ./
# RUN npm install
# COPY capsuletransit-client/ .
# RUN npm run build

# Load python 3.8 dependencies using slim debian 10 image.
FROM python:3.11-slim

# Install Supervisor, Nginx, and other dependencies
RUN apt-get update && apt-get install -y gcc libpq-dev nginx cron supervisor


ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /code
COPY requirements.txt /code/
RUN python -m pip install --upgrade pip
RUN pip install -r requirements.txt
COPY . /code/
# COPY --from=builder /app/build/index.html /code/templates/index.html
# COPY --from=builder /app/build/static /code/static
EXPOSE 8000
CMD ["gunicorn", "--workers", "3", "--bind", "0.0.0.0:8000", "capsuletransit.wsgi:application"]