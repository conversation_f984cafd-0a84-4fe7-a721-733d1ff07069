from azure.storage.blob import BlobServiceClient

class AzureBlobStorage:
    # constructor
    def __init__(self, connection_string, container_name):
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        self.container_name = container_name

    def upload_image(self, image_name, image_data):
        blob_client = self.blob_service_client.get_blob_client(container=self.container_name, blob=image_name)
        blob_client.upload_blob(image_data)

    def get_image_url(self, image_name):
        return f"https://{self.blob_service_client.account_name}.blob.core.windows.net/{self.container_name}/{image_name}"