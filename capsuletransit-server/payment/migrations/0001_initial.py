# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "payment_method_id",
                    models.AutoField(
                        primary_key=True,
                        serialize=False,
                        verbose_name="PaymentMethodID",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Cash", "Cash"),
                            ("Card", "Card"),
                            ("Paylater", "Paylater"),
                            ("iPay88 - Hotel Website", "iPay88 - Hotel Website"),
                            ("Security Deposit (Refund)", "Security Deposit (Refund)"),
                            (
                                "Security Deposit (Deposit)",
                                "Security Deposit (Deposit)",
                            ),
                            ("Bank Transfer", "Bank Transfer"),
                            ("E-Wallet", "E-Wallet"),
                            ("Voucher", "Voucher"),
                            ("Complimentary Room", "Complimentary Room"),
                        ],
                        max_length=50,
                        verbose_name="Payment Method",
                    ),
                ),
                (
                    "payment_method_desc",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Payment Method Description",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PaymentType",
            fields=[
                (
                    "payment_type_id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="PaymentTypeID"
                    ),
                ),
                (
                    "payment_type",
                    models.CharField(
                        choices=[
                            ("GrabPay", "GrabPay"),
                            ("Boost", "Boost"),
                            ("Alipay", "Alipay"),
                            ("TnGwallet", "TnGwallet"),
                            ("Visa Card", "Visa Card"),
                            ("Master Card", "Master Card"),
                            ("Debit Card", "Debit Card"),
                            ("MaybankQR", "MaybankQR"),
                            ("Diner", "Diner"),
                            ("Amex", "Amex"),
                            ("JCB", "JCB"),
                            ("Paylater", "Paylater"),
                            ("Other", "Other"),
                        ],
                        max_length=50,
                        verbose_name="Payment Type",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payment.paymentmethod",
                        verbose_name="Payment Method",
                    ),
                ),
            ],
        ),
    ]
