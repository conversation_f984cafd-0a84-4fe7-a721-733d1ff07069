from rest_framework.routers import DefaultRouter
from django.urls import path, include

# Route
from .views import PaymentMethodViewSet, PaymentTypeViewSet

router = DefaultRouter()
router.register("method", PaymentMethodViewSet, basename="payment-method")
router.register("type", PaymentTypeViewSet, basename="payment-type")

urlpatterns = router.urls
urlpatterns = [
    path("", include(router.urls)),
]