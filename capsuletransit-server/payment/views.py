from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from rest_framework.response import Response
from rest_framework import viewsets, status, generics
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON>J<PERSON>NRender<PERSON>

from payment.models import *
from payment.serializer import PaymentSerializer, PaymentMethodWriteSerializer, PaymentTypeSerializer

class PaymentMethodViewSet(viewsets.ModelViewSet):
    queryset = PaymentMethod.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = PaymentSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser, )

    def list(self, request):
        serializer = PaymentSerializer(self.queryset, many=True)
        return Response(serializer.data)

    def create(self, request):
        serializer = PaymentMethodWriteSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "status": "success",
                "message": "new payment method created"
            }, status=status.HTTP_201_CREATED)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    



class PaymentTypeViewSet(viewsets.ModelViewSet):
    queryset = PaymentType.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = PaymentTypeSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser, )

    def create(self, request):
        serializer = PaymentTypeSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "status": "success",
                "message": "new payment type created"
            }, status=status.HTTP_201_CREATED)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
