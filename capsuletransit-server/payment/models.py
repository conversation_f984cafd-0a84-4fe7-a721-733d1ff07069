# Create your models here.
from django.db import models


# Create your models here.
class PaymentMethod(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ("Cash", "Cash"),
        ("Card", "Card"),
        ("Paylater", "Paylater"),
        ("iPay88 - Hotel Website", "iPay88 - Hotel Website"),
        ("Security Deposit (Refund)", "Security Deposit (Refund)"),
        ("Security Deposit (Deposit)", "Security Deposit (Deposit)"),
        ("Bank Transfer", "Bank Transfer"),
        ("E-Wallet", "E-Wallet"),
        ("Voucher", "Voucher"),
        ("Complimentary Room", "Complimentary Room"),
    ]

    payment_method_id = models.AutoField(
        primary_key=True, verbose_name="PaymentMethodID"
    )
    payment_method = models.CharField(
        max_length=50, choices=PAYMENT_METHOD_CHOICES, verbose_name="Payment Method"
    )
    payment_method_desc = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Payment Method Description"
    )

    def __str__(self):
        return self.payment_method

    def get_related_payment_type(self):
        return PaymentType.objects.filter(payment_method_id=self.pk)


class PaymentType(models.Model):
    payment_type_id = models.AutoField(primary_key=True, verbose_name="PaymentTypeID")
    payment_method = models.ForeignKey(
        PaymentMethod, on_delete=models.CASCADE, verbose_name="Payment Method"
    )
    payment_type = models.CharField(max_length=50, verbose_name="Payment Type")

    def __str__(self):
        return self.payment_type
