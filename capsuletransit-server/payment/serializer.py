from rest_framework import serializers
from .models import *



class PaymentTypeSerializer(serializers.ModelSerializer):

    class Meta:
        model = PaymentType
        fields = "__all__"


class PaymentTypeForMethodQuery(PaymentTypeSerializer):

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.pop('payment_method', None)   
        return data


class PaymentSerializer(serializers.ModelSerializer):

    payment_types = PaymentTypeForMethodQuery(source='get_related_payment_type', read_only=True, many=True)
    
    class Meta:
        model = PaymentMethod
        fields = "__all__"



class PaymentMethodWriteSerializer(serializers.ModelSerializer):

    class Meta:
        model = PaymentMethod
        fields = "__all__"

    def validate(self, data):

        payment_method = data.get("payment_method")

        existing = PaymentMethod.objects.filter(payment_method = payment_method).first()
        if existing:
            raise serializers.ValidationError("Payment method already exist")

        return data