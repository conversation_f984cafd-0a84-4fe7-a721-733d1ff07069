# Generated by Django 4.2.3 on 2024-03-20 08:49

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AccessCardCommand",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("command", models.Char<PERSON><PERSON>(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_processed", models.<PERSON><PERSON>anField(default=False)),
                ("finished_at", models.DateTimeField(blank=True, null=True)),
                ("KC", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "room_number",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "front_desk_number",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("WS", models.CharField(blank=True, max_length=100, null=True)),
                ("DA", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ("DT", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ("G", models.CharField(blank=True, max_length=100, null=True)),
                ("GA", models.CharField(blank=True, max_length=100, null=True)),
                ("GD", models.CharField(blank=True, max_length=100, null=True)),
                ("GN", models.CharField(blank=True, max_length=100, null=True)),
                ("TI", models.CharField(blank=True, max_length=100, null=True)),
                ("source_ip", models.GenericIPAddressField(blank=True, null=True)),
                ("dest_kc", models.GenericIPAddressField(blank=True, null=True)),
                ("issuer_log", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="AccessCardConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("source_ip", models.GenericIPAddressField()),
                ("dest_kc", models.GenericIPAddressField()),
            ],
            options={
                "unique_together": {("source_ip", "dest_kc")},
            },
        ),
    ]
