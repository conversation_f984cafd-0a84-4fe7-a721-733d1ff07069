import uuid
from django.shortcuts import render
from rest_framework.generics import Retrieve<PERSON><PERSON><PERSON>iew
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework import generics
from .models import AccessCardCommand
from .serializers import AccessCardCmdSerializer, AccessCardCreateSerializer
from bookings.models import Booking, RoomBooking, ShowerBooking
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from drf_yasg.utils import swagger_auto_schema

from constant.enums import TransactionItemCategory
from accesscard import exceptions


class GetAccessCardCmdView(APIView):
    authentication_classes = []
    permission_classes = []
    def get(self, request):
        cmd = AccessCardCommand.objects.\
            filter(is_processed=False).\
            order_by('-created_at').first()
        
        if cmd:
            cmd.is_processed = True
            cmd.save()
            return Response(AccessCardCmdSerializer(cmd).data, status=200)
        else:
            return Response({}, status=status.HTTP_204_NO_CONTENT)


class GetAccessCardCmdV2View(APIView):
    authentication_classes = []
    permission_classes = []
    def get(self, request):
        lot_id = request.GET.get("lotId", None)

        if not lot_id:
            return Response({}, status=status.HTTP_204_NO_CONTENT)
        
        cmd = AccessCardCommand.objects.filter(
            is_processed=False,
            lot_id = lot_id
        ).order_by('-created_at').first()
        
        if cmd:
            cmd.is_processed = True
            cmd.save()
            return Response(AccessCardCmdSerializer(cmd).data, status=200)
        else:
            return Response({}, status=status.HTTP_204_NO_CONTENT)
        
        
class UpdateAccessCardCmdView(generics.UpdateAPIView):
    authentication_classes = []
    permission_classes = []
    queryset = AccessCardCommand.objects.all()
    serializer_class = AccessCardCmdSerializer
    lookup_field = 'id'
    lookup_url_kwarg = 'cmd_id'

class CreateAccessCardCmdView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(request_body=AccessCardCreateSerializer)
    def post(self, request):
        cmd_serializer = AccessCardCreateSerializer(data=request.data)

        try:
            cmd_serializer.is_valid(raise_exception=True)

            item_to_scan_id = cmd_serializer.validated_data['item_to_scan_id']
            item = cmd_serializer.validated_data['item']
            cashier_terminal_name = cmd_serializer.validated_data['cashier_terminal_name']

            if item == TransactionItemCategory.ROOM_SALES:
                try:
                    room_booking = RoomBooking.objects.get(pk=item_to_scan_id)
                except RoomBooking.DoesNotExist:
                    return Response({
                        'message': 'Room booking not found'
                    }, status=204)
                cmd = AccessCardCommand.create_from_room_booking(
                    room_booking=room_booking,
                    cashier_name=cashier_terminal_name,
                    lot_id = request.user.lot_id
                )

            elif item == TransactionItemCategory.SHOWER_SALES:
                try:
                    booking = Booking.objects.get(pk=item_to_scan_id)
                except Booking.DoesNotExist:
                    return Response({
                        'message': 'Booking not found'
                    }, status=204)
                cmd = AccessCardCommand.create_from_shower_booking(
                    booking=booking,
                    cashier_name=cashier_terminal_name,
                    lot_id = request.user.lot_id
                )
            
            else:
                return Response({
                    "mesasge" : "please use correct item"
                }, status=400)

            return Response(AccessCardCmdSerializer(cmd).data, status=201)
        
        except exceptions.CashierNotFound as e:
            return Response({
                "message" : str(e)
            }, status=204)

        except exceptions.NoKcNumber as e:
            return Response({
                "message" : str(e)
            }, status=403)
        
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

