from .views import GetAccessCardCmdV2View, GetAccessCardCmdView, CreateAccessCardCmdView, UpdateAccessCardCmdView, AccessCardCmdSerializer
from django.urls import path
app_name = 'accesscard'

urlpatterns = [
    path('poll/', GetAccessCardCmdView.as_view(), name='accesscard'),
    path('create/', CreateAccessCardCmdView.as_view(), name='create_accesscard'),
    path('update/<uuid:cmd_id>/', UpdateAccessCardCmdView.as_view(), name='update_accesscard'),
    path('poll/v2', GetAccessCardCmdV2View.as_view(), name='accesscardv2')

]