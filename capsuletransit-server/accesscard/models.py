from django.db import models
from bookings.models import Booking, RoomBooking, ShowerBooking

# Create your models here.
# views.py
import datetime
from django.http import JsonResponse
import socket
import pytz
from django.utils import timezone

from accesscard import exceptions

from bookings.models import BookingStatus, RoomBooking
from bookings.models import ROOM_BOOKING_STATUS
from cashierterminal.models import CashierTerminal

from rooms.models import Lot 

#  Glossary of the protocol
# <STX>
# KR|KTN = Make Card Protocol (Don't Change)
# K# = Number of Key Card
# KC = Front Desk Number (Follow what is set in Lock System settings)
# RN = Room Number, can be combination of alphabet and digit (Eg. LG101)
# WS = Work Station
# DA = Check In Date (yyMMdd)
# DT = Check Out Time (HH:mm)
# G# = Folio Number (Max. 10 Digit Numbers)
# GA = Guest Arrival Date
# GD = Check Out Date (yyMMdd)
# GN = Guest Name (Max. 40 characters)
# TI = Check In Time (HHmmss)
# <ETX>
"""
KR|KTN = Make Card Protocol (Don't Change)
-
K# = Number of Key Card
bookings_booking[adult_pax] + bookings_booking[child_pax]
KC = Front Desk Number (Follow what is set in Lock System settings)
"5D92-7035-AB12-E5E5"
RN = Room Number, can be combination of alphabet and digit (Eg. LG101)
bookings_roombooking[room_id] --> rooms_room[room_code]
WS = Work Station
192.168.0.1  port: 2887
DA = Check In Date (yyMMdd)
convert (bookings_bookingstatus[check_in_datetime] where is_latest = TRUE) to DATE data type
DT = Check Out Time (HH:mm)
convert (bookings_bookingstatus[check_out_datetime] where is_latest = TRUE) to TIME data type
G# = Folio Number (Max. 10 Digit Numbers)
bookings_booking[booking_no]
GA = Guest Arrival Date
convert (bookings_roombooking[actual_checkin_date_time]) to DATE data type
GD = Check Out Date (yyMMdd)
convert (bookings_roombooking[actual_checkin_date_time]) to DATE data type + (booking_duration)
GN = Guest Name (Max. 40 characters)
bookings_booking[person_in_charge_id]  -->  guests_customer[first_name] + guests_customer[last_name]
TI = Check In Time (HHmmss)
convert (bookings_bookingstatus[check_in_datetime] where is_latest = TRUE) to TIME data type
@Housekeeper
the access card comp will update us the latest value for KC
@Housekeeper
change KC params to  2
"""


def make_GN(guest_name : str):
    return "GN" + guest_name

def make_DA(check_in_date : datetime.datetime):
    return "DA" + extract_yyyymmdd(check_in_date)

def make_KC(KC : str):
    return "KC" + KC


def send_message(sock, message):
    sock.sendall(message.encode())

def receive_message(sock):
    return sock.recv(1024).decode()

def extract_yyyymmdd(date : datetime.datetime):
    year = date.year
    month =date.month
    day =  date.day
    
    year = str(year)[2:]
    month = str(month).rjust(2, '0')
    day = str(day).rjust(2, '0')
    return  year + month + day

def extract_hhmmss(date : datetime.datetime):
    hour = date.hour
    minute =date.minute
    second =  date.second
    
    hour = str(hour).rjust(2, '0')
    minute = str(minute).rjust(2, '0')
    second = str(second).rjust(2, '0')
    return  hour + minute + second

def extract_for_hhmm(date : datetime.datetime):
    hour = date.hour
    minute =date.minute
    
    hour = str(hour).rjust(2, '0')
    minute = str(minute).rjust(2, '0')
    return  hour + ":" + minute


def check_out(request):
    try:
        room_number = request.POST.get('room_number')
        front_desk_number = request.POST.get('front_desk_number')

        check_out_message = (
            "<STX>KD|KC{}|RN{}|WSFDesk01|DA091202|G#12345|TI113400|<ETX>v"
            .format(front_desk_number, room_number)
        )

        # Connect to the card software
        card_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        card_socket.connect(('card_software_address', 2887))  # Update with actual address

        # Send Check Out message
        send_message(card_socket, check_out_message)

        # Receive response from card software
        response = receive_message(card_socket)
        
        # Close socket
        card_socket.close()

        # Return success response with acknowledgment
        return JsonResponse({'success': True, 'response': response})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
    
#***************
#2887
class AccessCardConfiguration(models.Model):
    source_ip = models.GenericIPAddressField()
    dest_kc = models.GenericIPAddressField()
    class Meta:
        unique_together = ('source_ip', 'dest_kc')

class AccessCardCommand(models.Model):
    command = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_processed = models.BooleanField(default=False)
    finished_at = models.DateTimeField(null=True, blank=True)
    KC = models.CharField(max_length=100, null=True, blank=True)
    RN = models.CharField(max_length=100, null=True, blank=True)
    front_desk_number = models.CharField(max_length=100, null=True, blank=True)
    K = models.CharField(max_length=100, null=True, blank=True)
    WS = models.CharField(max_length=100, null=True, blank=True)
    DA = models.CharField(max_length=100, null=True, blank=True)
    DT = models.CharField(max_length=100, null=True, blank=True)
    G = models.CharField(max_length=100, null=True, blank=True)
    GA = models.CharField(max_length=100, null=True, blank=True)
    GD = models.CharField(max_length=100, null=True, blank=True)
    GN = models.CharField(max_length=100, null=True, blank=True)
    TI = models.CharField(max_length=100, null=True, blank=True)
    source_ip = models.GenericIPAddressField(null=True, blank=True)
    dest_kc = models.GenericIPAddressField(null=True, blank=True)
    issuer_log = models.TextField(null=True, blank=True)
    lot = models.ForeignKey(Lot, on_delete=models.CASCADE, null = True, blank=True)

    @classmethod
    def create_from_room_booking(cls, room_booking : RoomBooking, lot_id = 1, cashier_name : str = "Admin"):
        booking_num = room_booking.booking.booking_no
        count = room_booking.room.room_type.max_pax

        # Take only digits 
        booking_num = ''.join(filter(str.isdigit, booking_num))

        booking_status : BookingStatus = room_booking.booking.booking_status.get(is_latest=True)

        # Convert timezone
        malaysia_tz = pytz.timezone('Asia/Kuala_Lumpur')
        booking_status.check_in_datetime = booking_status.check_in_datetime.astimezone(malaysia_tz)
        booking_status.max_check_out_datetime = booking_status.max_check_out_datetime.astimezone(malaysia_tz)

        check_in_date = extract_yyyymmdd(booking_status.check_in_datetime)
        checkout_date = extract_yyyymmdd(booking_status.max_check_out_datetime)

        check_in_time = extract_hhmmss(booking_status.check_in_datetime)
        check_out_time = extract_hhmmss(booking_status.max_check_out_datetime)
        hh_mm_checkout = extract_for_hhmm(booking_status.max_check_out_datetime)

        # KC = "5D92-7035-AB12-E5E5"

        # message = f"<STX>KR|K#{room_booking.room.room_type.max_pax}|KC{KC}|KTN|RN{room_booking.room.room_code}|WSFDesk01|DA{check_in_date}|DT{hh_mm_checkout}|G#{booking_num}|GA{check_in_date}|GD{checkout_date}|GN{guest_name}|TI{check_in_time}|<ETX>"

        room_number = room_booking.room.room_code
        room_use_zone = AccessCardZoneRoom.objects.filter(
            roomzone_id = room_booking.room.room_type.roomzone_id
        )
        matching_zone = room_use_zone.filter(room_code__isnull=True).first()
        if matching_zone:
            room_number = matching_zone.room_name

        matching_room = room_use_zone.filter(room_code=room_booking.room.room_code).first()
        if matching_room:
            room_number = matching_room.room_code
        
        cashier_kc = 1
        if cashier_name != "Admin":
            cashier = CashierTerminal.objects.filter(cashier_terminal__iexact = cashier_name).first()

            if not cashier:
                raise exceptions.CashierNotFound(f"Cashier with name {cashier_name} not found")
            
            if not cashier.kc_number or cashier.kc_number == "":
                raise exceptions.NoKcNumber(f"Cashier with name {cashier.cashier_terminal} dont have kc number assigned. Please contact Admin to assign")
        
            cashier_kc = cashier.kc_number
                

        cmd = cls()
        cmd.command = 'KR'
        cmd.RN = room_number
        cmd.front_desk_number = "FD02"
        cmd.WS = "FD02"
        cmd.K = str(count)
        cmd.KC = str(cashier_kc)

        cmd.DA = check_in_date
        cmd.DT = hh_mm_checkout
        cmd.G = booking_num
        cmd.GA = check_in_date
        cmd.GD = checkout_date
        cmd.GN = room_booking.booking.customer_booked
        cmd.TI = check_in_time  
        cmd.lot_id = lot_id


        cmd.save()
        return cmd

    @classmethod
    def create_from_shower_booking(cls, booking : Booking, lot_id = 1, cashier_name : str = "Admin"):
        booking_num = booking.booking_no
        count = ShowerBooking.objects.filter(booking_id = booking.pk).count()

        # Take only digits 
        booking_num = ''.join(filter(str.isdigit, booking_num))

        booking_status : BookingStatus = booking.booking_status.get(is_latest=True)

        # Convert timezone
        malaysia_tz = pytz.timezone('Asia/Kuala_Lumpur')
        booking_status.check_in_datetime = booking_status.check_in_datetime.astimezone(malaysia_tz)
        booking_status.max_check_out_datetime = booking_status.max_check_out_datetime.astimezone(malaysia_tz)

        check_in_date = extract_yyyymmdd(booking_status.check_in_datetime)
        checkout_date = extract_yyyymmdd(booking_status.max_check_out_datetime)

        check_in_time = extract_hhmmss(booking_status.check_in_datetime)
        check_out_time = extract_hhmmss(booking_status.max_check_out_datetime)
        hh_mm_checkout = extract_for_hhmm(booking_status.max_check_out_datetime)


        cashier_kc = 1
        if cashier_name != "Admin":
            cashier = CashierTerminal.objects.filter(cashier_terminal__iexact = cashier_name).first()

            if not cashier:
                raise exceptions.CashierNotFound(f"Cashier with name {cashier_name} not found")
            
            if not cashier.kc_number or cashier.kc_number == "":
                raise exceptions.NoKcNumber(f"Cashier with name {cashier.cashier_terminal} dont have kc number assigned. Please contact Admin to assign")
        
            cashier_kc = cashier.kc_number
        

        cmd = cls()
        cmd.command = 'KR'
        cmd.RN = "Gate A"  # Still static. need to change
        cmd.front_desk_number = "FD02"
        cmd.WS = "FD02"
        cmd.K = str(count)
        cmd.KC = str(cashier_kc)

        cmd.DA = check_in_date
        cmd.DT = hh_mm_checkout
        cmd.G = booking_num
        cmd.GA = check_in_date
        cmd.GD = checkout_date
        cmd.GN = booking.customer_booked
        cmd.TI = check_in_time  
        cmd.lot_id = lot_id

        cmd.save()
        return cmd


class AccessCardZoneRoom(models.Model):
    room_name = models.CharField(max_length=100, null=True)
    room_code = models.CharField(max_length=100, null=True)
    roomzone = models.ForeignKey("rooms.roomzone", on_delete=models.CASCADE)
