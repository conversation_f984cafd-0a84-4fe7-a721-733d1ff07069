from rest_framework import serializers
from .models import AccessCardCommand

class AccessCardCmdSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccessCardCommand
        fields = '__all__'

class AccessCardCreateSerializer(serializers.Serializer):
    item_to_scan_id = serializers.UUIDField()   # RoomBooking id or ShowerBooking id
    item = serializers.CharField() # ROOM_SALES or SHOWER_SALES
    cashier_terminal_name = serializers.CharField()