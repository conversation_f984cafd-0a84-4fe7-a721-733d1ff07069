from django.urls import include, path
from promotions.urls import urlpatterns as promotion_urls
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

from . import views

# views
from .views import Authentication, MyTokenObtainPairView

urlpatterns = [
    path("get-token/", MyTokenObtainPairView.as_view(), name="get-token"),
    path("refresh-token/", TokenRefreshView.as_view(), name="refresh-token"),
    path("login/", Authentication.as_view(), name="login"),
    # other app routes
    path("accounts/", include("accounts.urls")),
    path("rooms/", include("rooms.urls")),
    path("lockers/", include("lockers.urls")),
    path("bookings/", include("bookings.urls")),
    path("guests/", include("guests.urls")),
    path("payment/", include("payment.urls")),
    path("transaction/", include("transaction.urls")),
    path("merch/", include("merch.urls")),
    path("promotions/", include("promotions.urls")),
    path("promotion-usage/", include("promotions.urls2")),
    path("shower/", include("shower.urls")),
    path("shower-rate/", include("shower.urls2")),
    path("lockers-rate/", include("lockers.urls2")),
    path("lot/", include("lot.urls")),
    path("lot-settings/", include("lot.urls2")),
    path("cashierterminal/", include("cashierterminal.urls")),
    path("sales-report/", include("salesreport.urls")),
    path("queryservice/", include("queryservice.urls")),
    path("staah/", include("staah.urls")),
    path('accesscard/', include('accesscard.urls')),
    path('payment-terminal/', include('paymentterminal.urls')),
    path("landing-page/", include("landingpage.urls")),
]
