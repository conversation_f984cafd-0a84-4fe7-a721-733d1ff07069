from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from accounts.models import Roles




class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        token['account_id'] = str(user.account_id)
        token['name'] = user.name
        token['username'] = user.username
        token['email'] = user.email
        token['role'] = user.role.name
        token['permissions'] = [permission.codename for permission in user.get_user_permissions()]
        token['lot'] = user.lot_id

        return token