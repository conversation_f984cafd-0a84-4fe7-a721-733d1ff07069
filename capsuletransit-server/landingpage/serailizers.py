from decimal import Decimal
from rest_framework import serializers

from lot.models import Lot

from bookings.models import RoomBooking, Booking
from bookings.serializer import BookingStatusSerializer
from rooms.serializers import RoomSerializer
from promotions.models import MemberPromolist
from guests.models import CustomerAccount


class RoomForBookingSeralizer(serializers.Serializer):
    room_type_name = serializers.CharField()
    room_zone_names = serializers.CharField()
    max_pax = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    available_count = serializers.IntegerField()


class LotInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lot
        fields = "__all__"


class TaxServiceChargeSerializer(serializers.Serializer):
    service_charge_amount = serializers.CharField()
    tax_amount = serializers.CharField()


class BookingsRoom(serializers.Serializer):
    room_type_name = serializers.Char<PERSON><PERSON>()
    quantity = serializers.IntegerField()
    room_price = serializers.DecimalField(max_digits=10, decimal_places=2)


class LandingPageBooking(serializers.Serializer):
    lot_id = serializers.IntegerField()
    checkin_datetime = serializers.IntegerField()
    duration = serializers.IntegerField()
    room_types = BookingsRoom(many=True)
    promotion = serializers.CharField(allow_blank=True)
    promotion_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    sum = serializers.DecimalField(max_digits=10, decimal_places=2)
    credit_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    roundings = serializers.DecimalField(max_digits=10, decimal_places=2)
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    id_type = serializers.CharField()
    country_code = serializers.CharField()
    id_no = serializers.CharField()
    email = serializers.CharField(allow_blank=True)
    phone_number = serializers.CharField(allow_blank=True)
    gender = serializers.CharField()

    def validate_id_no(self, value):
        if value.strip() == "-":
            raise serializers.ValidationError("The ID number cannot be '-'.")
        return value


class ConfirmBookingSerializer(serializers.Serializer):
    RefNo = serializers.CharField()
    Amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    TransId = serializers.CharField(allow_blank=True)
    AuthCode = serializers.CharField(allow_blank=True)
    Status = serializers.CharField()
    CCName = serializers.CharField(allow_blank=True)
    CCNo = serializers.CharField(allow_blank=True)
    TranDate = serializers.CharField(allow_blank=True)

    def to_internal_value(self, data):
        if "Amount" in data:
            data = data.copy()
            data["Amount"] = data["Amount"].replace(",", "")
        return super().to_internal_value(data)


class GenerateReceiptSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()


class RegisterUserAccountSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    id_type = serializers.CharField()
    country_code = serializers.CharField()
    id_no = serializers.CharField()
    email = serializers.CharField(allow_blank=True)
    phone_number = serializers.CharField(allow_blank=True)
    gender = serializers.CharField()
    username = serializers.CharField()
    password = serializers.CharField()


class CustomerMembershipLoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)


class RoomBookingSerializer(serializers.ModelSerializer):
    room = RoomSerializer(read_only=True)

    class Meta:
        model = RoomBooking
        fields = "__all__"


class BookingLandingPageSerializer(serializers.ModelSerializer):
    room_bookings = RoomBookingSerializer(many=True, read_only=True)
    booking_status = BookingStatusSerializer(many=True, read_only=True)

    class Meta:
        model = Booking
        fields = "__all__"


class ChangeCustomerPasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True, write_only=True)
    new_password = serializers.CharField(required=True, write_only=True)


class ChangeCustomerNameSerializer(serializers.Serializer):
    new_firstname = serializers.CharField(required=False)
    new_lastname = serializers.CharField(required=False, allow_blank=True)


class ChangeCustomerIdnoSerializer(serializers.Serializer):
    new_idno = serializers.CharField(required=True, write_only=True)


class ClaimPromoSerializer(serializers.ModelSerializer):
    individual_promo_id = serializers.UUIDField()

    class Meta:
        model = MemberPromolist
        fields = ["individual_promo_id"]


class CustomerInfoSerializer(serializers.ModelSerializer):
    id_no = serializers.CharField(source="customer.id_no")
    member = serializers.CharField(source="customer.member")
    id_type = serializers.CharField(source="customer.id_type")
    gender = serializers.CharField(source="customer.gender")
    country = serializers.CharField(source="customer.country")
    firstname = serializers.CharField(source="customer.firstname")
    lastname = serializers.CharField(source="customer.lastname")
    phone_number = serializers.CharField(source="customer.phone_number")
    email = serializers.EmailField(source="customer.email")
    # point = serializers.IntegerField(source='customer.point')
    membership_no = serializers.CharField(source="customer.membership_no")

    class Meta:
        model = CustomerAccount
        fields = [
            "account_id",
            "username",
            "id_no",
            "member",
            "id_type",
            "gender",
            "country",
            "firstname",
            "lastname",
            "phone_number",
            "email",
            # 'point',
            "membership_no",
            "created_at",
            "updated_at",
        ]
