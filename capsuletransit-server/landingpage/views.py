from collections import Counter
import os
import jwt
import pytz
from datetime import datetime, timedelta
from django.http import HttpResponse, JsonResponse
from rest_framework.decorators import action
from rest_framework import status, viewsets
from rest_framework.response import Response
from django.conf import settings
from django.db.models import Q, Count, F, Prefetch, Sum
from django.db import transaction
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
from datetime import datetime
from email.mime.image import MIMEImage
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.throttling import UserRateThrottle
import dotenv

from djangorestframework_camel_case.parser import Camel<PERSON>aseJ<PERSON>NParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer

from django.middleware.csrf import get_token

from . import exceptions
from .middleware import TokenAuthentication
from .utils import (
    generate_password,
    validate_password,
    get_email_password,
)
from .dependency.password import BcryptPasswordHashing
from .serailizers import (
    ConfirmBookingSerializer,
    LandingPageBooking,
    LotInfoSerializer,
    RegisterUserAccountSerializer,
    RoomForBookingSeralizer,
    TaxServiceChargeSerializer,
    CustomerMembershipLoginSerializer,
    BookingLandingPageSerializer,
    ChangeCustomerPasswordSerializer,
    ChangeCustomerNameSerializer,
    ChangeCustomerIdnoSerializer,
    ClaimPromoSerializer,
    CustomerInfoSerializer,
)

from guests.serializer import CustomerAccountSerializer

from constant.enums import TransactionItemCategory

from rooms.models import Room, RoomType, RoomRate, PlatformTier
from bookings.models import Booking, BookingStatus, RoomBooking, Platform
from bookings.services.booking import exist_room_booking, register_booking
from lot.models import Lot, Settings
from promotions.models import Promotion, MemberPromolist, IndividualPromo
from promotions.serializers import IndividualPromoSerializer
from rooms.services.room import generate_available
from guests.models import Country, Customer, CustomerAccount, Member
from guests.services.customer import create_new_customer, customer_milestone_progress
from transaction.services.transaction import (
    create_new_pending_transaction,
    generate_invoice,
    generate_receipt,
)
from transaction.models import Transaction
from transaction.utils import (
    calculate_booking_duration,
)

from accounts.models import Shift, Account
from payment.models import PaymentMethod, PaymentType
from landingpage.models import PaymentGateawayTransaction
from accounts.utils import get_current_shift, auto_start_shift

from django.views.decorators.csrf import csrf_protect
from django.core.mail import EmailMessage, get_connection
import base64

dotenv.load_dotenv()


class LandingPageViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = []
    permission_classes = []
    throttle_classes = []

    @action(
        detail=False,
        methods=["GET"],
        url_path="list-for-booking",
        url_name="list-for-booking",
    )
    def room_list_for_booking(self, request):

        check_in_datetime = request.GET.get("checkInDatetime", None)
        duration = request.GET.get("duration", None)
        lot_id = request.GET.get("lotId", None)

        try:

            if not check_in_datetime or not duration or not lot_id:
                raise exceptions.UnableGetListForBooking("must add all query params")

            qs = Room.objects.all()
            qs = qs.filter(room_type__roomzone__lot_id=lot_id)

            # get start and end datetime
            start_date = datetime.fromtimestamp(int(check_in_datetime))
            end_date = datetime.fromtimestamp(
                int(check_in_datetime)
            ) + timezone.timedelta(hours=int(duration))
            start_date = timezone.make_aware(
                start_date, timezone.get_current_timezone()
            )
            end_date = timezone.make_aware(end_date, timezone.get_current_timezone())
            start_date_time = start_date
            end_date_time = end_date

            # get excluded (occupied) room ids
            room_booking_exist = exist_room_booking(
                start_date=start_date_time, end_date=end_date_time
            )
            excluded_room_ids = [room_book.room_id for room_book in room_booking_exist]

            # exclude occupied room
            filtered_room = qs.exclude(pk__in=excluded_room_ids)

            # get available room count
            room_data_count_filtered = filtered_room.values(
                type_name=F("room_type__type_name")
            ).annotate(room_count=Count("room_type__type_name"))
            filtered_count_dict = {
                item["type_name"]: item["room_count"]
                for item in room_data_count_filtered
            }

            room_type_list = RoomType.objects.prefetch_related("roomzone").filter(
                roomzone__lot_id=lot_id
            )

            # get rate
            platforms_tier = PlatformTier.objects.filter(
                platform__platform="Hotel Website", tiers__lot_id=lot_id
            ).first()
            room_rates = RoomRate.objects.filter(
                hours_of_stay=duration,
                is_latest=True,
                tiers_id=platforms_tier.tiers.pk,
                room_type__roomzone__lot_id=lot_id,
            )

            room_for_booking_list = []
            room_type_aggregated = {}
            for item in room_type_list:
                if item.type_name not in room_type_aggregated:

                    # rate calculation
                    price = 0
                    for room_rate in room_rates:
                        if room_rate.room_type.type_name != item.type_name:
                            continue
                        price = room_rate.room_rate

                    # aggregate data
                    room_type_aggregated[item.type_name] = {
                        "room_type_name": item.type_name,
                        "room_zone_names": [item.roomzone.zone_name],
                        "max_pax": item.max_pax,
                        "price": price,
                        "available_count": filtered_count_dict.get(item.type_name, 0),
                    }
                else:
                    room_type_aggregated[item.type_name]["room_zone_names"].append(
                        item.roomzone.zone_name
                    )
                    room_type_aggregated[item.type_name][
                        "available_count"
                    ] += filtered_count_dict.get(item.type_name, 0)

            for room_type_data in room_type_aggregated.values():
                room_for_booking_list.append(
                    {
                        "room_type_name": room_type_data["room_type_name"],
                        "room_zone_names": ", ".join(room_type_data["room_zone_names"]),
                        "max_pax": room_type_data["max_pax"],
                        "price": room_type_data["price"],
                        "available_count": room_type_data["available_count"],
                    }
                )

            return_serializer = RoomForBookingSeralizer(
                room_for_booking_list, many=True
            )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except exceptions.UnableGetListForBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="lot-info/(?P<lot_id>[a-zA-Z0-9-]+)",
        url_name="lot-info",
    )
    def lot_address(self, request, lot_id):
        try:

            try:
                lot = Lot.objects.get(pk=lot_id)
            except:
                return Response(
                    {"status": "failed", "message": "lot not found"},
                    status=status.HTTP_204_NO_CONTENT,
                )

            return_serializer = LotInfoSerializer(lot)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="tax-and-service-charge",
        url_name="tax-and-service-charge",
    )
    def get_tax_and_service_charge(self, request):

        lot = request.GET.get("lot", 1)

        try:
            settings = Settings.objects.filter(
                Q(lot_id=lot)
                & Q(Q(settings_name="SST") | Q(settings_name="Service Charge"))
            )

            service_charge = "0%"
            tax = "0%"

            for setting in settings:
                if setting.settings_name == "Service Charge":
                    service_charge = setting.settings_description

                if setting.settings_name == "SST":
                    tax = setting.settings_description

            return_serializer = TaxServiceChargeSerializer(
                {"service_charge_amount": service_charge, "tax_amount": tax}
            )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="promotions",
        url_name="promotions",
        throttle_classes=[UserRateThrottle],
    )
    def get_promotions(self, request):
        promo_code = request.GET.get("promoCode", None)

        try:

            if not promo_code:
                return Response(
                    {"status": "failed", "message": "please provide the promo code"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            promo = Promotion.objects.filter(
                promo_code=promo_code,
                visible_at_website=True,
                promo_end_datetime__gte=timezone.now(),
            ).first()

            if not promo:
                return Response(
                    {"status": "failed", "message": "promo not found"},
                    status=status.HTTP_204_NO_CONTENT,
                )

            if promo.seats <= 0:
                return Response(
                    {"status": "failed", "message": "promo seats not available"},
                    status=status.HTTP_204_NO_CONTENT,
                )

            return Response(
                {"status": "success", "data": {"promo_value": promo.details}},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @method_decorator(csrf_protect)
    @action(
        detail=False,
        methods=["POST"],
        url_path="booking",
        url_name="booking",
        throttle_classes=[UserRateThrottle],
    )
    def landing_page_booking(self, request):
        serializer = LandingPageBooking(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            # get start and end datetime
            start_date = datetime.fromtimestamp(int(validated_data["checkin_datetime"]))
            end_date = datetime.fromtimestamp(
                int(validated_data["checkin_datetime"])
            ) + timezone.timedelta(hours=int(validated_data["duration"]))
            # start_date = timezone.make_aware(
            #     start_date, timezone.get_current_timezone()
            # )
            # end_date = timezone.make_aware(end_date, timezone.get_current_timezone())

            utc_timezone = pytz.UTC

            start_date_time_utc = start_date.astimezone(utc_timezone)
            end_date_time_utc = end_date.astimezone(utc_timezone)

            with transaction.atomic():
                # get customer
                country = Country.objects.filter(
                    country_code=validated_data["country_code"]
                ).first()
                customer = Customer.objects.filter(
                    id_no=validated_data["id_no"], country_id=country.pk
                ).first()
                if not customer:
                    customer = create_new_customer(
                        first_name=validated_data["first_name"],
                        last_name=validated_data["last_name"],
                        id_no=validated_data["id_no"],
                        phone_no=validated_data["phone_number"],
                        email=validated_data["email"],
                        country_code=validated_data["country_code"],
                        gender=validated_data["gender"],
                        id_type=validated_data["id_type"],
                    )

                # duped booking validation
                last_one_min = datetime.now() - timedelta(minutes=1)
                last_one_min_booking = Booking.objects.filter(
                    customer_staying_id=customer.pk,
                    booking_made_datetime__gte=last_one_min,
                )
                if last_one_min_booking.exists():
                    return Response(
                        {
                            "status": "failed",
                            "message": "multiple book attempt detected (please try again in 1 minute)",
                        },
                        status=status.HTTP_409_CONFLICT,
                    )

                room_for_book = []
                transaction_detail = []
                for type in validated_data["room_types"]:
                    room_type_name = type["room_type_name"]
                    count = type["quantity"]

                    available_rooms = generate_available(
                        start_date=start_date_time_utc,
                        end_date=end_date_time_utc,
                        room_type_name=room_type_name,
                        quantity=count,
                        lot_id=validated_data["lot_id"],
                    )

                    for room in available_rooms:
                        room_for_book.append(
                            {
                                "picId": customer.pk,
                                "roomId": room.pk,
                                "details": "",
                                "maxPax": room.room_type.max_pax,
                            }
                        )

                        transaction_detail.append(
                            {
                                "item_id": str(room.pk),
                                "item_name": str(room.room_code),
                                "item_type": str(room.room_type.type_name),
                                "quantity": str(1),
                                "price": str(type["room_price"]),
                                "duration": validated_data["duration"],
                                "category": TransactionItemCategory.ROOM_SALES,
                            }
                        )

                if len(room_for_book) == 0:
                    return Response(
                        {"status": "failed", "message": "room not available"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                platform = Platform.objects.filter(platform="Hotel Website").first()

                # Format the datetime objects
                formatted_check_in_datetime = start_date_time_utc.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                )
                formatted_check_out_datetime = end_date_time_utc.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                )

                max_pax = sum(room_booking["maxPax"] for room_booking in room_for_book)

                booking = register_booking(
                    {
                        "adult": max_pax,
                        "child": 0,
                        "checkInDatetime": formatted_check_in_datetime,
                        "checkOutDatetime": formatted_check_out_datetime,
                        "details": "",
                        "otaCode": "",
                        "platformId": platform.pk,
                        "rooms": room_for_book,
                    },
                    validated_data["lot_id"],
                )
                
                print('booking', booking)

                account = Account.objects.filter(
                    role__name="Super Admin",
                    lot_id=validated_data["lot_id"],
                    username__icontains="superadmin",
                ).first()

                account_current_shift = get_current_shift(account_id=account.pk)
                if not account_current_shift:
                    account_current_shift = auto_start_shift(
                        account_id=account.pk, lot_id=validated_data["lot_id"]
                    )

                new_transaction = create_new_pending_transaction(
                    booking_id=booking.pk,
                    transaction_details=transaction_detail,
                    account_id=account.pk,
                    sum=validated_data["sum"],
                    is_room_booking=True,
                    promotion=validated_data["promotion"],
                    promotion_amount=validated_data["promotion_amount"] * -1 ,
                    shift=account_current_shift,
                    roundings=validated_data["roundings"],
                    for_landing_page=True
                )
                
                print('new_transaction', new_transaction)

            return Response(
                {
                    "status": "success",
                    "message": "booking created",
                    "data": {
                        "bookingId": booking.pk,
                        "bookingNo": booking.booking_no,
                        "transactionId": new_transaction.pk,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="confirm-booking",
        url_name="confirm-booking",
        parser_classes=[MultiPartParser, FormParser],
        throttle_classes=[UserRateThrottle],
    )
    def confirm_booking(self, request):

        serializer = ConfirmBookingSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            booking = Booking.objects.filter(booking_id=validated_data["RefNo"]).first()

            if not booking:
                return Response(
                    {"status": "failed", "message": "booking not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            saved_transaction = Transaction.objects.filter(booking=booking).first()

            if saved_transaction.transaction_status == "Paid":
                return HttpResponse("RECEIVEOK")

            with transaction.atomic():
                exist_ref = (
                    PaymentGateawayTransaction.objects.select_for_update()
                    .filter(payment_gateaway_transaction_ref=validated_data["TransId"])
                    .exists()
                )

                if not exist_ref:
                    PaymentGateawayTransaction.objects.create(
                        transaction_id=saved_transaction.transaction_id,
                        payment_gateaway_transaction_ref=validated_data["TransId"],
                    )

            with transaction.atomic():

                iPay88 = "iPay88"

                iPay88PaymentMethod = PaymentMethod.objects.filter(
                    payment_method="iPay88"
                )

                if len(iPay88PaymentMethod) < 1:
                    iPay88Method = PaymentMethod.objects.create(
                        payment_method=iPay88, payment_method_desc="pay with iPay88"
                    )

                    iPay88PaymentType = PaymentType.objects.create(
                        payment_method=iPay88Method, payment_type=iPay88
                    )

                if validated_data["Status"] != "1":
                    return Response(
                        {"status": "failed", "message": "Payment was Unsuccessful"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                iPay88PaymentType = PaymentType.objects.filter(
                    payment_type=iPay88
                ).first()

                saved_transaction.transaction_status = "Paid"
                saved_transaction.payment_type = iPay88PaymentType
                saved_transaction.debit_amount = saved_transaction.credit_amount
                saved_transaction.credit_amount = 0

                if validated_data["TranDate"] is not None:
                    tran_date = datetime.strptime(
                        validated_data["TranDate"], "%Y-%m-%d"
                    )
                    saved_transaction.transaction_datetime = tran_date

                payment_reference = ""

                # if validated_data["TransId"]:
                #     payment_reference += (
                #         "iPay88 Transaction ID: " + validated_data["TransId"]
                #     )
                # if validated_data["AuthCode"]:
                #     payment_reference += (
                #         " Bank Approval Code: " + validated_data["AuthCode"]
                #     )
                # if validated_data["CCName"]:
                #     payment_reference += (
                #         " Credit Card Holder: " + validated_data["CCName"]
                #     )
                # if validated_data["CCNo"]:
                #     payment_reference += (
                #         " Credit Card No: " + validated_data["CCNo"]
                #     )

                saved_transaction.payment_reference = payment_reference

                saved_transaction.save()

                latest_booking_status = BookingStatus.objects.filter(
                    is_latest=True, booking=booking
                ).first()

                if latest_booking_status.booking_status == "Cancelled":
                    booking.is_cancelled = False
                    booking.confirm_booking()
                    booking.save()

            try:
                # send email

                customer = booking.customer_staying

                generated_pdf = generate_invoice(
                    request=request, transaction_id=saved_transaction.pk
                )
                pdf_file = base64.b64decode(generated_pdf)

                room_bookings = RoomBooking.objects.filter(booking=booking)

                max_pax = sum(
                    room_booking.room.room_type.max_pax
                    for room_booking in room_bookings
                )

                check_in_datetime = latest_booking_status.check_in_datetime + timedelta(
                    hours=8
                )
                check_out_datetime = (
                    latest_booking_status.check_out_datetime + timedelta(hours=8)
                )

                room_details = [
                    room_booking.room.room_type.type_name
                    for room_booking in room_bookings
                ]

                # Count occurrences of each room type
                counter = Counter(room_details)

                # Format the output
                room_details_string = ", ".join(
                    [f"{count}x {room}" for room, count in counter.items()]
                )

                context = {
                    "customer_billed": booking.customer_booked,
                    "customer_email": customer.email,
                    "customer_IC": customer.id_no,
                    "customer_country": customer.country.country_name,
                    "booking_no": booking.booking_no,
                    "check_in_datetime": check_in_datetime.strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "check_out_datetime": check_out_datetime.strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "max_pax": max_pax,
                    "transaction_datetime": saved_transaction.transaction_datetime.strftime(
                        "%Y-%m-%d"
                    ),
                    "duration": calculate_booking_duration(
                        latest_booking_status.check_in_datetime,
                        latest_booking_status.check_out_datetime,
                    ),
                    "debit_amount": saved_transaction.debit_amount,
                    "billing_details": room_details_string,
                }

                html_message = render_to_string("email_body.html", context=context)
                body = strip_tags(html_message)

                generated_pdf = generate_invoice(
                    request=request, transaction_id=saved_transaction.pk
                )
                pdf_file = base64.b64decode(generated_pdf)

                email_creds = get_email_password(lot=booking.lot.lot_id)
                email = EmailMessage(
                    subject="Confirmation of Booking",
                    body=html_message,
                    to=[f"{customer.email}"],
                    connection=get_connection(
                        username=email_creds["email"],
                        password=email_creds["password"],
                    ),
                    headers={"Content-Type": "text/html"},
                )
                email.content_subtype = "html"
                email.attach(saved_transaction.invoice_no, pdf_file, "application/pdf")

                ct_logo_file_path = os.path.join(
                    settings.BASE_DIR, "static", "images", "CTLogo.png"
                )

                with open(ct_logo_file_path, "rb") as image_file:
                    image = MIMEImage(image_file.read())
                    image.add_header("Content-ID", "<CTLogo>")
                    email.attach(image)

                email.send()

            except:
                pass

            return HttpResponse("RECEIVEOK")

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="check-booking-status",
        url_name="check-booking-status",
    )
    def check_booking_status(self, request):
        booking_id = request.GET.get("bookingId", None)

        try:

            if not booking_id:
                return Response(
                    {"status": "failed", "message": "please provide the booking Id"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            booking = Booking.objects.filter(booking_id=booking_id).first()

            if not booking:
                return Response(
                    {"status": "failed", "message": "booking not found"},
                    status=status.HTTP_204_NO_CONTENT,
                )

            latest_booking_status = BookingStatus.objects.filter(
                is_latest=True, booking_id=booking_id
            ).first()

            if latest_booking_status.booking_status == "Confirm Booking":
                return Response(
                    {"status": "success"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "failed",
                        "message": "Booking has not been confirmed yet.",
                    },
                    status=status.HTTP_402_PAYMENT_REQUIRED,
                )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_name="generate-receipt",
        url_path="generate-receipt",
    )
    def generate_receipt(self, request):
        generated_pdf = generate_receipt(request, is_landing_page=True)

        return JsonResponse(
            {
                "pdf_base64": generated_pdf.decode("utf-8"),
                "message": "Invoice PDF generated.",
            }
        )

    @action(
        detail=False,
        methods=["POST"],
        url_name="send_email",
        url_path="send_email",
    )
    def send_email_test(self, request):
        booking = (
            Booking.objects.filter(booking_id="17007db3-a18b-4a99-8136-ae9f84739702")
            .order_by("-booking_made_datetime")
            .first()
        )
        latest_booking_status = BookingStatus.objects.filter(
            booking=booking, is_latest=True
        ).first()

        room_bookings = RoomBooking.objects.filter(booking=booking)
        transaction = Transaction.objects.filter(booking=booking).first()

        max_pax = sum(
            room_booking.room.room_type.max_pax for room_booking in room_bookings
        )

        room_details = [
            room_booking.room.room_type.type_name for room_booking in room_bookings
        ]

        # Count occurrences of each room type
        counter = Counter(room_details)

        # Format the output
        room_details_string = ", ".join(
            [f"{count}x {room}" for room, count in counter.items()]
        )

        check_in_datetime = latest_booking_status.check_in_datetime + timedelta(hours=8)
        check_out_datetime = latest_booking_status.check_out_datetime + timedelta(
            hours=8
        )

        context = {
            "customer_billed": booking.customer_booked,
            "customer_email": booking.customer_staying.email,
            "customer_IC": booking.customer_staying.id_no,
            "customer_country": booking.customer_staying.country.country_name,
            "booking_no": booking.booking_no,
            "check_in_datetime": check_in_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "check_out_datetime": check_out_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "max_pax": max_pax,
            "transaction_datetime": transaction.transaction_datetime.strftime(
                "%Y-%m-%d"
            ),
            "duration": calculate_booking_duration(
                latest_booking_status.check_in_datetime,
                latest_booking_status.check_out_datetime,
            ),
            "debit_amount": transaction.debit_amount,
            "credit_amount": transaction.credit_amount,
            "billing_details": room_details_string,
        }

        html_message = render_to_string("email_body.html", context=context)
        body = strip_tags(html_message)

        generated_pdf = generate_invoice(request=request, transaction_id=transaction.pk)
        pdf_file = base64.b64decode(generated_pdf)

        email_creds = get_email_password(lot=booking.lot.lot_id)
        email = EmailMessage(
            subject="Sample Email",
            body=html_message,
            to=["<EMAIL>"],
            connection=get_connection(
                username=email_creds["email"],
                password=email_creds["password"],
            ),
            headers={"Content-Type": "text/html"},
        )
        email.content_subtype = "html"
        email.attach(transaction.invoice_no, pdf_file, "application/pdf")

        ct_logo_file_path = os.path.join(
            settings.BASE_DIR, "static", "images", "CTLogo.png"
        )

        with open(ct_logo_file_path, "rb") as image_file:
            image = MIMEImage(image_file.read())
            image.add_header("Content-ID", "<CTLogo>")
            email.attach(image)
        email.send()

        return JsonResponse(
            {
                "message": "OK",
            }
        )

    @action(
        detail=False,
        methods=["GET"],
        url_path="milestone-promo",
        url_name="milestone-promo",
    )
    def get_active_milestone_promo(self, request):

        try:
            milestone_promo = IndividualPromo.objects.filter(
                is_milestone=True, archived=False
            ).first()

            if not milestone_promo:
                return Response(
                    {"status": "failed", "message": "no milestone promo found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return_serializer = IndividualPromoSerializer(milestone_promo)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CustomerAccountViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = []
    permission_classes = []
    throttle_classes = []

    @action(
        detail=False,
        methods=["POST"],
        url_path="create",
        url_name="create",
        throttle_classes=[UserRateThrottle],
    )
    def create_customer_account(self, request):
        serializer = RegisterUserAccountSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                exist_customer = Customer.objects.filter(
                    id_no=validated_data["id_no"],
                    country__country_code=validated_data["country_code"],
                ).first()

                membership = Member.objects.filter(
                    member_condition="Member", is_archive=False
                ).first()

                # if Customer with specific id_no already exist
                if exist_customer:
                    exist_customer_account = CustomerAccount.objects.filter(
                        customer_id=exist_customer.pk
                    ).first()

                    if exist_customer_account:
                        return Response(
                            {
                                "status": "failed",
                                "message": "Account with given IC is already exist",
                            },
                            status=status.HTTP_409_CONFLICT,
                        )

                    country = Country.objects.filter(
                        country_code=validated_data["country_code"]
                    ).first()

                    phone_no = validated_data["phone_number"]

                    exist_customer.firstname = validated_data["first_name"]
                    exist_customer.lastname = validated_data["last_name"]
                    exist_customer.phone_number = (
                        f"+{country.prefix} {phone_no}" if phone_no != "" else None,
                    )
                    exist_customer.email = validated_data["email"]
                    exist_customer.member_id = membership.pk
                    exist_customer.save()

                exist_username = CustomerAccount.objects.filter(
                    username=validated_data["username"]
                ).first()

                if exist_username:
                    raise exceptions.UsernameExist("")

                if not exist_customer:
                    exist_customer = create_new_customer(
                        first_name=validated_data["first_name"],
                        last_name=validated_data["last_name"],
                        id_no=validated_data["id_no"],
                        phone_no=validated_data["phone_number"],
                        email=validated_data["email"],
                        country_code=validated_data["country_code"],
                        gender=validated_data["gender"],
                        id_type=validated_data["id_type"],
                        is_member=True,
                    )
                password = validated_data["password"]
                errors = []
                if not any(c.isupper() for c in password):
                    errors.append("Password must contain at least one uppercase letter")
                if not any(c.isdigit() for c in password):
                    errors.append("Password must contain at least one digit")
                if not any(c.isupper() for c in password) and not any(
                    c.islower() for c in password
                ):
                    errors.append("Password must contain at least one letter")
                if len(password) < 8:
                    errors.append("Password must be at least 8 characters long.")
                if errors:
                    return Response(
                        {"status": "failed", "message": errors},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                unit_of_work = BcryptPasswordHashing()
                generated_pass = generate_password(
                    password_string=password, uow=unit_of_work
                )

                new_customer_account = CustomerAccount.objects.create(
                    username=validated_data["username"],
                    password=generated_pass,
                    customer_id=exist_customer.pk,
                )

            return_serializer = CustomerAccountSerializer(new_customer_account)
            return Response(
                {
                    "status": "success",
                    "message": "account created",
                    "data": return_serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        except exceptions.UsernameExist as e:
            return Response(
                {"status": "failed", "message": "Username already exist"},
                status=status.HTTP_409_CONFLICT,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="membership-login",
        url_name="membership-login",
        permission_classes=[],
    )
    def customer_membership_login(self, request):
        serializer = CustomerMembershipLoginSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                {"status": "failed", "message": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        validated_data = serializer.validated_data
        username = validated_data["username"]
        password = validated_data["password"]

        try:
            if not CustomerAccount.objects.filter(username=username).exists():
                return Response(
                    {"status": "failed", "message": "Username does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            customer_account = CustomerAccount.objects.get(username=username)

            unit_of_work = BcryptPasswordHashing()
            password_match = validate_password(
                req_password=password,
                hashed_saved_password=customer_account.password,
                uow=unit_of_work,
            )

            if password_match:

                payload = {
                    "username": customer_account.username,
                    "first_name": customer_account.customer.firstname,
                    "last_name": customer_account.customer.lastname,
                    "id_no": customer_account.customer.id_no,
                    "country_code": customer_account.customer.country.country_code,
                    "email": customer_account.customer.email,
                    "phone_no": customer_account.customer.phone_number,
                    "gender": str(customer_account.customer.gender),
                    "exp": datetime.utcnow() + timedelta(days=10),
                }
                jwt_token = jwt.encode(
                    payload, os.environ["TOKEN_SECRET_KEY"], algorithm="HS256"
                )

                return Response(
                    {
                        "status": "success",
                        "message": "Login successful",
                        "token": jwt_token,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "failed", "message": "Password does not match"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="get-customer-booking",
        url_name="get-customer-booking",
        permission_classes=[TokenAuthentication],
    )
    def get_customer_booking(self, request):

        try:
            payload = request.user_payload
            username = payload.get("username")
            customer_account = CustomerAccount.objects.get(username=username)
            customer_id = customer_account.customer_id

            customer_booking_list = (
                Booking.objects.filter(customer_staying=customer_id)
                .select_related("customer_staying", "platform")
                .prefetch_related(
                    Prefetch(
                        "room_bookings",
                        queryset=RoomBooking.objects.select_related("room"),
                        to_attr="room_bookings_with_room_info",
                    ),
                    Prefetch(
                        "booking_status",
                        queryset=BookingStatus.objects.filter(is_latest=True),
                        to_attr="latest_booking_status",
                    ),
                )
            )

            booking_serializer = BookingLandingPageSerializer(
                customer_booking_list, many=True
            )

            return Response(
                {
                    "status": "success",
                    "message": booking_serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer account does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="change-customer-password",
        url_name="change-customer-password",
        permission_classes=[TokenAuthentication],
    )
    def change_customer_password(self, request):
        serializer = ChangeCustomerPasswordSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"status": "failed", "message": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        validated_data = serializer.validated_data
        old_password = validated_data["old_password"]
        new_password = validated_data["new_password"]
        errors = []
        if not any(c.isupper() for c in new_password):
            errors.append("Password must contain at least one uppercase letter")
        if not any(c.isdigit() for c in new_password):
            errors.append("Password must contain at least one digit")
        if not any(c.isupper() for c in new_password) and not any(
            c.islower() for c in new_password
        ):
            errors.append("Password must contain at least one letter")
        if len(new_password) < 8:
            errors.append("Password must be at least 8 characters long.")
        if errors:
            return Response(
                {"status": "failed", "message": errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            payload = request.user_payload
            username = payload.get("username")
            customer_account = CustomerAccount.objects.get(username=username)
            unit_of_work = BcryptPasswordHashing()

            password_match = validate_password(
                req_password=old_password,
                hashed_saved_password=customer_account.password,
                uow=unit_of_work,
            )

            if password_match:

                hashed_new_pass = generate_password(
                    password_string=new_password, uow=unit_of_work
                )
                customer_account.password = hashed_new_pass
                customer_account.save()

                return Response(
                    {"status": "success", "message": "password changed successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "failed", "message": "old password is incorrect"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer account does not exsist"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="change-customer-name",
        url_name="change-customer-name",
        permission_classes=[TokenAuthentication],
    )
    def change_customer_name(self, request):
        serializer = ChangeCustomerNameSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"status": "failed", "message": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            validated_data = serializer.validated_data
            new_firstname = validated_data["new_firstname"]
            new_lastname = validated_data["new_lastname"]
            payload = request.user_payload
            username = payload.get("username")
            if not username:
                return Response(
                    {"status": "failed", "message": "username not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            customer_account = CustomerAccount.objects.get(username=username)
            customer = customer_account.customer
            changes_made = False

            if new_firstname is not None and customer.firstname != new_firstname:
                customer.firstname = new_firstname
                changes_made = True
            if new_lastname is not None and customer.lastname != new_lastname:
                customer.lastname = new_lastname
                changes_made = True

            if changes_made:
                customer.save()
                return Response(
                    {"status": "success", "message": "Name changed successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "success", "message": "No changes made"},
                    status=status.HTTP_200_OK,
                )

        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "CustomerAccount not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="change-customer-idno",
        url_name="change-custoner-idno",
        permission_classes=[TokenAuthentication],
    )
    def change_customer_idnno(self, request):
        serializer = ChangeCustomerIdnoSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"status": "failed", "message": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            validated_data = serializer.validated_data
            new_idno = validated_data["new_idno"]
            payload = request.user_payload
            username = payload.get("username")
            if not username:
                return Response(
                    {"status": "failed", "message": "username not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            customer_account = CustomerAccount.objects.get(username=username)
            customer = customer_account.customer

            if Customer.objects.filter(
                id_no=new_idno, country=customer.country
            ).exists():
                return Response(
                    {
                        "status": "failed",
                        "message": "ID number already exists for the given country",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            customer.id_no = new_idno
            customer.save()
            return Response(
                {"status": "success", "message": "ID number changed successfully"},
                status=status.HTTP_200_OK,
            )
        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer account not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="claim-promo",
        url_name="claim-promo",
        permission_classes=[TokenAuthentication],
    )
    def claim_promo(self, request):
        serializer = ClaimPromoSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"status": "failed", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            validated_data = serializer.validated_data
            individual_promo_id = validated_data["individual_promo_id"]
            payload = request.user_payload
            username = payload.get("username")

            if not username:
                return Response(
                    {"status": "failed", "message": "username not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            customer_account = CustomerAccount.objects.get(username=username)
            customer = customer_account.customer

            if MemberPromolist.objects.filter(
                individual_promo_id=individual_promo_id, customer=customer
            ).exists():
                return Response(
                    {"status": "failed", "message": "Promo already claimed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            individual_promo = IndividualPromo.objects.get(
                individual_promo_id=individual_promo_id
            )

            if individual_promo.archived:
                raise IndividualPromo.DoesNotExist

            MemberPromolist.objects.create(
                individual_promo=individual_promo, customer=customer
            )
            return Response(
                {"status": "success", "message": "Promo claimed successfully"},
                status=status.HTTP_200_OK,
            )
        except IndividualPromo.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Promo not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @action(
    #     detail=False,
    #     methods=["POST"],
    #     url_path="claim-milestone-promo",
    #     url_name="claim-milestone-promo",
    #     permission_classes=[TokenAuthentication],
    # )
    # def claim_milestone_promo(self, request):
    #     serializer = ClaimPromoSerializer(data=request.data)
    #     if not serializer.is_valid():
    #         return Response(
    #             {"status": "failed", "data": serializer.errors},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )
    #     try:
    #         validated_data = serializer.validated_data
    #         individual_promo_id = validated_data["individual_promo_id"]
    #         payload = request.user_payload
    #         username = payload.get("username")

    #         if not username:
    #             return Response(
    #                 {"status": "failed", "message": "username not found"},
    #                 status=status.HTTP_400_BAD_REQUEST,
    #             )

    #         # check if given milestone promo id exist
    #         individual_promo = IndividualPromo.objects.get(
    #             individual_promo_id=individual_promo_id
    #         )
    #         if individual_promo.archived or not individual_promo.is_milestone:
    #             raise IndividualPromo.DoesNotExist

    #         customer_account = CustomerAccount.objects.get(username=username)
    #         customer = customer_account.customer

    #         if MemberPromolist.objects.filter(
    #             individual_promo_id=individual_promo_id, customer=customer
    #         ).exists():
    #             return Response(
    #                 {"status": "failed", "message": "Promo already claimed"},
    #                 status=status.HTTP_400_BAD_REQUEST,
    #             )

    #         MemberPromolist.objects.create(
    #             individual_promo=individual_promo, customer=customer
    #         )
    #         return Response(
    #             {"status": "success", "message": "Promo claimed successfully"},
    #             status=status.HTTP_200_OK,
    #         )
    #     except IndividualPromo.DoesNotExist:
    #         return Response(
    #             {"status": "failed", "message": "Promo not found"},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )
    #     except CustomerAccount.DoesNotExist:
    #         return Response(
    #             {"status": "failed", "message": "Customer not found"},
    #             status=status.HTTP_404_NOT_FOUND,
    #         )
    #     except Exception as e:
    #         return Response(
    #             {"status": "failed", "message": "server error", "error": str(e)},
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    #         )

    @action(
        detail=False,
        methods=["POST"],
        url_path="claim-milestone-promo",
        url_name="claim-milestone-promo",
        permission_classes=[TokenAuthentication],
    )
    def claim_milestone_promo(self, request):
        serializer = ClaimPromoSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"status": "failed", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            validated_data = serializer.validated_data
            individual_promo_id = validated_data["individual_promo_id"]
            payload = request.user_payload
            username = payload.get("username")

            if not username:
                return Response(
                    {"status": "failed", "message": "username not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # check if given milestone promo id exist
            milestone_promo = IndividualPromo.objects.get(
                individual_promo_id=individual_promo_id
            )
            if milestone_promo.archived or not milestone_promo.is_milestone:
                raise IndividualPromo.DoesNotExist

            customer_account = CustomerAccount.objects.get(username=username)
            customer = customer_account.customer

            # check user eligibility to claim milestone promo
            milestone_progress = customer_milestone_progress(
                customer=customer, milestone_hours_to_hit=milestone_promo.hours_to_hit
            )
            customer_available_point = milestone_progress["customer_available_point"]
            if customer_available_point < milestone_promo.hours_to_hit:
                point_needed = milestone_promo.hours_to_hit - customer_available_point
                return Response(
                    {
                        "status": "failed",
                        "message": f"need another {point_needed} points to claim this promo.",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            MemberPromolist.objects.create(
                individual_promo=milestone_promo, customer=customer
            )
            return Response(
                {"status": "success", "message": "Promo claimed successfully"},
                status=status.HTTP_200_OK,
            )
        except IndividualPromo.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Promo not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @action(
    #     detail=False,
    #     methods=['GET'],
    #     url_path="user-milestone",
    #     url_name="user-milestone",
    #     permission_classes=[TokenAuthentication],
    # )
    # def check_user_claimed_milestone(self, request):
    #     try:

    #         id_no = request.user_payload.get("id_no")

    #         try:
    #             customer_account = CustomerAccount.objects.get(customer__id_no=id_no)
    #         except:
    #             return Response({
    #                 "status" : "failed",
    #                 "message" : "customer account not found"
    #             }, status=status.HTTP_404_NOT_FOUND)

    #         user_milestone_promo_exist = MemberPromolist.objects.filter(
    #             customer_id = customer_account.customer.pk,
    #             individual_promo__is_milestone = True
    #         ).exists()

    #         return Response({
    #             "status" : "success",
    #             "data" : {
    #                 "isClaimed" : user_milestone_promo_exist
    #             }
    #         }, status=status.HTTP_200_OK)

    #     except Exception as e:
    #         return Response(
    #             {"status":"failed","message":"server error","error":str(e)},
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )

    @action(
        detail=False,
        methods=["GET"],
        url_path="user-milestone",
        url_name="user-milestone",
        permission_classes=[TokenAuthentication],
    )
    def check_user_milestone_progress(self, request):
        try:

            id_no = request.user_payload.get("id_no")

            try:
                customer_account = CustomerAccount.objects.get(customer__id_no=id_no)
            except:
                return Response(
                    {"status": "failed", "message": "customer account not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # get current active milestone promo
            milestone_promo = IndividualPromo.objects.filter(
                is_milestone=True, archived=False
            ).first()

            if not milestone_promo:
                return Response(
                    {"status": "failed", "message": "no milestone promo found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            customer = customer_account.customer

            membership_progress = customer_milestone_progress(
                customer=customer, milestone_hours_to_hit=milestone_promo.hours_to_hit
            )

            return Response(
                {
                    "status": "success",
                    "data": {
                        "customer_total_point": customer.point,
                        "customer_available_point": membership_progress[
                            "customer_available_point"
                        ],
                        "customer_cycle": membership_progress["customer_cycle"],
                        "customer_milestone_progress": membership_progress[
                            "customer_milestone_progress"
                        ],
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="customer-info",
        url_name="customer-info",
        permission_classes=[TokenAuthentication],
    )
    def get_customer_info(self, request):
        try:
            id_no = request.user_payload.get("id_no")
            customer_account = CustomerAccount.objects.get(customer__id_no=id_no)

            serializer = CustomerInfoSerializer(customer_account)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except CustomerAccount.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Customer not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CsrfTokenViewSet(viewsets.ViewSet):
    authentication_classes = []
    permission_classes = []

    @action(
        detail=False,
        methods=["GET"],
        url_path="generate-csrf-token",
        url_name="generate-csrf-token",
    )
    def generate_csrf_token(self, request):
        try:
            csrf_token = get_token(self, request)
            return Response({"csrfToken": csrf_token}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
