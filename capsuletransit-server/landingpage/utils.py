import os
from .dependency.password import PasswordHashing
from django.core.mail import EmailMessage, get_connection


def validate_password(
    req_password: str, hashed_saved_password: str, uow: PasswordHashing
) -> bool:
    return uow.match_hashed_pass(
        req_password=req_password, saved_hased_pass=hashed_saved_password
    )


def generate_password(password_string: str, uow: PasswordHashing) -> str:
    return uow.generate_pass(password_str=password_string)


def get_email_password(lot):
    if lot == 1:
        return {
            "email": os.environ["EMAIL_LANDSIDE_USER"],
            "password": os.environ["EMAIL_LANDSIDE_PASSWORD"],
        }
    elif lot == 2:
        return {
            "email": os.environ["EMAIL_AIRSIDE_USER"],
            "password": os.environ["EMAIL_AIRSIDE_PASSWORD"],
        }
    elif lot == 3:
        return {
            "email": os.environ["EMAIL_SLEEPLOUNGE_USER"],
            "password": os.environ["EMAIL_SLEEPLOUNGE_PASSWORD"],
        }
    elif lot == 4:
        return {
            "email": os.environ["EMAIL_MAX_USER"],
            "password": os.environ["EMAIL_MAX_PASSWORD"],
        }
    else:
        return {
            "email": os.environ["EMAIL_AIRSIDE_USER"],
            "password": os.environ["EMAIL_AIRSIDE_PASSWORD"],
        }
