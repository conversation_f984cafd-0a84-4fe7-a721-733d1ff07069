# Generated by Django 4.2.3 on 2024-06-10 08:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('transaction', '0007_alter_transaction_payment_reference'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentGateawayTransaction',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('payment_gateaway_transaction_ref', models.CharField(max_length=100)),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='transaction.transaction')),
            ],
        ),
    ]
