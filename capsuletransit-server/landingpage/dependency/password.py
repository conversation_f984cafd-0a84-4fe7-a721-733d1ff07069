import bcrypt


class PasswordHashing():

    def match_hashed_pass(
        self,
        req_password : str,
        saved_hased_pass : str
    ) -> bool:
        raise Exception('this class must contain match_hashed_pass method')
    
    def generate_pass(self, password_str : str) -> str:
        raise Exception('this class must contain generate_pass method')
    


class BcryptPasswordHashing(PasswordHashing):

    def match_hashed_pass(self, req_password: str, saved_hased_pass: str) -> bool:
        try:
            pass_bytes = req_password.encode('utf-8')
            saved_hased_pass_bytes = saved_hased_pass.encode('utf-8')

            if not bcrypt.checkpw(
                password=pass_bytes,
                hashed_password=saved_hased_pass_bytes
            ):
                return False

            return True
        except:
            raise Exception('something wrong')
    
    def generate_pass(self, password_str: str) -> str:

        try:
            encoded_pass = password_str.encode('utf-8')
            salt = bcrypt.gensalt()

            hashed_pass = bcrypt.hashpw(encoded_pass, salt).decode('utf-8')
            return hashed_pass
        except:
            raise Exception('something wrong')
    