import os
import jwt
import dotenv
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import BasePermission

dotenv.load_dotenv()

class TokenAuthentication(BasePermission):

    def has_permission(self, request, view):
        auth_header = request.headers.get('Authorization')

        if not auth_header:
            return False
        
        try:
            
            token = auth_header.split(" ")[1]

            payload = jwt.decode(token,os.environ["TOKEN_SECRET_KEY"], algorithms=["HS256"])
            request.user_payload = payload

            return True
            
        except jwt.ExpiredSignatureError:
            return False
        except jwt.InvalidTokenError:
            return False