# Deploy to Azure Kubernetes Service
# Build and push image to Azure Container Registry; Deploy to Azure Kubernetes Service
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
  branches:
    include:
      - staging 
  paths:
    include:
      - capsuletransit-server/*
resources:
- repo: self

variables:
  - group: 'STAGING'
  - name: dockerRegistryServiceConnection
    value: '************************************'
  - name: imageRepository
    value: 'capsuletransit'
  - name: containerRegistry
    value: 'infrontseadev.azurecr.io'
  - name: dockerfilePath
    value: '$(Build.SourcesDirectory)/capsuletransit-server/Dockerfile'
  - name: tag
    value: '$(Build.BuildId)'
  - name: imagePullSecret
    value: 'acr-secret-infront-sea-dev-staging'
  - name: vmImageName
    value: 'ubuntu-latest'
  #Agent VM image name

pool:
  name: Agent-SEA-DEV

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    steps:
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: $(tag)

    - upload: capsuletransit-server/manifests
      artifact: manifests

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build

  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: 'STAGING.capsuletransit-staging'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: KubernetesManifest@0
            displayName: Create imagePullSecret
            inputs:
              action: createSecret
              secretName: $(imagePullSecret)
              dockerRegistryEndpoint: $(dockerRegistryServiceConnection)

          - task: KubernetesManifest@0
            displayName: Deploy to Kubernetes cluster
            inputs:
              action: deploy
              manifests: |
                $(Pipeline.Workspace)/manifests/deployment.yml
                $(Pipeline.Workspace)/manifests/service.yml
              imagePullSecrets: |
                $(imagePullSecret)
              containers: |
                $(containerRegistry)/$(imageRepository):$(tag)
