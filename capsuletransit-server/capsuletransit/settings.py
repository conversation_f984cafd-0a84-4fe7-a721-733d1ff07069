"""
Django settings for capsuletransit project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import dotenv

dotenv.load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-qggyt@!afyxysz6qo#xk-#t=w--b8tu*jn3d^3zbevxnj8j!t3"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [
    "*************",
    "127.0.0.1",
    "capsuletransitbackend.southeastasia.cloudapp.azure.com",
    "*",
]
ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # installed
    "rest_framework",
    "rest_framework_simplejwt",
    "corsheaders",
    "django_filters",
    # "django_cron",
    "django_crontab",
    # APPS
    "accounts",
    "rooms",
    "bookings",
    "guests",
    "lockers",
    "staff",
    "payment",
    "transaction",
    "cashierterminal",
    "currency",
    "lot",
    "airportCode",
    "shower",
    "merch",
    "promotions",
    "drf_yasg",
    "salesreport",
    "bookingplatform",
    "staah",
    "paymentterminal",
    "accesscard",
    "landingpage",
    "target",
]

CRON_CLASSES = ["transaction.cron.ReportZReport"]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
]

ROOT_URLCONF = "capsuletransit.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "capsuletransit.wsgi.application"


REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": (
        "rest_framework.permissions.IsAuthenticated",
        # 'rest_framework.permissions.IsAdminUser',
    ),
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {"anon": "100/second", "user": "100/second"},
    "JSON_UNDERSCOREIZE": {"ignore_fields": ("json",)},
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=10),
    # "ACCESS_TOKEN_LIFETIME": timedelta(hours=20),
    "REFRESH_TOKEN_LIFETIME": timedelta(hours=20),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "JWK_URL": None,
    "LEEWAY": 0,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "account_id",
    "USER_ID_CLAIM": "account_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "JTI_CLAIM": "jti",
    "TOKEN_OBTAIN_SERIALIZER": "api.serializers.MyTokenObtainPairSerializer",
    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
}

AUTH_USER_MODEL = "accounts.Account"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.postgresql",
#         "HOST": os.getenv("DB_NAME"),
#         "PORT": os.getenv("DB_PORT"),
#         "NAME": "postgres",
#         "USER": os.getenv("DB_USER"),
#         "PASSWORD": os.getenv("DB_PASSWORD"),
#         "OPTIONS": {
#             "sslmode": "require",
#         },
#     }
# }
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "HOST": os.environ["DB_HOST"],
        "PORT": os.environ["DB_PORT"],
        "NAME": os.environ["DB_NAME"],
        "USER": os.environ["DB_USER"],
        "PASSWORD": os.environ["DB_PASSWORD"],
    }
}
# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

CORS_ALLOW_ALL_ORIGIN = True

CORS_ALLOWED_ORIGINS = [
    "http://**************",
    "http://localhost:3000",
    "http://ctlandingpage.ginan.cloud",
    "http://capsule-transit.southeastasia.cloudapp.azure.com",
    "https://capsuletransit.com",
    "http://***********",
    "http://*************"
]
CORS_ALLOW_ALL_ORIGINS = True


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")


CRONJOBS = [
    # ("*/1 * * * *", "django.core.management.call_command", ["mark_overstay"]),
    (("* * * * *", "transaction.cron.run_report")),
]

SWAGGER_SETTINGS = {
    "DEFAULT_AUTO_SCHEMA_CLASS": "drf_yasg.inspectors.SwaggerAutoSchema",
}


# import logging

# class SQLFormatter(logging.Formatter):
#     def format(self, record):

#         # Check if Pygments is available for coloring
#         try:
#             import pygments
#             from pygments.lexers import SqlLexer
#             from pygments.formatters import TerminalTrueColorFormatter
#         except ImportError:
#             pygments = None

#         # Check if sqlparse is available for indentation
#         try:
#             import sqlparse
#         except ImportError:
#             sqlparse = None

#         # Remove leading and trailing whitespaces
#         sql = record.sql.strip()

#         if sqlparse:
#             # Indent the SQL query
#             sql = sqlparse.format(sql, reindent=True)

#         if pygments:
#             # Highlight the SQL query
#             sql = pygments.highlight(
#                 sql,
#                 SqlLexer(),
#                 #TerminalTrueColorFormatter(style='monokai')
#                 TerminalTrueColorFormatter()
#             )

#         # Set the records statement to the formatted query
#         record.statement = sql
#         return super(SQLFormatter, self).format(record)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "error.log",
            "formatter": "verbose",
        },
        "django_file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "django_error.log",
            "formatter": "verbose",
        },
        "crontab_file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": "django_crontab.log",
            "formatter": "verbose",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "django_file"],
            "level": "ERROR",
            "propagate": True,
        },
        "django.request": {
            "handlers": ["console", "file"],
            "level": "ERROR",
            "propagate": False,
        },
        "django.server": {
            "handlers": ["console", "file"],
            "level": "ERROR",
            "propagate": False,
        },
        "django.db.backends": {
            "handlers": ["console", "file"],
            "level": "ERROR",
            "propagate": False,
        },
        "django.security": {
            "handlers": ["console", "file"],
            "level": "ERROR",
            "propagate": False,
        },
        "django_crontab": {
            "handlers": ["crontab_file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "api": {
            "handlers": ["console", "file"],
            "level": "ERROR",
            "propagate": False,
        },
    },
}

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.environ["EMAIL_HOST"]
# EMAIL_HOST_USER = os.environ["EMAIL_HOST_USER"]
DEFAULT_FROM_EMAIL = os.environ["EMAIL_AIRSIDE_USER"]
EMAIL_HOST_PASSWORD = os.environ["EMAIL_AIRSIDE_PASSWORD"]
EMAIL_PORT = os.environ["EMAIL_PORT"]
EMAIL_USE_SSL = True
EMAIL_USE_TLS = False
