from django.db import models

class Currency(models.Model):
    CURRENCY_CHOICES = [
        ('USD', 'United States Dollar'),
        ('AUD', 'Australian Dollar'),
        ('BND', 'Bruneian Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('JPY', 'Japanese Yen'),
        ('SDG', 'Singapore Dollar'),
        ('CNY', 'Chinese Yuan Renminbi'),
        ('CAD', 'Canadian Dollar'),
        ('HKD', 'Hong Kong Dollar'),
        ('NZD', 'New Zealand Dollar'),
        ('IDR', 'Indonesian Rupiah'),
        ('TWD', 'New Taiwanese Dollar'),
        ('INR', 'Indian Rupee'),
        ('KRW', 'Korean Won'),
        ('THB', 'Thai Baht'),
        ('MYR', 'Malaysia Ringgit'),
    ]

    currency_id = models.AutoField(primary_key=True, verbose_name='CurrencyID')
    currency_code = models.CharField(max_length=20, unique=True, verbose_name='Currency Code')
    currency_description = models.CharField(max_length=50, verbose_name='Currency Description')

    def __str__(self):
        return self.currency_code
