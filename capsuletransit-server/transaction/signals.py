from bookings.services.booking import check_out_pos_booking, create_merch_booking
from django.db import transaction
from django.db.models.signals import post_save
from django.dispatch import receiver
from merch.services.merch import deduct_merch_after_transaction
from promotions.promotion import handle_use_promo
from bookings.services.booking import handle_add_booking_hour
from transaction.models import Transaction
from transaction.services.transaction import (
    # pay_pending_transaction_for_signal,
    transaction_confirm_booking_for_not_room_booking,
    transactions_confirm_booking,
)
from promotions.promotion import revert_used_individual_promo


@receiver(post_save, sender=Transaction)
def confirm_booking_check_signal(instance: Transaction, **kwargs):
    """
    Signal listener to check if booking can be confirmed
    """
    if instance.is_room_booking:
        transactions_confirm_booking(instance.booking_id)

    if not instance.is_room_booking and instance.transaction_status == "Paid":
        transaction_confirm_booking_for_not_room_booking(instance.booking_id)


@receiver(post_save, sender=Transaction)
def multipurpose_transaction_signal(instance: Transaction, **kwargs):

    if instance.transaction_status == "Paid":
        handle_add_booking_hour(transaction_obj=instance)

    if instance.transaction_status == "Paid" and instance.promotion_amount > 0:
        handle_use_promo(transaction_id=instance.pk)

    if (
        instance.customer
        and instance.customer.firstname == "Non Guest"
        and instance.transaction_status == "Paid"
    ):
        check_out_pos_booking(booking_id=instance.booking.pk)

    with transaction.atomic():
        if instance.transaction_status == "Paid":
            create_merch_booking(transaction_data=instance)
            deduct_merch_after_transaction(transaction_data=instance)
    
    # Revert individual promo usage on refund
    if instance.refunded:
        revert_used_individual_promo(transaction_id=instance.transaction_id)


    # if instance.sum == 0 and instance.transaction_status == "Pending Payment":
    #     pay_pending_transaction_for_signal(transaction_id=instance.pk)

