# Generated by Django 4.2.3 on 2024-03-08 08:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("transaction", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="RefundForm",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("refund_form", models.BinaryField()),
                ("status_datetime", models.DateTimeField()),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transaction.transaction",
                        verbose_name="Transaction ID",
                    ),
                ),
            ],
        ),
    ]
