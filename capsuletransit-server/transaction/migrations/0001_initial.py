# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("payment", "0001_initial"),
        ("bookings", "0001_initial"),
        ("guests", "0001_initial"),
        ("currency", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "transaction_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("pan", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "invoice_no",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Invoice No"
                    ),
                ),
                ("payment_details", models.TextField(verbose_name="Payment Details")),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Sum"
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Tax Amount"
                    ),
                ),
                (
                    "service_charge_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Service Charge Amount",
                    ),
                ),
                ("adjustments", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "adjustements_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "promotion_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "credit_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Credit Amount"
                    ),
                ),
                (
                    "debit_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Debit Amount"
                    ),
                ),
                (
                    "transaction_datetime",
                    models.DateTimeField(
                        null=True, verbose_name="Transaction Datetime"
                    ),
                ),
                (
                    "rounding",
                    models.DecimalField(
                        decimal_places=4,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Rounding",
                    ),
                ),
                (
                    "transaction_status",
                    models.CharField(
                        choices=[
                            ("Pending Payment", "Pending Payment"),
                            ("Paid", "Paid"),
                            ("Refund", "Refund"),
                            ("Void", "Void"),
                            ("Paid at Landside", "Paid at Landside"),
                            ("Paid at Airside", "Paid at Airside"),
                            ("Pay at Landside", "Pay at Landside"),
                            ("Pay at Airside", "Pay at Airside"),
                        ],
                        max_length=50,
                        verbose_name="Transaction Status",
                    ),
                ),
                ("is_latest", models.BooleanField(verbose_name="Is Latest")),
                (
                    "is_room_booking",
                    models.BooleanField(default=False, verbose_name="Is Booking"),
                ),
                ("items", models.JSONField(null=True, verbose_name="Items")),
                (
                    "payment_reference",
                    models.CharField(
                        blank=True,
                        help_text="to store credit card OTP or payment site OTP",
                        max_length=20,
                        null=True,
                        verbose_name="Payment Reference",
                    ),
                ),
                (
                    "payment_remarks",
                    models.CharField(
                        blank=True,
                        help_text="to let audit team dump in notes",
                        max_length=100,
                        null=True,
                        verbose_name="Payment Remarks",
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="bookings.booking",
                        verbose_name="Booking ID",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="currency.currency",
                        verbose_name="Currency ID",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="guests.customer",
                        verbose_name="Customer ID",
                    ),
                ),
                (
                    "payment_type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="payment.paymenttype",
                        verbose_name="Payment Type ID",
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.shift",
                        verbose_name="Shift ID",
                    ),
                ),
            ],
        ),
    ]
