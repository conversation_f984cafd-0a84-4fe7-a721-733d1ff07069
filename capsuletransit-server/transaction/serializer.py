from rest_framework import serializers
from django.utils import timezone
from django.db import transaction as tr
from django.db.models import Q, F

from .models import *
from .utils import calculate_amount_percentage, get_payment_details, generate_invoice_no
from bookings.models import Booking, RoomBooking
from accounts.models import Shift
from accounts.utils import get_current_shift

# from transaction.services.transaction import confirm_booking

from payment.serializer import PaymentTypeSerializer
from guests.serializer import CustomerOnlySerializer
from queryservice.serializers import TransactionShiftReportSerializer

# class TransactionSerializer(serializers.ModelSerializer):
#     account_id = serializers.UUIDField(write_only=True)

#     class Meta:
#         model = Transaction
#         fields = "__all__"
#         extra_kwargs = {
#             "invoice_no": {"required": False},
#             "payment_details": {"required": False},
#             # "sum": {"required": False},
#             # "debit_amount": {"required": False},
#             "transaction_status": {"required": False},
#             "is_latest": {"required": False},
#             "currency": {"required": False},
#             "shift": {"required": False},
#         }

#     def create(self, validated_data):
#         user_id = validated_data.pop("account_id")
#         booking = validated_data.pop("booking")
#         currency = validated_data.pop("currency")
#         payment_type = validated_data.pop("payment_type")
#         payment_details = validated_data.pop("payment_details")
#         transaction_status = validated_data.pop("transaction_status")

#         try:
#             with tr.atomic():
#                 shift = get_current_shift(account_id=user_id)
#                 transaction_date = timezone.now()
#                 invoice_no = generate_invoice_no(
#                     booking_no=booking.booking_no, transaction_date=transaction_date
#                 )

#                 validated_data["invoice_no"] = invoice_no
#                 validated_data["payment_details"] = payment_details
#                 # validated_data["sum"] = booking.sum
#                 # validated_data["tax_amount"] = calculated_tax
#                 # validated_data["service_charge_amount"] = calculated_service_charge
#                 validated_data["transaction_datetime"] = transaction_date
#                 validated_data["transaction_status"] = transaction_status
#                 validated_data["is_latest"] = True
#                 validated_data["booking_id"] = booking.pk
#                 validated_data["shift_id"] = shift.pk if shift else None
#                 validated_data["currency_id"] = currency.pk
#                 validated_data["payment_type_id"] = payment_type.pk

#                 transaction = Transaction.objects.create(**validated_data)

#                 return transaction

#         except Booking.DoesNotExist:
#             raise serializers.ValidationError({"detail": "Booking not found."})

#         except Exception as e:
#             raise serializers.ValidationError({"detail": str(e)})


class TransactionSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField(required=False)
    adjustements_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    adjustments = serializers.CharField(allow_blank=True)
    booking = serializers.UUIDField(required=False)
    credit_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    currency = serializers.CharField()
    customer = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    debit_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    is_room_booking = serializers.BooleanField()
    items = serializers.ListField(child=serializers.DictField())
    payment_details = serializers.CharField()
    payment_type = PaymentTypeSerializer()
    promotion_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    rounding = serializers.DecimalField(
        max_digits=50, decimal_places=20, required=False
    )
    service_charge_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    sum = serializers.DecimalField(max_digits=50, decimal_places=20)
    tax_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    transaction_status = serializers.CharField()
    invoice_no = serializers.CharField(required=False)
    transaction_datetime = serializers.CharField(required=False)
    pan = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    payment_remarks = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    payment_reference = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guest_given = serializers.DecimalField(
        max_digits=50, decimal_places=20, allow_null=True
    )
    guest_change = serializers.DecimalField(
        max_digits=50, decimal_places=20, allow_null=True
    )


class TransactionExtraInfoSerializer(TransactionSerializer):
    cashier_name = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )


class TransactionWriteSerializer(TransactionSerializer):
    account_id = serializers.UUIDField(required=False)
    added_hour = serializers.CharField(allow_blank=True)
    payment_type = serializers.IntegerField()
    settings_name = serializers.CharField(allow_blank=True)
    in_house_guest = serializers.BooleanField(required=False)


class UpdateTransactionStatusSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField()
    payment_type_id = serializers.CharField()
    status = serializers.CharField()


class PayPendingTransactionSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField()
    payment_type_id = serializers.CharField()
    debit_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    promotion_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    adjustments_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    service_charge_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    tax_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    rounding = serializers.DecimalField(
        max_digits=50, decimal_places=20, required=False
    )
    payment_details = serializers.CharField()
    pan = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    payment_remarks = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    payment_reference = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guest_given = serializers.DecimalField(
        max_digits=50, decimal_places=20, allow_null=True
    )
    guest_change = serializers.DecimalField(
        max_digits=50, decimal_places=20, allow_null=True
    )
    items = serializers.ListField(child=serializers.DictField())


class TransactionReadOnlySerializer(serializers.ModelSerializer):
    payment_type = PaymentTypeSerializer()
    customer = CustomerOnlySerializer()

    class Meta:
        model = Transaction
        fields = "__all__"


class ReportOverviewItem(serializers.Serializer):
    name = serializers.CharField()
    value = serializers.DecimalField(max_digits=50, decimal_places=2)


class ReportOverviewSerializer(serializers.Serializer):
    left = ReportOverviewItem()
    right = ReportOverviewItem()


class TransactionSummary(serializers.Serializer):
    date = serializers.DateTimeField()
    room_sales = serializers.DecimalField(max_digits=50, decimal_places=2)
    no_show = serializers.DecimalField(max_digits=50, decimal_places=2)
    pos = serializers.DecimalField(max_digits=50, decimal_places=2)
    gross = serializers.DecimalField(max_digits=50, decimal_places=2)
    service_charge = serializers.DecimalField(max_digits=50, decimal_places=2)
    roundings = serializers.DecimalField(max_digits=50, decimal_places=2)
    tax = serializers.DecimalField(max_digits=50, decimal_places=2)
    promotion = serializers.DecimalField(max_digits=50, decimal_places=2)
    adjustments = serializers.DecimalField(max_digits=50, decimal_places=2)
    total = serializers.DecimalField(max_digits=50, decimal_places=2)


class TransactionDetaild(serializers.Serializer):
    # TODO : Add rounding to serializer
    booking_id = serializers.UUIDField()
    booking_no = serializers.CharField()
    transfer_from_to = serializers.CharField()
    booking_date = serializers.DateTimeField()
    payment_date = serializers.DateTimeField()
    method = serializers.CharField()
    pan = serializers.CharField()
    payment_status = serializers.CharField()
    booking_status = serializers.CharField()
    check_in_date = serializers.DateTimeField()
    actual_check_in_date = serializers.DateTimeField()
    check_out_date = serializers.DateTimeField()
    actual_check_out_date = serializers.DateTimeField()
    ota = serializers.CharField()
    source = serializers.CharField()
    guest = serializers.CharField()
    total_rooms = serializers.IntegerField()
    interval = serializers.IntegerField()
    room_type = serializers.CharField()
    sales = serializers.DecimalField(max_digits=50, decimal_places=2)
    tax = serializers.DecimalField(max_digits=50, decimal_places=2)
    service_charge = serializers.DecimalField(max_digits=50, decimal_places=2)
    promotions = serializers.DecimalField(max_digits=50, decimal_places=2)
    adjustment_string = serializers.CharField(required=False)
    adjustments = serializers.DecimalField(max_digits=50, decimal_places=2)
    roundings = serializers.DecimalField(max_digits=50, decimal_places=2)
    total = serializers.DecimalField(max_digits=50, decimal_places=2)
    booking_remarks = serializers.CharField()
    no_show_date = serializers.DateTimeField(required=False)


class TransactionSummaryGroupByBooking(TransactionSummary):
    booking_id = serializers.UUIDField()
    booking_no = serializers.CharField()


class TransactionSummaryGroupByPaymentMethod(TransactionSummary):
    payment_method_id = serializers.UUIDField()
    payment_method_name = serializers.CharField()


class TransactionSummaryGroupBypaymentType(TransactionSummary):
    payment_type_id = serializers.UUIDField()
    payment_type_name = serializers.CharField()


class TransactionSummaryGroupByBookingPlatform(TransactionSummary):
    booking_platform_id = serializers.CharField()
    booking_platform = serializers.CharField()


class TransactionSummaryGroupByBookingDuration(TransactionSummary):
    booking_duration = serializers.CharField()


class TransactionSummaryGroupByRoomType(TransactionSummary):
    room_type_name = serializers.CharField()


class TransactionSummaryGroupByPromotion(TransactionSummary):
    promotion_name = serializers.CharField()


class TransactionDetailedGroupByTransaction(TransactionDetaild):
    invoice_no = serializers.CharField()
    is_pos = serializers.BooleanField()


class ZReportKLIADetailedSerializer(serializers.Serializer):
    item = serializers.IntegerField()
    type = serializers.CharField()
    id = serializers.CharField()
    details = serializers.ListField(
        child=serializers.DictField(), required=False, default=None
    )
    payment_date = serializers.CharField()
    check_in = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    check_out = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sales = serializers.DecimalField(max_digits=50, decimal_places=2)


class ZReportKLIASummarySerializer(serializers.Serializer):
    item = serializers.IntegerField()
    date = serializers.CharField()
    room_sales = serializers.DecimalField(max_digits=50, decimal_places=2)
    pos_sales = serializers.DecimalField(max_digits=50, decimal_places=2)
    total = serializers.DecimalField(max_digits=50, decimal_places=2)


class ZReportGatewaySalesSerializer(serializers.Serializer):
    transaction_id = serializers.CharField()
    total_amount = serializers.DecimalField(max_digits=19, decimal_places=2)
    service_charge_amount = serializers.DecimalField(max_digits=19, decimal_places=2)
    tax_amount = serializers.DecimalField(max_digits=19, decimal_places=2)
    status = serializers.CharField()
    sales_date = serializers.CharField()
    business_date = serializers.CharField()
    need_update_sales = serializers.BooleanField()


class OverstayPoaSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()


class ZReportGatewayPaymentSerializer(serializers.Serializer):
    transaction_id = serializers.CharField()
    amount = serializers.DecimalField(max_digits=19, decimal_places=2)
    currency_code = serializers.CharField()
    currency_amount = serializers.DecimalField(max_digits=19, decimal_places=2)
    currency_exchange_rate = serializers.DecimalField(max_digits=19, decimal_places=2)
    payment_date = serializers.CharField()
    business_date = serializers.CharField()
    payment_method = serializers.CharField()
    payment_type = serializers.CharField()


class UpdatePendingTransactionSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField()
    sum = serializers.DecimalField(max_digits=50, decimal_places=20)
    credit_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    promotion_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    service_charge_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    tax_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    adjustment_amount = serializers.DecimalField(max_digits=50, decimal_places=20)
    rounding = serializers.DecimalField(
        max_digits=50, decimal_places=20, required=False
    )
    payment_details = serializers.CharField()
    items = serializers.ListField(child=serializers.DictField())


class RefundSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField()
    reason = serializers.CharField()
    payment_type_id = serializers.IntegerField()
    refund_form = serializers.FileField()


class ChangePaymentDetails(serializers.Serializer):
    transaction_id = serializers.UUIDField()
    payment_remarks = serializers.CharField(allow_null=True, allow_blank=True)
    payment_reference = serializers.CharField(allow_null=True, allow_blank=True)
    pan = serializers.CharField(allow_null=True, allow_blank=True)
    guest_given = serializers.DecimalField(
        allow_null=True, decimal_places=2, max_digits=10
    )
    guest_change = serializers.DecimalField(
        allow_null=True, decimal_places=2, max_digits=10
    )


class LedgerDynamicFilterSerializer(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField(allow_null=True, allow_blank=True)


class LedgerDetailedSerializer(serializers.Serializer):
    transactions = TransactionShiftReportSerializer(many=True)


class LedgerPaymentMethodDetailedSerializer(LedgerDetailedSerializer):
    payment_method_id = serializers.CharField()
    payment_method_name = serializers.CharField()


class LedgerPaymentTypeDetailedSerializer(LedgerDetailedSerializer):
    payment_type_id = serializers.CharField()
    payment_type_name = serializers.CharField()


class LedgerBookingPlatformDetailedSerializer(LedgerDetailedSerializer):
    booking_platform_id = serializers.CharField()
    booking_platform = serializers.CharField()


class LedgerRoomTypeDetailedSerializer(LedgerDetailedSerializer):
    room_type_name = serializers.CharField()


class LedgerPromotionDetailedSerializer(LedgerDetailedSerializer):
    promotion_name = serializers.CharField()


class RemoveTransactionItem(serializers.Serializer):
    booking_id = serializers.UUIDField()
    category = serializers.CharField()
    item_name = serializers.CharField()
    count = serializers.IntegerField()


class GenerateReceiptHMSSerializer(serializers.Serializer):
    transaction_id = serializers.UUIDField()


class RefundDetailsSerializer(serializers.Serializer):
    remarks = serializers.CharField()
    refunded_amount = serializers.DecimalField(max_digits=50, decimal_places=2)
    payment_type = serializers.CharField()
    refund_form_url = serializers.CharField()
    staff_name = serializers.CharField()


class CollectionSummarySerializer(serializers.Serializer):
    payment_type_name = serializers.CharField()
    payment_type_id = serializers.IntegerField()
    total = serializers.DecimalField(decimal_places=2, max_digits=20)


class AdvancedSummarySerializer(serializers.Serializer):
    sales = serializers.DecimalField(decimal_places=2, max_digits=20)
    sst = serializers.DecimalField(decimal_places=2, max_digits=20)
    promotion = serializers.DecimalField(decimal_places=2, max_digits=20)
    adjustment = serializers.DecimalField(decimal_places=2, max_digits=20)
    roundings = serializers.DecimalField(decimal_places=2, max_digits=20)
    total_deposit = serializers.DecimalField(decimal_places=2, max_digits=20)


class AdvancedSummaryGroupByBooking(AdvancedSummarySerializer):
    booking_id = serializers.CharField()
    booking_no = serializers.CharField()
    booking_status = serializers.CharField()


class AdvancedSummaryGroupByDate(AdvancedSummarySerializer):
    date = serializers.CharField(required=False)


class AdvancedSummaryGroupByTransaction(AdvancedSummarySerializer):
    transaction_id = serializers.CharField()
    invoice_no = serializers.CharField()


class AdvancedDetailedSerializer(serializers.Serializer):
    payment_date = serializers.CharField()
    check_in_date = serializers.CharField()
    actual_check_in_date = serializers.CharField(required=False)
    booking_id = serializers.CharField()
    booking_status = serializers.CharField()
    sales = serializers.DecimalField(decimal_places=2, max_digits=20)
    promotion = serializers.DecimalField(decimal_places=2, max_digits=20)
    adjustment = serializers.DecimalField(decimal_places=2, max_digits=20)
    roundings = serializers.DecimalField(decimal_places=2, max_digits=20)
    sst = serializers.DecimalField(decimal_places=2, max_digits=20)
    total_deposit = serializers.DecimalField(decimal_places=2, max_digits=20)


class SalesItemSerializer(serializers.Serializer):
    item_code = serializers.CharField()
    item_name = serializers.CharField()
    sold_qty = serializers.IntegerField()
    unit_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    sub_total = serializers.DecimalField(max_digits=10, decimal_places=2)
    line_discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    line_tax_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
    line_tax_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    line_tax_type = serializers.CharField()
    product_dictionary_l1 = serializers.CharField()
    product_dictionary_l2 = serializers.CharField()
    product_dictionary_l3 = serializers.CharField()
    product_dictionary_l4 = serializers.CharField(required=False, allow_blank=True)
    product_dictionary_l5 = serializers.CharField(required=False, allow_blank=True)


class CollectionSerializer(serializers.Serializer):
    method = serializers.CharField()
    payment_type = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)


class MAHBZReportSerializer(serializers.Serializer):
    store = serializers.CharField()
    transaction_type = serializers.CharField()
    transaction_status = serializers.CharField()
    transaction_id = serializers.CharField()
    transaction_date = serializers.DateTimeField()
    transaction_total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    transaction_service_charge_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2
    )
    transaction_rounding_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2
    )
    sales_item = SalesItemSerializer(many=True)
    collection = CollectionSerializer(many=True)


class MAHBSummaryZReportSerializer(serializers.Serializer):
    transaction_date = serializers.DateField()
    total = serializers.FloatField()
