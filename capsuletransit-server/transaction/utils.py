import json
import re
from datetime import datetime
from typing import List, Tuple

from transaction.models import Transaction


def get_payment_details(room_bookings):

    payment_details = {"room": []}

    for room in room_bookings:
        payment_details["room"].append({"product": room.room_code_, "amount": 1})

    payment_details_json = json.dumps(payment_details)
    return payment_details_json


def generate_invoice_no(booking_no, transaction_date):
    month = transaction_date.strftime("%m")
    day = transaction_date.strftime("%d")
    hour = transaction_date.strftime("%H")
    minutes = transaction_date.strftime("%M")
    seconds = transaction_date.strftime("%S")
    milliseconds = transaction_date.strftime("%f")[:3]

    return f"{booking_no}{hour}{minutes}{milliseconds}"


def calculate_amount_percentage(price: float, percentage: int) -> float:
    return float(price) * float((percentage / 100))


def get_transaction_bill(transactions: List[Transaction]) -> Tuple[float, float, float]:
    """
    Function to get payment informations from transactions
    returns
    (billed_amount, paid)
    """

    billed_amount = 0
    paid = 0
    for t in transactions:
        billed = t.sum + t.tax_amount + t.service_charge_amount
        paid = t.debit_amount
        billed_amount += billed
        paid += paid

    return (billed_amount, paid)


def calculate_booking_duration(check_in_datetime, check_out_datetime):
    return (check_out_datetime - check_in_datetime).total_seconds() / 3600


def map_payment_method(payment_method: str) -> str:
    payment_mapping = {
        "Cash": "Cash",
        "Card": "Card",
        "E-Wallet": "E-Wallet",
        "Paylater": "Other",
        "Bank Transfer": "Other",
        "Recharge": "Other",
        "Other": "Other",
        "iPay88": "Other",
    }

    return payment_mapping.get(payment_method, "-")


def map_payment_type(payment_type: str) -> str:
    payment_mapping = {
        "GrabPay": "GrabPay",
        "Alipay": "Alipay",
        "Visa Card": "Visa",
        "Master Card": "Mastercard",
        "Debit Card": "Debit",
        "Cash": "Cash",
        "TnG": "TnGwallet",
        "Boost": "Boost",
        "AMEX": "Amex",
        "Paylater": "Others",
        "Complimentary": "Others",
        "Recover Services": "Others",
        "Recharge": "Others",
        "Bank Transfer": "Others",
        "Channel Collect": "Others",
        "Union Pay": "Others",
        "QR Pay": "Others",
        "JCB": "Others",
        "iPay88": "Others",
    }

    return payment_mapping.get(payment_type, "-")


def map_payment_method_v2(payment_method: str) -> str:
    payment_mapping = {
        "Cash": "cash",
        "Card": "card",
        "E-Wallet": "ewallet",
        "Paylater": "Other",
        "Bank Transfer": "Other",
        "Recharge": "Other",
        "Other": "Other",
        "iPay88": "Other",
    }

    return payment_mapping.get(payment_method, "Other")


def find_tax_percentage(transaction):
    amount = (
        transaction.debit_amount
        - transaction.adjustements_amount
        - transaction.rounding
        if transaction.debit_amount != 0
        else transaction.credit_amount
        - transaction.adjustements_amount
        - transaction.rounding
    )
    sum = transaction.sum
    for item in transaction.items:
        if item["item_type"] == "Tax":
            # Use regex to extract the percentage from itemName
            match = re.search(r"(\d+)%", item["item_name"])
            if match:
                return float(match.group(1)) / 100
            elif transaction.promotion_amount > 0:
                return round(
                    float((transaction.tax_amount / (amount - transaction.tax_amount))),
                    2,
                )
            else:
                return round(float((amount - sum) / sum), 2)
