from uuid import uuid4

from accounts.models import Shift
from django.db import models
from django.db.models import BooleanField

# Create transaction model which fields are below


class Transaction(models.Model):
    """

    # Formula for calculating credit amount
    room_price = sum + adjustment

    final_price = room_price - (room_price * promotion_percentage)

    credit_amount =  final_price + (final_price * service_charge_percentage)  + (final_price * tax_percentage)
    """

    STATUS_CHOICES = [
        ("Pending Payment", "Pending Payment"),
        ("Paid", "Paid"),
        ("Refund", "Refund"),
        ("Void", "Void"),
        ("Paid at Landside", "Paid at Landside"),
        ("Paid at Airside", "Paid at Airside"),
        ("Pay at Landside", "Pay at Landside"),
        ("Pay at Airside", "Pay at Airside"),
    ]

    transaction_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
    )
    booking = models.ForeignKey(
        "bookings.Booking", on_delete=models.CASCADE, verbose_name="Booking ID"
    )
    customer = models.ForeignKey(
        "guests.Customer",
        on_delete=models.CASCADE,
        verbose_name="Customer ID",
        null=True,
    )
    payment_type = models.ForeignKey(
        "payment.PaymentType",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name="Payment Type ID",
    )
    pan = models.CharField(max_length=20, null=True, blank=True)
    currency = models.ForeignKey(
        "currency.Currency", on_delete=models.CASCADE, verbose_name="Currency ID"
    )
    shift = models.ForeignKey(
        Shift, on_delete=models.CASCADE, verbose_name="Shift ID", null=True
    )
    invoice_no = models.CharField(max_length=20, unique=True, verbose_name="Invoice No")
    payment_details = models.TextField(verbose_name="Payment Details")
    sum = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Sum")
    tax_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="Tax Amount"
    )
    service_charge_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="Service Charge Amount",
        default=0.0,
    )
    adjustments = models.CharField(max_length=50, null=True, blank=True)
    adjustements_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    promotion_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )

    # Credit = adjustment + sum - promotion
    # Credit is the amount that is credited to the customer
    credit_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="Credit Amount"
    )
    debit_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="Debit Amount"
    )
    transaction_datetime = models.DateTimeField(
        null=True, verbose_name="Transaction Datetime"
    )
    rounding = models.DecimalField(
        max_digits=10, decimal_places=4, verbose_name="Rounding", default=0.0
    )
    transaction_status = models.CharField(
        max_length=50, choices=STATUS_CHOICES, verbose_name="Transaction Status"
    )
    is_latest = models.BooleanField(verbose_name="Is Latest")
    is_room_booking = models.BooleanField(verbose_name="Is Booking", default=False)

    items = models.JSONField(null=True, verbose_name="Items")

    payment_reference = models.CharField(
        max_length=100,
        verbose_name="Payment Reference",
        help_text="to store credit card OTP or payment site OTP",
        null=True,
        blank=True,
    )

    payment_remarks = models.CharField(
        max_length=1000,
        verbose_name="Payment Remarks",
        help_text="to let audit team dump in notes",
        null=True,
        blank=True,
    )
    refunded = BooleanField(default=False)

    guest_given = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="Guest Given",
        null=True,
        blank=True,
    )
    guest_change = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="Guest Change",
        null=True,
        blank=True,
    )

    @property
    def booking_can_be_confirmed(self):
        return self.sum >= self.debit_amount

    def __str__(self):
        return f"Transaction - {self.transaction_id}"


class RefundForm(models.Model):
    id = models.AutoField(primary_key=True)
    transaction = models.ForeignKey(
        Transaction, on_delete=models.CASCADE, verbose_name="Transaction ID"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    refund_form = models.CharField()
    status_datetime = models.DateTimeField()
    staff = models.ForeignKey("accounts.Account", on_delete=models.CASCADE)
