<!DOCTYPE html>
<html lang="en">

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
          crossorigin="anonymous"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Baskerville&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Octava:wght@400;700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Klainy&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Equip+Condensed:400,700&display=swap" />


    <style>

      .container{
        width: fit-content;
        max-width: 75mm;
      }
      .adjust{
        width:400px
      }

        p {
            white-space: nowrap;
            line-height: 1.15;
        }
        .col-12 p {
            font-size: 9 ;
            font-family: Arial;
            margin-bottom: 5px; /* Adjust the margin-bottom value to shorten the distance */
        }
        .custom-padding{
            padding: 5px; 
        }
        .table{            
            border-bottom: 0px solid transparent;

            border-collapse: collapse;
            border-spacing: 0;

            table-layout: fixed;
            width: 100%; /* Ensure tables take up full width of their container */
        }
        .table >thead{
          font-size: 12px;
        }
        .table>tbody{
            font-size: 15px ;
            font-family: Arial;
            border-bottom: 1px solid transparent;
            white-space: nowrap;

        }
        .table>tbody>tr>td{
            padding: 0;
            font-size: 12px
            
        }
        .table > tfoot > tr > td:nth-child(2) {
            font-size: 14px;
            font-weight: bold;
            justify-content: center;
            align-items: center;

        }

        .space {
            margin-top: 40px;
        }

        .row .col-8 .row > div {
            margin-bottom: 5px;
        }

        .table-container {
            margin-top: 20px;
            size: 12px;
            width: 70mm;
        }
        .left{
          text-align: left;
        }
        .custom-margin {
        margin-bottom: -10px;
        
    }
    .align-right {
        text-align: right;
    }

    .dotted-line {
            border-top: 1px dotted #000;
        }
    </style>
</head>

<body style="text-align: left; max-width: 75mm; width: fit-content; padding: 37px;">
<div class="bg-danger" style="text-align: center; max-width: 75mm; width: fit-content; border: 5px">
    <div class="row">
        <div class="col-12">
            <h1 style="white-space: nowrap;">Capsule Transit</h1>
            
            <h1 style="white-space: nowrap;">{{lot_number}}</h1>
        </div>
    </div>

    <div class="adjust" style="margin-top: 15px; margin-bottom: 15px;">
        <div class="col-12" style="max-width: 70mm">
            <p style="white-space: normal; width: 100%">{{lot_address}}</p>
            <p>Reg. {{lot_reg}}</p>
            <p>SST No. {{lot_sst}}</p>
            <p>Tel: {{lot_tel}}</p>
        </div>
    </div>
    {% if is_room_booking %}

    <div style="display: inline-block; margin-top: 30px; font-size: 16px; font-family: 'Courier New'; font-weight: bold; white-space: nowrap;
    ">
        <span style="border-top: 1px dotted black; padding-top: 20px;">BOOKING SLIP #:</span>
        {{ invoice_no }}
    </div>
    <div class="col-12" style="padding-top: 15px;">
        <p>BOOKING DATE: {{booking_date}}</p>
        <p>BOOKING SOURCE: {{platform}}</p>
        <p>GUEST: {{guest}}</p>
        <p>ROOM NO: {{room_code}}</p>
        <p>LOCKER NO: {{locker_no}}</p>
        <p>CHECK IN: {{check_in}}</p>
        <p>INTERVAL: {{interval}} hours</p>
        <p>CHECK OUT: {{check_out}}</p>

    </div>
    <div>

        
        <div class="table-container adjust">
            <table class="table">
                <tbody style="border-bottom: 1px solid transparent;">
                    {% for item in item_details %}
                        {% if item.category == "ROOM_SALES" or item.category == "ADJUSTMENT_SALES" %}
                            <tr >
                                <td >{{ item.item_type }}</td>
                                <td></td>
                                <td><div style="display: inline-block;  width: fit-content;">RM {{ item.price }}</div></td>
                            </tr>                            
                        {% endif %}
                    {% endfor %}
                    <tr>
                        <td>ROOMS TOTAL:</td>
                        <td></td>
                        <td><div style="border-top: 1px dotted black;">RM {{ room_sales_amount }}</div></td>
                    </tr>
                    {% if promotion_amount > 0 %}
                        <tr>
                            <td>PROMOTION:</td>
                            <td></td>
                            <td>-RM {{ promotion_amount }}</td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td>SST</td>
                        <td></td>
                        <td>RM {{sst}}</td>
                    </tr>
                    {% if rounding > 0 %}
                        <tr>
                            <td>ROUNDING:</td>
                            <td></td>
                            {% if rounding < 0 %}
                                <td>-RM {{ rounding|floatformat:2 }}</td>
                            {% else %}
                                <td>RM {{ rounding|floatformat:2 }}</td>
                            {% endif %}
                        </tr>
                    {% endif %}
                    <tr>
                        <td style="font-weight: bold; font-size: larger;">Total</td>
                        <td></td>
                        <td></td>
                        <td style="font-weight: bold;font-size: larger;">
                            RM {{debit_amount}}
                        </td>
                    </tr>
                </tbody>

            </table>
            <table class="table"><tbody>
                <tr>
                    <td style="padding-top: 20px;">Payment Date</td>
                    <td></td>

                    <td style="padding-top: 20px;">{{payment_date}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>{{payment_type}}</td>
                    <td></td>

                    <td>RM {{paid_amount}}</td>
                    <td></td>

                </tr>
                <tr>
                    <td>Change</td>
                    <td></td>

                    <td>RM {{change_amount}}</td>
                    <td></td>

                </tr>
            </tbody></table>
            
        </div>
        </div>
        <div style="text-align: center;margin-top: 100px;">{{payment_date}}</div>
        <div style="text-align: center;">CAPSULE TRANSIT KLIA2</div>

        {%else%}
        <div class="table-container">
            <table class="table" >
            <div style="font-weight: bold; font-size: large;">RECEIPT #: {{invoice_no}}
            </div>
                <thead>
                    <td >Item</td>
                    <td >Qty</td>
                    <td>Price(RM)</td>
                    <td>Total(RM)</td>
                </thead>
                <tbody>
                    {% for item in item_details %}
                        {% if item.category != "SERVICE_CHARGE" and item.category != "TAX" and item.category != "ROUNDING" %}
                            <tr>
                                <td style="width: 20px">{{ item.item_name }}</td>
                                <td style="width:10px">{{item.quantity}}</td>
                                <td>RM {{ item.price }}</td>
                                <td></td>
                            </tr>

                        {% endif %}
                    {% endfor %}
                </tbody>
                <tbody>
                    <tr>
                        <td>Total</td>
                        <td></td>
                        <td></td>
                        <td>RM {{debit_amount}}</td>
                    </tr>
                    <tr>
                        <td>Gross Payment</td>
                        <td></td>
                        <td></td>
                        <td>RM {{sum}}</td>
                    </tr>
                    <tr>
                        <td>SST</td>
                        <td></td>
                        <td></td>
                        <td>RM {{sst}}</td>
                    </tr>
                    <tr>
                        <td><div style="font-weight: bold; font-size: larger;">Total Payment</div></td>
                        <td></td>
                        <td></td>
                        <td>RM {{debit_amount}}</td>
                    </tr>
                    <tr>
                        <td>Payment Date:</td>
                        <td></td>
                        <td></td>

                        <td class="align-right" style="white-space: nowrap; direction: rtl;">
                            {{ payment_date|default:"0" }}
                        </td>
                        
                    </tr>
                    <tr>
                        <td>{{payment_type}}</td>
                        <td></td>
                        <td></td>

                        <td class="align-right">RM {{paid_amount|default:"0" }}</td>
                    </tr>
                    <tr>
                        <td>Change</td>
                        <td></td>
                        <td></td>

                        <td class="align-right">RM {{ change_amount|default:"0" }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

        {%endif%}

</div>
</body>

</html>
