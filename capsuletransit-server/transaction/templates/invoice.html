<!DOCTYPE html>
<html lang="en">
  <head>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
      crossorigin="anonymous"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Baskerville&display=swap"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Octava:wght@400;700&display=swap"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Klainy&display=swap"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Equip+Condensed:400,700&display=swap"
    />

    <style>
      .small-note {
        font-size: 8px;
        margin-left: 100px;
        margin-top: 100px;
      }

      .invoice h2 {
        font-family: "Baskerville", serif;
        font-weight: bold;
      }
      .location-name h3 {
        font-family: "Octava", sans-serif;
        font-weight: 3000;
        margin-bottom: 0px;
      }

      .container {
        min-width: 1000px;
        max-width: fit-content;
      }
      p {
        font-size: 14px !important;
        font-family: "Equip Condensed", sans-serif;
        font-weight: 800;
        line-height: 1.2;
      }

      h3 {
        font-family: "Klainy", sans-serif;
        font-size: 15px !important;
        font-weight: 1000;
        margin-bottom: 0px;
      }
      .table > thead {
        font-size: 13px;
      }
      .table > tbody {
        font-size: 14px !important;
        font-family: "Equip Condensed", sans-serif;
        font-weight: 800;
        line-height: 2;
      }
      .table > tfoot > tr > td:nth-child(2) {
        font-size: 14px;
        text-align: right;
        font-weight: bold;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
      }

      .space {
        margin-top: 40px;
      }

      .row .col-8 .row > div {
        margin-bottom: 5px;
      }

      .table-container {
        margin-top: -60px;
        size: 12px;
      }
      .left {
        text-align: left;
      }
      .custom-margin {
        margin-bottom: -10px;
      }

      .item-price {
        text-align: end;
      }
    </style>
  </head>

  <body>
    {% load static %}
    <div class="container container">
      <div class="row">
        <div class="col-8 space">
          <h1>
            <img src="data:image/svg+xml;base64,{{ capsuleLogo }}" alt="logo" />
            Capsule Transit
          </h1>
        </div>
        <div class="col-4 space invoice">
          <h2><bold>INVOICE</bold></h2>
        </div>
      </div>

      <div class="row space">
        <div class="col-8">
          <div class="row">
            <div class="col-12 booking-info">
              <h3>Billed to :</h3>
              <p>{{ billedTo }}</p>
            </div>
            <div class="col-6">
              <h3>Invoice No :</h3>
              <p>{{ invoiceNo }}</p>
            </div>
            <div class="col-6">
              <h3>Invoice Date :</h3>
              <p>{{ invoiceDate }}</p>
            </div>
            <div class="col-6">
              <h3>Booking No :</h3>
              <p>{{ bookingNo }}</p>
            </div>
            <div class="col-6">
              <h3>Check In</h3>
              <p>{{ checkIn }}</p>
            </div>
            <div class="col-6">
              <h3>Booking Source</h3>
              <p>{{ bookingSource }}</p>
            </div>
            <div class="col-6">
              <h3>Check Out</h3>
              <p>{{ checkOut }}</p>
            </div>
          </div>
          <div class="table-container">
            <hr />
            <table class="table">
              <thead>
                <hr />
                <tr class="text-center">
                  <td><strong>Date</strong></td>
                  <td class="left"><strong>Description</strong></td>
                  <th>
                    <strong
                      >Charges<br />(Incl.&nbsp;of&nbsp; {{ sstPercentage }}
                      &nbsp;SST)</strong
                    >
                  </th>
                  <td><strong>Payment</strong></td>
                </tr>
              </thead>
              <tbody>
                {% for item in items %} {% if item.category == "ROOM_SALES" %}
                <tr>
                  <td>{{ invoiceDate }}</td>
                  <td>
                    Capsule: {{ item.duration }} hours<br />{{ item.itemName }}
                    - {{ item.category }}
                  </td>
                  <td class="item-price">{{item.price}}</td>
                  <td></td>
                </tr>
                {% endif %} {% if item.category == "ADJUSTMENT_SALES" %}
                <tr>
                  <td>{{ invoiceDate }}</td>
                  <td>Adjustment - {{ item.category }}</td>
                  <td class="item-price">{{item.price|floatformat:2}}</td>
                  <td></td>
                </tr>
                {% endif %} {% endfor %}
              </tbody>
              {% comment %}
              <tbody>
                <tr>
                  <td>{{ item.date }}</td>
                  <td>Total Charges</td>
                  <td>RM {{ debitAmount }}</td>
                </tr>

                <tr>
                  <td>{{ item.date }}</td>
                  <td>
                    Payment Receive <br />
                    {{payment.paymentType}}
                  </td>
                  <td></td>
                  <td>RM {{ debitAmount }}</td>
                </tr>
              </tbody>
              {% endcomment %}
              <tfoot>
                <tr>
                  <td></td>
                  <td>Gross Charges (Excl. SST) (RM)</td>
                  <td class="item-price">{{ sumAmount }}</td>
                  <td></td>
                </tr>
                <tr>
                  <td></td>
                  <td>SST {{ sstPercentage }} (RM)</td>
                  <td class="item-price">{{ taxAmount }}</td>
                  <td></td>
                </tr>
                {% if rounding != 0 %}
                <tr>
                  <td></td>
                  <td>Rounding (RM)</td>
                  <td class="item-price">{{ rounding }}</td>
                  <td></td>
                </tr>
                {% endif%}
                <tr>
                  <td></td>
                  <td>Total Charges (RM)</td>
                  <td class="item-price">{{ totalCharges }}</td>
                  <td></td>
                </tr>
                <tr>
                  <td></td>
                  <td>Paid Amount (RM)</td>
                  <td></td>
                  <td class="item-price">{{ debitAmount }}</td>
                </tr>
                <tr>
                  <td></td>
                  <td>Outstanding Balance (RM)</td>
                  <td class="item-price">{{ creditAmount }}</td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <div class="col-4">
          <div class="hotel-basic-info location-name">
            <h3>Capsule Transit</h3>
            <p>{{ capsuleHotelLocation }}</p>
          </div>
          <div class="companyInfo">
            <p>{{companyLegalName}}<br />SST ID: {{ companySSTID }}</p>
          </div>
          <div class="fulladdressInfo">
            <p>{{ fullAddressInfo }}</p>
          </div>
          <div class="contactInfo custom-margin">
            <p>{{ contactInfo }}</p>
          </div>

          <div>
            <img src="data:image/png+xml;base64,{{ chgLogo }}" alt="chg" />
          </div>
        </div>
      </div>
      <div class="small-note">
        <p>* This is a computer generated receipt; no signature is required</p>
        <p>* Above charges are in Malaysian Ringgit (RM)</p>
      </div>
    </div>
  </body>
</html>
