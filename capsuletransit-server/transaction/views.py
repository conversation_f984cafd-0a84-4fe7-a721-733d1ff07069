import json
import os
from datetime import datetime, time, timedelta
from json import JSONDecodeError

import requests
from accounts.utils import get_current_shift
from bookings.models import Booking, BookingStatus, RoomBooking
from drf_yasg import openapi
from django.db import transaction as tr
from django.utils import timezone
import pytz
import copy

from azureblobservice import AzureBlobStorage
from django.db import transaction

from transaction.models import *
from transaction.services.transaction import (
    calculate_advanced_detalied_data,
    calculate_advanced_summary_data,
    calculate_transaction_detailed,
    calculate_transaction_summary,
    calculate_z_report_klia_paymentfile,
    calculate_z_report_klia_salesfile,
    calculate_z_report_gateway_detailed,
    calculate_z_report_gateway_summary,
    calculate_z_report_mahb,
    calculate_z_report_mahb_grouping,
    create_new_pending_transaction,
    convert_to_report_format,
    generate_invoice,
    generate_receipt,
    get_ledger_dynamic_filter,
    group_by_actual_check_in,
    group_by_booking,
    group_by_booking_duration,
    group_by_booking_platform,
    group_by_checkin_date,
    group_by_date,
    group_by_payment_method,
    group_by_payment_type,
    group_by_promotion,
    group_by_room_type,
    group_by_transaction,
    update_status,
)
from bookings.services.booking import (
    adjust_room_duration,
    calculate_overstay_fee,
    calculate_overstay_hour,
    create_booking_for_pos_transaction,
)
from constant.enums import TransactionItemCategory
from django.db import transaction as tr
from django.db.models import Case, Prefetch, Q, When, F, Sum
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from rest_framework.parsers import MultiPartParser
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.authentication import JWTAuthentication
from rooms.utils import get_total_room
from transaction.models import *
from transaction.serializer import (
    AdvancedDetailedSerializer,
    AdvancedSummaryGroupByBooking,
    AdvancedSummaryGroupByDate,
    AdvancedSummaryGroupByTransaction,
    ChangePaymentDetails,
    CollectionSummarySerializer,
    LedgerBookingPlatformDetailedSerializer,
    LedgerDynamicFilterSerializer,
    LedgerPaymentMethodDetailedSerializer,
    LedgerPaymentTypeDetailedSerializer,
    LedgerPromotionDetailedSerializer,
    LedgerRoomTypeDetailedSerializer,
    MAHBSummaryZReportSerializer,
    MAHBZReportSerializer,
    OverstayPoaSerializer,
    PayPendingTransactionSerializer,
    RefundDetailsSerializer,
    RefundSerializer,
    RemoveTransactionItem,
    ReportOverviewSerializer,
    TransactionDetaild,
    TransactionDetailedGroupByTransaction,
    TransactionExtraInfoSerializer,
    TransactionReadOnlySerializer,
    TransactionSerializer,
    TransactionSummaryGroupByBooking,
    TransactionSummaryGroupByBookingDuration,
    TransactionSummaryGroupByBookingPlatform,
    TransactionSummaryGroupByPaymentMethod,
    TransactionSummaryGroupByPromotion,
    TransactionSummaryGroupBypaymentType,
    TransactionSummaryGroupByRoomType,
    TransactionWriteSerializer,
    UpdatePendingTransactionSerializer,
    UpdateTransactionStatusSerializer,
    ZReportGatewayPaymentSerializer,
    ZReportGatewaySalesSerializer,
    ZReportKLIADetailedSerializer,
    ZReportKLIASummarySerializer,
)
from transaction.services.transaction import (
    calculate_transaction_detailed,
    calculate_transaction_summary,
    calculate_z_report_klia_paymentfile,
    calculate_z_report_klia_salesfile,
    calculate_z_report_gateway_detailed,
    calculate_z_report_gateway_summary,
    create_new_pending_transaction,
    generate_invoice,
    generate_invoice_context,
    group_by_booking,
    group_by_booking_duration,
    group_by_booking_platform,
    group_by_date,
    group_by_payment_type,
    group_by_room_type,
    group_by_transaction,
    update_status,
)
from transaction.utils import generate_invoice_no
from transaction.z_report import z_report, z_report_txt
from transaction.report import MockRequest, send_report
from transaction import exceptions
import dotenv
from bookings import exceptions as booking_exceptions
from lot.models import Lot

dotenv.load_dotenv()


class TransactionViewSet(viewsets.ModelViewSet):
    queryset = (
        Transaction.objects.all()
        .prefetch_related("customer", "payment_type")
        .order_by("transaction_status")
    )
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = TransactionSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def list(self, request):

        qs = self.queryset

        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        group_by = request.GET.get("groupBy", None)
        report_type = request.GET.get("reportType", None)
        versions = request.GET.get("versions", None)
        booking_status = request.GET.get("bookingStatus", None)
        booking_source = request.GET.get("bookingSource", None)
        payment_type = request.GET.get("paymentType", None)
        is_pos_history = request.GET.get("isPOSHistory", None)

        try:
            if (
                report_type == "sales_and_collections"
                and not start_date_time.strip()
                and not end_date_time.strip()
            ):
                raise Exception(
                    "Make sure start date and end date is selected for Sales and Collection report"
                )

            qs = qs.filter(booking__lot_id=request.user.lot_id)

            if is_pos_history is not None:
                qs = qs.filter(is_room_booking=False)

            # Filter by date-time range
            if start_date_time and end_date_time:
                # start_date = datetime.fromtimestamp(int(start_date_time))
                # end_date = datetime.fromtimestamp(int(end_date_time))
                # start_date = timezone.make_aware(
                #     start_date, timezone.get_current_timezone()
                # )
                # end_date = timezone.make_aware(
                #     end_date, timezone.get_current_timezone()
                # )

                # start_date_time = start_date
                # end_date_time = end_date

                # qs = qs.filter(transaction_datetime__range=(start_date, end_date))

                """
                if sales report type

                collections dont have since the date time filter
                is filtering by transaction datetime by default
                """
                if report_type and "sales" in report_type:
                    refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & Q(transaction_status="Refund")
                        & Q(
                            transaction_datetime__range=(start_date_time, end_date_time)
                        )
                    )

                    # Filter for non-Refund transactions
                    non_refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                        & (
                            Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=False,
                                booking__room_bookings__actual_checkin_date_time__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                            | Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=True,
                                booking__booking_status__check_in_datetime__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                        )
                    )

                    no_show_qs = qs.filter(
                        Q(booking__booking_status__booking_status="No Show")
                        & ~Q(transaction_status="Refund")
                        & Q(
                            booking__booking_status__check_in_datetime__range=(
                                start_date_time,
                                end_date_time,
                            )
                        )
                    )

                    qs = refund_qs | non_refund_qs | no_show_qs

                if report_type == "collections":
                    qs = qs.filter(
                        transaction_datetime__range=(start_date_time, end_date_time)
                    )

                serializer = TransactionReadOnlySerializer(qs, many=True)

            serializer = TransactionSerializer(qs, many=True)

            if report_type and report_type == "sales_and_collections":
                qs = qs.filter(
                    Q(transaction_status="Paid") | Q(transaction_status="Refund")
                )
                serializer = TransactionReadOnlySerializer(qs, many=True)

            # Filter by booking status
            if booking_status:
                splited = booking_status.split(":")
                qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & Q(booking__booking_status__booking_status__in=splited)
                )

            if booking_source:
                splited_booking_source = booking_source.split(":")
                qs = qs.filter(booking__platform__platform__in=splited_booking_source)

            if payment_type:
                splitted_payment_type = payment_type.split(":")
                qs = qs.filter(payment_type__payment_type__in=splitted_payment_type)

            if versions and versions == "summary":

                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                qs = qs.prefetch_related(latest_booking_status)

                if group_by:
                    if group_by == "booking":
                        qs = group_by_booking(qs=qs)
                        qs = calculate_transaction_summary(
                            qs=qs, report_type=report_type
                        )
                        serializer = TransactionSummaryGroupByBooking(qs, many=True)

                    if group_by == "payment-type":
                        qs = group_by_payment_type(qs=qs)
                        qs = calculate_transaction_summary(
                            qs=qs, report_type=report_type
                        )
                        serializer = TransactionSummaryGroupBypaymentType(qs, many=True)

                    if group_by == "booking-platform":
                        qs = group_by_booking_platform(qs=qs)
                        qs = calculate_transaction_summary(
                            qs=qs, report_type=report_type
                        )
                        serializer = TransactionSummaryGroupByBookingPlatform(
                            qs, many=True
                        )

                    if group_by == "booking-duration":
                        qs = group_by_booking_duration(qs=qs)
                        qs = calculate_transaction_summary(
                            qs=qs, report_type=report_type
                        )
                        serializer = TransactionSummaryGroupByBookingDuration(
                            qs, many=True
                        )

                    if group_by == "room-type":
                        qs = group_by_room_type(qs=qs)
                        qs = calculate_transaction_summary(
                            qs=qs, report_type=report_type
                        )
                        serializer = TransactionSummaryGroupByRoomType(qs, many=True)

            if versions and versions == "detailed":
                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                room_bookings = Prefetch(
                    "booking__room_bookings",
                    queryset=RoomBooking.objects.all(),
                    to_attr="room_booking_list",
                )
                qs = qs.prefetch_related(latest_booking_status, room_bookings)

                qs = qs.distinct()

                if group_by:
                    if group_by == "booking":
                        qs = group_by_booking(qs=qs)
                        qs = calculate_transaction_detailed(
                            qs=qs,
                            group_by=group_by,
                            report_type=report_type,
                            start_date_time=start_date_time,
                            end_date_time=end_date_time,
                        )
                        serializer = TransactionDetaild(qs, many=True)

                    if group_by == "transaction":
                        qs = group_by_transaction(qs=qs)
                        qs = calculate_transaction_detailed(
                            qs=qs,
                            group_by=group_by,
                            report_type=report_type,
                            start_date_time=start_date_time,
                            end_date_time=end_date_time,
                        )
                        serializer = TransactionDetailedGroupByTransaction(
                            qs, many=True
                        )

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def create(self, request):
        serializer = TransactionWriteSerializer(data=request.data)

        try:
            with tr.atomic():

                if not serializer.is_valid():
                    return JsonResponse(
                        serializer.errors, status=status.HTTP_400_BAD_REQUEST
                    )

                validated_data = serializer.validated_data

                added_hour = validated_data["added_hour"]
                settings_name = validated_data["settings_name"]

                try:
                    rounding = validated_data["rounding"]
                except:
                    rounding = 0

                try:
                    in_house_guest = validated_data["in_house_guest"]
                except:
                    in_house_guest = True

                try:
                    if in_house_guest:
                        booking = Booking.objects.get(pk=validated_data["booking"])
                except:
                    return Response(
                        {"status": "failed", "message": "booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                # if settings_name == "Add 1 hour" and validated_data["is_room_booking"]:
                #     if added_hour == "":
                #         return Response(
                #             {"detail": "please input added hour"},
                #             status=status.HTTP_400_BAD_REQUEST,
                #         )

                #     if not adjust_room_duration(
                #         added_hour=int(added_hour), booking_id=validated_data["booking"]
                #     ):
                #         return Response(
                #             {"detail": "room not available"},
                #             status=status.HTTP_403_FORBIDDEN,
                #         )

                shift = get_current_shift(account_id=validated_data["account_id"])

                if in_house_guest:
                    transaction_date = timezone.now()
                    invoice_no = generate_invoice_no(
                        booking_no=booking.booking_no, transaction_date=transaction_date
                    )

                    new_transaction = Transaction.objects.create(
                        booking_id=booking.pk,
                        customer_id=(
                            validated_data["customer"]
                            if validated_data["customer"]
                            else None
                        ),
                        payment_type_id=validated_data["payment_type"],
                        currency_id=validated_data["currency"],
                        shift_id=shift.pk,
                        invoice_no=invoice_no,
                        payment_details=validated_data["payment_details"],
                        sum=validated_data["sum"],
                        tax_amount=validated_data["tax_amount"],
                        service_charge_amount=validated_data["service_charge_amount"],
                        adjustments=validated_data["adjustments"],
                        adjustements_amount=validated_data["adjustements_amount"],
                        promotion_amount=validated_data["promotion_amount"],
                        credit_amount=validated_data["credit_amount"],
                        debit_amount=validated_data["debit_amount"],
                        transaction_datetime=(
                            transaction_date
                            if validated_data["transaction_status"] == "Paid"
                            else None
                        ),
                        rounding=rounding,
                        transaction_status=validated_data["transaction_status"],
                        is_latest=True,
                        is_room_booking=validated_data["is_room_booking"],
                        items=validated_data["items"],
                        guest_given=(
                            validated_data["guest_given"]
                            if validated_data["guest_given"]
                            else 0
                        ),
                        guest_change=(
                            validated_data["guest_change"]
                            if validated_data["guest_change"]
                            else 0
                        ),
                        pan=validated_data["pan"] if validated_data["pan"] else None,
                        payment_remarks=(
                            validated_data["payment_remarks"]
                            if validated_data["payment_remarks"]
                            else None
                        ),
                        payment_reference=(
                            validated_data["payment_reference"]
                            if validated_data["payment_reference"]
                            else None
                        ),
                    )

                if not in_house_guest:
                    new_booking: Booking = create_booking_for_pos_transaction(
                        item_sum=validated_data["sum"],
                        booking_status="Check In",
                        user=request.user,
                    )

                    transaction_date = timezone.now()
                    invoice_no = generate_invoice_no(
                        booking_no=new_booking.booking_no,
                        transaction_date=transaction_date,
                    )

                    new_transaction = Transaction.objects.create(
                        booking_id=new_booking.pk,
                        customer_id=new_booking.customer_staying.pk,
                        payment_type_id=validated_data["payment_type"],
                        currency_id=validated_data["currency"],
                        shift_id=shift.pk,
                        invoice_no=invoice_no,
                        payment_details=validated_data["payment_details"],
                        sum=validated_data["sum"],
                        tax_amount=validated_data["tax_amount"],
                        service_charge_amount=validated_data["service_charge_amount"],
                        adjustments=validated_data["adjustments"],
                        adjustements_amount=validated_data["adjustements_amount"],
                        promotion_amount=validated_data["promotion_amount"],
                        credit_amount=validated_data["credit_amount"],
                        debit_amount=validated_data["debit_amount"],
                        transaction_datetime=(
                            new_booking.booking_made_datetime
                            if validated_data["transaction_status"] == "Paid"
                            else None
                        ),
                        rounding=rounding,
                        transaction_status=validated_data["transaction_status"],
                        is_latest=True,
                        is_room_booking=validated_data["is_room_booking"],
                        items=validated_data["items"],
                        guest_given=(
                            validated_data["guest_given"]
                            if validated_data["guest_given"]
                            else 0
                        ),
                        guest_change=(
                            validated_data["guest_change"]
                            if validated_data["guest_change"]
                            else 0
                        ),
                        pan=validated_data["pan"] if validated_data["pan"] else None,
                        payment_remarks=(
                            validated_data["payment_remarks"]
                            if validated_data["payment_remarks"]
                            else None
                        ),
                        payment_reference=(
                            validated_data["payment_reference"]
                            if validated_data["payment_reference"]
                            else None
                        ),
                    )

                return Response(
                    {
                        "status": "success",
                        "message": "new transaction created",
                        "transaction_id": new_transaction.pk,
                    },
                    status=status.HTTP_201_CREATED,
                )

        except exceptions.UnableAddHour as e:
            return Response({"detail": str(e)}, status=status.HTTP_403_FORBIDDEN)

        except Exception as e:
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="get-by-booking-id/(?P<booking_id>[a-f0-9-]+)",
    )
    def get_transaction_by_booking_id(self, request, booking_id):
        # booking_id = request.query_params.get('bookingId', None)
        booking_id = booking_id
        if not booking_id:
            return Response(
                {"error": "booking_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            transactions = Transaction.objects.filter(booking_id=booking_id).annotate(
                cashier_name=F("shift__staffshift__staff__name"),
            )
            serializer = TransactionExtraInfoSerializer(transactions, many=True)
            return Response(serializer.data)
        except Transaction.DoesNotExist:
            return Response(
                {"error": "transaction not found"}, status=status.HTTP_404_NOT_FOUND
            )

    @action(
        detail=False,
        methods=["GET"],
        url_name="advanced",
        url_path="advanced",
    )
    def advanced_report_list(self, request):
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        as_at_date_time = request.GET.get("asAtDateTime", None)
        group_by = request.GET.get("groupBy", None)
        report_type = request.GET.get("reportType", None)
        versions = request.GET.get("versions", None)
        booking_status = request.GET.get("bookingStatus", None)
        booking_source = request.GET.get("bookingSource", None)
        payment_type = request.GET.get("paymentType", None)
        date_filter_by = request.GET.get("dateFilterBy", None)

        try:
            qs = self.queryset

            qs = qs.filter(booking__lot_id=request.user.lot_id)

            if start_date_time and end_date_time:

                if report_type == "used":
                    refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & Q(transaction_status="Refund")
                        & Q(
                            transaction_datetime__range=(start_date_time, end_date_time)
                        )
                    )

                    # Filter for non-Refund transactions
                    non_refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                        & Q(
                            booking__room_bookings__actual_checkin_date_time__isnull=False,
                            booking__room_bookings__actual_checkin_date_time__range=(
                                start_date_time,
                                end_date_time,
                            ),
                        )
                    )

                    qs = refund_qs | non_refund_qs

                if report_type == "collected" or report_type == "ledger":
                    qs = qs.filter(
                        transaction_datetime__range=(start_date_time, end_date_time)
                    )

                if date_filter_by and date_filter_by == "transactionDatetime":
                    qs = qs.filter(
                        transaction_datetime__range=(start_date_time, end_date_time)
                    )

                if date_filter_by and date_filter_by == "checkInDatetime":
                    refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & Q(transaction_status="Refund")
                        & Q(
                            transaction_datetime__range=(start_date_time, end_date_time)
                        )
                    )

                    # Filter for non-Refund transactions
                    non_refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                        & (
                            Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=False,
                                booking__room_bookings__actual_checkin_date_time__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                            | Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=True,
                                booking__booking_status__check_in_datetime__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                        )
                    )

                    qs = refund_qs | non_refund_qs

            # only get room booking transaction
            qs = qs.filter(is_room_booking=True)

            if report_type == "used":
                qs = qs.filter(
                    Q(transaction_datetime__isnull=False)
                    & Q(transaction_datetime__lt=start_date_time)
                    & Q(booking__room_bookings__actual_checkin_date_time__isnull=False)
                    & Q(
                        Q(booking__booking_status__is_latest=True)
                        & Q(
                            booking__booking_status__booking_status__in=[
                                "Check In",
                                "Check Out",
                                "Cancelled",
                            ]
                        )
                    )
                )

                qs = qs.order_by("booking__booking_status__check_in_datetime")

            if report_type == "collected":
                qs = qs.filter(
                    Q(transaction_datetime__isnull=False)
                    # & Q(booking__room_bookings__actual_checkin_date_time__isnull=True)
                    & Q(
                        Q(booking__booking_status__is_latest=True)
                        & Q(
                            booking__booking_status__check_in_datetime__gt=end_date_time
                        )
                        & Q(
                            Q(
                                booking__booking_status__booking_status__in=[
                                    "Confirm Booking",
                                    "No Show",
                                    "Cancelled",
                                    "Check Out",
                                ]
                            )
                            | Q(
                                booking__booking_status__booking_status__icontains="Transfer to"
                            )
                            | Q(
                                booking__booking_status__booking_status__icontains="Transfer from"
                            )
                        )
                    )
                )
                qs = qs.order_by("booking__booking_status__check_in_datetime")

            if report_type == "ledger":
                qs = qs.filter(
                    Q(transaction_datetime__isnull=False)
                    & Q(
                        Q(booking__booking_status__is_latest=True)
                        & Q(
                            booking__booking_status__check_in_datetime__gt=as_at_date_time
                        )
                        & Q(
                            Q(
                                booking__booking_status__booking_status__in=[
                                    "Check In",
                                    "Check Out",
                                    "Cancelled",
                                    "No Show",
                                    "Confirm Booking",
                                ]
                            )
                            | Q(
                                booking__booking_status__booking_status__icontains="Transfer to"
                            )
                            | Q(
                                booking__booking_status__booking_status__icontains="Transfer from"
                            )
                        )
                    )
                )
            serializer = TransactionSerializer(qs, many=True)

            # Filter by booking status
            if booking_status:
                splited = booking_status.split(":")
                qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & Q(booking__booking_status__booking_status__in=splited)
                )

            # filter by booking source
            if booking_source:
                splited_booking_source = booking_source.split(":")
                qs = qs.filter(booking__platform__platform__in=splited_booking_source)

            # filter by payment type
            if payment_type:
                splitted_payment_type = payment_type.split(":")
                qs = qs.filter(payment_type__payment_type__in=splitted_payment_type)

            if versions and versions == "summary":

                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                qs = qs.prefetch_related(latest_booking_status)

                if group_by and group_by == "transaction":
                    qs = group_by_transaction(qs=qs)
                    qs = calculate_advanced_summary_data(qs=qs, report_type=report_type)
                    serializer = AdvancedSummaryGroupByTransaction(qs, many=True)

                if group_by and group_by == "transaction_date":
                    qs = group_by_date(qs=qs)
                    qs = calculate_advanced_summary_data(qs=qs, report_type=report_type)
                    serializer = AdvancedSummaryGroupByDate(qs, many=True)

                if group_by and group_by == "check_in_datetime":
                    qs = group_by_checkin_date(qs=qs)
                    qs = calculate_advanced_summary_data(qs=qs, report_type=report_type)
                    qs = sorted(
                        qs,
                        key=lambda x: x["date"],
                        reverse=False,
                    )
                    serializer = AdvancedSummaryGroupByDate(qs, many=True)

                if group_by and group_by == "booking":
                    qs = group_by_booking(qs=qs)
                    qs = calculate_advanced_summary_data(qs=qs, report_type=report_type)
                    serializer = AdvancedSummaryGroupByBooking(qs, many=True)

            if versions and versions == "detailed":

                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                room_bookings = Prefetch(
                    "booking__room_bookings",
                    queryset=RoomBooking.objects.all(),
                    to_attr="room_booking_list",
                )
                qs = qs.prefetch_related(latest_booking_status, room_bookings)

                qs = qs.distinct()

                if group_by and group_by == "transaction":
                    qs = group_by_transaction(qs=qs)
                    qs = calculate_advanced_detalied_data(
                        qs=qs, report_type=report_type, group_by=group_by
                    )
                    serializer = AdvancedDetailedSerializer(qs, many=True)

                if group_by and group_by == "booking":
                    qs = group_by_booking(qs=qs)
                    qs = calculate_advanced_detalied_data(
                        qs=qs, report_type=report_type, group_by=group_by
                    )
                    serializer = AdvancedDetailedSerializer(qs, many=True)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(request_body=PayPendingTransactionSerializer)
    @action(
        detail=False,
        methods=["PATCH"],
        url_name="pay-pending-transaction",
        url_path="pay-pending-transaction",
    )
    def pay_pending_transaction(self, request):
        serializer = PayPendingTransactionSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with tr.atomic():
                validated_data = serializer.validated_data

                try:
                    rounding = validated_data["rounding"]
                except:
                    rounding = 0

                try:
                    transaction_obj = Transaction.objects.get(
                        pk=validated_data["transaction_id"]
                    )
                except:
                    return Response(
                        {"status": "failed", "message": "transaction not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                account_id = request.user.account_id

                shift = get_current_shift(account_id=account_id)

                transaction_obj.transaction_status = "Paid"
                transaction_obj.credit_amount = 0
                transaction_obj.debit_amount = validated_data["debit_amount"]
                transaction_obj.transaction_datetime = timezone.now()
                transaction_obj.payment_type_id = validated_data["payment_type_id"]
                transaction_obj.adjustements_amount = validated_data[
                    "adjustments_amount"
                ]
                transaction_obj.promotion_amount = validated_data["promotion_amount"]
                transaction_obj.service_charge_amount = validated_data[
                    "service_charge_amount"
                ]
                transaction_obj.shift = shift
                transaction_obj.tax_amount = validated_data["tax_amount"]
                transaction_obj.rounding = rounding
                transaction_obj.payment_details = validated_data["payment_details"]
                transaction_obj.items = validated_data["items"]
                transaction_obj.guest_given = validated_data["guest_given"]
                transaction_obj.guest_change = validated_data["guest_change"]

                transaction_obj.pan = validated_data["pan"]
                transaction_obj.payment_reference = validated_data["payment_reference"]
                transaction_obj.payment_remarks = validated_data["payment_remarks"]

                transaction_obj.save()

                return Response({"status": "success", "data": "paid"})

        except exceptions.UnableAddHour as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=UpdatePendingTransactionSerializer,
        responses={
            200: "success",
            400: "bad request",
            403: "not pending transaction",
            500: "internal server error",
        },
        operation_description="Update pending transaction",
    )
    @action(
        detail=False,
        methods=["PUT"],
        url_path="update-pending-transaction",
    )
    def update_pending_transaction(self, request):
        serializer = UpdatePendingTransactionSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with tr.atomic():
                validated_data = serializer.validated_data

                try:
                    transaction_obj = Transaction.objects.get(
                        pk=validated_data["transaction_id"]
                    )
                except:
                    return Response(
                        {"status": "failed", "message": "transaction not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                if transaction_obj.transaction_status != "Pending Payment":
                    return Response(
                        {
                            "status": "failed",
                            "message": "only pending transaction can be edited",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )

                try:
                    rounding = validated_data["rounding"]
                except:
                    rounding = 0

                transaction_obj.sum = validated_data["sum"]
                transaction_obj.credit_amount = validated_data["credit_amount"]
                transaction_obj.promotion_amount = validated_data["promotion_amount"]
                transaction_obj.service_charge_amount = validated_data[
                    "service_charge_amount"
                ]
                transaction_obj.tax_amount = validated_data["tax_amount"]
                transaction_obj.adjustements_amount = validated_data[
                    "adjustment_amount"
                ]
                transaction_obj.rounding = rounding
                transaction_obj.payment_details = validated_data["payment_details"]
                transaction_obj.items = validated_data["items"]

                transaction_obj.save()

            return Response(
                {"status": "success", "message": "edited"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="refund-transaction",
        parser_classes=[MultiPartParser],
    )
    def refund_transaction(self, request):
        serializer = RefundSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                transaction_obj = Transaction.objects.get(
                    pk=validated_data["transaction_id"]
                )
            except:
                return Response(
                    {"status": "failed", "message": "transaction not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if transaction_obj.refunded:
                return Response(
                    {
                        "status": "failed",
                        "message": "transaction has been refunded",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            if transaction_obj.transaction_status != "Paid":
                return Response(
                    {
                        "status": "failed",
                        "message": "only paid transaction can be refunded",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            with tr.atomic():

                transaction_date = timezone.now()
                invoice_no = generate_invoice_no(
                    booking_no=transaction_obj.booking.booking_no,
                    transaction_date=transaction_date,
                )

                new_items = []
                for item in transaction_obj.items:
                    item_copy = copy.deepcopy(item)
                    item_copy["price"] = -1 * float(item_copy["price"])
                    new_items.append(item_copy)

                new_payment_details = json.dumps(new_items)

                new_transaction = Transaction.objects.create(
                    booking_id=transaction_obj.booking_id,
                    customer_id=transaction_obj.customer_id,
                    payment_type_id=validated_data["payment_type_id"],
                    currency_id=transaction_obj.currency_id,
                    shift_id=transaction_obj.shift_id,
                    invoice_no=invoice_no,
                    payment_details=new_payment_details,
                    sum=-1 * transaction_obj.sum,
                    tax_amount=-1 * transaction_obj.tax_amount,
                    service_charge_amount=-1 * transaction_obj.service_charge_amount,
                    adjustments=transaction_obj.adjustments,
                    adjustements_amount=-1 * transaction_obj.adjustements_amount,
                    promotion_amount=-1 * transaction_obj.promotion_amount,
                    credit_amount=-1 * transaction_obj.credit_amount,
                    debit_amount=-1 * transaction_obj.debit_amount,
                    transaction_datetime=transaction_date,
                    rounding=-1 * transaction_obj.rounding,
                    transaction_status="Refund",
                    is_latest=True,
                    is_room_booking=transaction_obj.is_room_booking,
                    items=new_items,
                    guest_given=transaction_obj.guest_given,
                    guest_change=transaction_obj.guest_change,
                    pan=transaction_obj.pan,
                    payment_reference=transaction_obj.payment_reference,
                    payment_remarks=validated_data["reason"],
                )

                transaction_obj.refunded = True
                transaction_obj.save()

                azure_blob = AzureBlobStorage(
                    connection_string=os.environ["AZURE_BLOB_CONNECTION_STRING"],
                    container_name="refund-form",
                )

                azure_blob.upload_image(
                    image_data=validated_data["refund_form"],
                    image_name=f"{invoice_no}_refund_form",
                )

                image_url = azure_blob.get_image_url(
                    image_name=f"{invoice_no}_refund_form"
                )

                refund_form = RefundForm.objects.create(
                    transaction_id=new_transaction.pk,
                    status_datetime=timezone.now(),
                    staff_id=request.user.account_id,
                    refund_form=image_url,
                )

            return Response(
                {"status": "success", "message": "refunded"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="refund-detail/(?P<transaction_id>[a-f0-9-]+)",
    )
    def get_refund_detail(self, request, transaction_id):

        try:

            try:
                transaction_obj = Transaction.objects.get(pk=transaction_id)
            except:
                return Response(
                    {"status": "failed", "message": "transaction not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            try:
                refund_form_obj = RefundForm.objects.get(
                    transaction_id=transaction_obj.pk
                )
            except:
                return Response(
                    {"status": "failed", "message": "refund form not found"},
                    status=status.HTTP_204_NO_CONTENT,
                )

            remarks = transaction_obj.payment_remarks

            azure_blob = AzureBlobStorage(
                connection_string=os.environ["AZURE_BLOB_CONNECTION_STRING"],
                container_name="refund-form",
            )

            form_iamge_url = azure_blob.get_image_url(
                image_name=f"{transaction_obj.invoice_no}_refund_form"
            )

            details_data = {
                "remarks": remarks,
                "refunded_amount": -1 * transaction_obj.debit_amount,
                "payment_type": transaction_obj.payment_type.payment_type,
                "refund_form_url": form_iamge_url,
                "staff_name": refund_form_obj.staff.name,
            }

            return_serializer = RefundDetailsSerializer(details_data)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="void-transaction/(?P<transaction_id>[a-f0-9-]+)",
    )
    def void_transaction(self, request, transaction_id):

        try:
            transaction_obj = Transaction.objects.get(pk=transaction_id)
        except:
            return Response(
                {"status": "failed", "message": "transaction not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if transaction_obj.transaction_status != "Pending Payment":
            return Response(
                {
                    "status": "failed",
                    "message": "only pending transaction can be cancelled",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            with tr.atomic():
                # transaction_obj.sum= 0
                # transaction_obj.tax_amount= 0
                # transaction_obj.service_charge_amount= 0
                # transaction_obj.adjustments=0
                # transaction_obj.adjustements_amount=0
                # transaction_obj.promotion_amount=0
                # transaction_obj.credit_amount=0
                # transaction_obj.debit_amount=0
                # transaction_obj.rounding=0

                transaction_obj.transaction_status = "Void"

                transaction_obj.save()

            return Response(
                {"status": "success", "message": "void"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="delete-items",
    )
    def delete_transaction_item(self, request):
        serializer = RemoveTransactionItem(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            booking = Booking.objects.filter(pk=validated_data["booking_id"]).first()
            if not booking:
                return Response(
                    {"status": "failed", "message": "Booking not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            category = validated_data["category"]

            update_fail = False
            if category == TransactionItemCategory.ROOM_SALES:
                updated = booking.remove_room_booking(
                    room_code=validated_data["item_name"]
                )
                update_fail = False if updated else True

            if category == TransactionItemCategory.LOCKER_SALES:
                updated = booking.remove_locker_booking(
                    locker_code=validated_data["item_name"]
                )
                update_fail = False if updated else True

            if category == TransactionItemCategory.SHOWER_SALES:
                updated = booking.remove_shower_booking(
                    remove_count=validated_data["count"]
                )
                update_fail = False if updated else True

            if update_fail:
                return Response(
                    {"status": "failed", "message": "Failed to update"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            return Response(
                {"status": "success", "message": "item removed"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_name="payment-details",
        url_path="payment-details",
    )
    def change_payment_details(self, request):
        serializer = ChangePaymentDetails(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                transaction_obj = Transaction.objects.get(
                    pk=validated_data["transaction_id"]
                )
            except:
                return Response(
                    {"status": "failed", "message": "transaction not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            transaction_obj.payment_remarks = validated_data["payment_remarks"]
            transaction_obj.payment_reference = validated_data["payment_reference"]
            transaction_obj.pan = validated_data["pan"]
            transaction_obj.guest_given = validated_data["guest_given"]
            transaction_obj.guest_change = validated_data["guest_change"]
            transaction_obj.save()

            return Response(
                {"status": "success", "message": "changed"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=UpdateTransactionStatusSerializer,
        responses={200: "success", 400: "bad request", 500: "internal server error"},
        operation_description="Update transaction status API",
    )
    @action(
        detail=False,
        methods=["PATCH"],
        url_path="update-status",
    )
    def update_transaction_status(self, request):
        serializer = UpdateTransactionStatusSerializer(data=request.data)
        try:
            if serializer.is_valid():
                result = update_status(data=serializer)
                return Response({"status": "success", "data": result})
            else:
                return Response(
                    {
                        "status": "failed",
                        "msg": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False, methods=["POST"], url_path="overstay-poa", url_name="overstay-poa"
    )
    def create_overstay_poa(self, request):
        serializer = OverstayPoaSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():

                overstay_hour = calculate_overstay_hour(
                    booking_id=validated_data["booking_id"]
                )

                if not overstay_hour:
                    return Response(
                        {
                            "status": "failed",
                            "message": "make sure booking is exist and status is overstay",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # overstay_transaction_exist = Transaction.objects.select_for_update().filter(
                #     booking_id = validated_data['booking_id'],
                #     payment_remarks = 'Overstay charge'
                # ).exists()

                booking_transaction_list = (
                    Transaction.objects.select_for_update().filter(
                        booking_id=validated_data["booking_id"]
                    )
                )

                overstay_transaction_exist = False
                for transaction_obj in booking_transaction_list:
                    for item in transaction_obj.items:
                        if item["category"] == TransactionItemCategory.OVERSTAY:
                            overstay_transaction_exist = True
                            break

                    if overstay_transaction_exist:
                        break

                print(overstay_transaction_exist)

                if overstay_transaction_exist:
                    return Response(
                        {
                            "status": "failed",
                            "message": "Overstay POA already generated",
                        },
                        status=status.HTTP_409_CONFLICT,
                    )

                overstay_fee_info = calculate_overstay_fee(
                    hour=overstay_hour, booking_id=validated_data["booking_id"]
                )

                new_transaction = create_new_pending_transaction(
                    booking_id=validated_data["booking_id"],
                    transaction_details=overstay_fee_info["details"],
                    account_id=request.user.account_id,
                    sum=overstay_fee_info["amount"],
                    is_room_booking=True,
                    remarks="Overstay charge",
                )

            return Response(
                {
                    "status": "success",
                    "message": "Overstay fee calculated, please pay before check out",
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "startDateTime",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="start date for filtering",
            ),
            openapi.Parameter(
                "endDateTime",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="end date for filtering",
            ),
            openapi.Parameter(
                "reportType",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="either sales or collections",
            ),
        ],
        responses={200: ReportOverviewSerializer, 500: "server error"},
    )
    @action(
        detail=False,
        methods=["GET"],
        url_name="report-overview",
        url_path="report-overview",
    )
    def get_report_overview(self, request):

        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        report_type = request.GET.get("reportType", None)
        filter_lot = request.GET.get("filterLot", None)

        try:
            qs = self.queryset

            # if filter_lot is not None:
            qs = qs.filter(shift__staffshift__staff__lot=request.user.lot_id)

            if start_date_time and end_date_time:
                start_date = datetime.fromtimestamp(int(start_date_time))
                end_date = datetime.fromtimestamp(int(end_date_time))
                start_date = timezone.make_aware(
                    start_date, timezone.get_current_timezone()
                )
                end_date = timezone.make_aware(
                    end_date, timezone.get_current_timezone()
                )

                qs = qs.filter(transaction_datetime__range=(start_date, end_date))

                if report_type == "sales":
                    qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & Q(
                            booking__booking_status__check_in_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                    )

            if report_type == "collections":
                qs = qs.filter(transaction_status="Paid")

            total_sales = 0
            total_sales_after_tax = 0
            room_sales_after_tax = 0
            pos_sales_after_tax = 0

            total_tax = 0
            total_sales_wo_tax = 0
            room_sales_wo_tax = 0
            pos_sales_wo_tax = 0

            booking_ids = []
            for data in qs:
                transaction_sum = data.sum if data.sum else 0
                transaction_debit = data.debit_amount if data.debit_amount else 0
                adjustment_amount = (
                    data.adjustements_amount if data.adjustements_amount else 0
                )
                promotion_amount = data.promotion_amount if data.promotion_amount else 0
                tax_amount = data.tax_amount if data.tax_amount else 0
                service_cahrge_amount = (
                    data.service_charge_amount if data.service_charge_amount else 0
                )
                rounding = data.rounding if data.rounding else 0

                total_sales += transaction_sum
                total_sales_wo_tax += (
                    transaction_debit - tax_amount - service_cahrge_amount
                    if transaction_debit > 0
                    else 0
                )
                total_sales_after_tax += transaction_debit

                total_tax += tax_amount + service_cahrge_amount

                if data.is_room_booking:
                    room_sales_after_tax += transaction_debit
                    room_sales_wo_tax += (
                        transaction_debit - tax_amount - service_cahrge_amount
                        if transaction_debit > 0
                        else 0
                    )

                if not data.is_room_booking:
                    pos_sales_after_tax += transaction_debit
                    pos_sales_wo_tax += (
                        transaction_debit - tax_amount - service_cahrge_amount
                        if transaction_debit > 0
                        else 0
                    )

                if data.booking_id not in booking_ids:
                    booking_ids.append(data.booking_id)

            room_bookings = RoomBooking.objects.filter(booking_id__in=booking_ids)
            total_room_booking_count = room_bookings.count()

            total_room_count = get_total_room(not_archived=True)

            booked_room_ids = []
            for room_book in room_bookings:
                if room_book.room_id not in booked_room_ids:
                    booked_room_ids.append(room_book.room_id)

            occupany = (len(booked_room_ids) / total_room_count) * 100

            report_overview = [
                {
                    "left": {"name": "Total Sales", "value": total_sales},
                    "right": {"name": "Total Tax", "value": total_tax},
                },
                {
                    "left": {
                        "name": "Total Room Bookings Count",
                        "value": int(total_room_booking_count),
                    },
                    "right": {"name": "Occupany %", "value": occupany},
                },
                {
                    "left": {
                        "name": "Total Sales after Tax",
                        "value": total_sales_after_tax,
                    },
                    "right": {
                        "name": "Total Sales w/o Tax",
                        "value": total_sales_wo_tax,
                    },
                },
                {
                    "left": {
                        "name": "Room Sales after Tax",
                        "value": room_sales_after_tax,
                    },
                    "right": {"name": "Room Sales w/o Tax", "value": room_sales_wo_tax},
                },
                {
                    "left": {
                        "name": "POS Sales after Tax",
                        "value": pos_sales_after_tax,
                    },
                    "right": {"name": "POS Sales w/o Tax", "value": pos_sales_wo_tax},
                },
            ]

            serializer = ReportOverviewSerializer(report_overview, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False, methods=["GET"], url_name="get-z-report", url_path="get-z-report"
    )
    def get_z_report(self, request):

        qs = self.queryset.filter(booking__lot_id=request.user.lot_id)
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        report_type = request.GET.get("reportType", None)
        versions = request.GET.get("versions", None)

        qs = qs.exclude(
            payment_type_id__payment_type__in=["Complimentary", "Recovery Services"]
        )

        try:

            if start_date_time and end_date_time:
                qs = qs.exclude(transaction_status="Void")

                refund_qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & Q(transaction_status="Refund")
                    & Q(transaction_datetime__range=(start_date_time, end_date_time))
                )

                # Filter for non-Refund transactions
                non_refund_qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                    & (
                        Q(
                            booking__room_bookings__actual_checkin_date_time__isnull=False,
                            booking__room_bookings__actual_checkin_date_time__range=(
                                start_date_time,
                                end_date_time,
                            ),
                        )
                        | Q(
                            booking__room_bookings__actual_checkin_date_time__isnull=True,
                            booking__booking_status__check_in_datetime__range=(
                                start_date_time,
                                end_date_time,
                            ),
                        )
                    )
                )

                qs = refund_qs | non_refund_qs

            latest_booking_status = Prefetch(
                "booking__booking_status",
                queryset=BookingStatus.objects.filter(is_latest=True),
                to_attr="latest_booking_status",
            )

            if report_type == "KLIA/KLIA2":
                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                room_bookings = Prefetch(
                    "booking__room_bookings",
                    queryset=RoomBooking.objects.all(),
                    to_attr="room_booking_list",
                )
                # CODE BELOW IS DEPRECATED AS MAHB HAS A NEW FORMAT#
                # if versions == "salesFile":
                #     qs = qs.prefetch_related(latest_booking_status)
                #     qs = calculate_z_report_klia_salesfile(qs)
                #     serializer = ZReportGatewaySalesSerializer(data=qs, many=True)

                # if versions == "paymentFile":
                #     qs = qs.prefetch_related(latest_booking_status, room_bookings)
                #     qs = calculate_z_report_klia_paymentfile(qs)
                #     serializer = ZReportGatewayPaymentSerializer(data=qs, many=True)
                qs = qs.prefetch_related(latest_booking_status, room_bookings)
                qs = qs.distinct()
                qs = calculate_z_report_mahb(qs)

                if versions == "mahbSummary":
                    qs = calculate_z_report_mahb_grouping(qs)
                    serializer = MAHBSummaryZReportSerializer(data=qs, many=True)
                else:
                    serializer = MAHBZReportSerializer(data=qs, many=True)

            if report_type == "Gatewaymall":
                latest_booking_status = Prefetch(
                    "booking__booking_status",
                    queryset=BookingStatus.objects.filter(is_latest=True),
                    to_attr="latest_booking_status",
                )
                room_bookings = Prefetch(
                    "booking__room_bookings",
                    queryset=RoomBooking.objects.all(),
                    to_attr="room_booking_list",
                )
                if versions == "detailed":
                    qs = qs.prefetch_related(latest_booking_status, room_bookings)
                    qs = qs.distinct()
                    qs = calculate_z_report_gateway_detailed(qs)
                    serializer = ZReportKLIADetailedSerializer(data=qs, many=True)

                if versions == "summary":
                    qs = qs.prefetch_related(latest_booking_status, room_bookings)
                    qs = qs.distinct()
                    qs = group_by_actual_check_in(qs)
                    qs = calculate_z_report_gateway_summary(qs)
                    serializer = ZReportKLIASummarySerializer(data=qs, many=True)

            if serializer.is_valid():
                return Response(
                    {"status": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "error", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @swagger_auto_schema(
    #     request_body=PayPendingTransactionSerializer
    # )
    @action(
        detail=False,
        methods=["POST"],
        url_name="generate-z-report",
        url_path="generate-z-report",
    )
    def generate_z_report(self, request):
        generated_pdf = z_report(request)
        # Return a JSON response with the base64 encoded PDF
        return JsonResponse(
            {
                "pdf_base64": generated_pdf.decode("utf-8"),
                "message": "Z-Report PDF generated.",
            }
        )

    @action(
        detail=False,
        methods=["POST"],
        url_name="generate-z-report-txt",
        url_path="generate-z-report-txt",
    )
    def generate_z_report_txt(self, request):
        generated_pdf = z_report_txt(request)
        # Return a JSON response with the base64 encoded PDF
        return JsonResponse(
            {
                "pdf_base64": generated_pdf.decode("utf-8"),
                "message": "Z-Report PDF generated.",
            }
        )

    # @swagger_auto_schema(
    #     request_body=PayPendingTransactionSerializer
    # )
    @action(
        detail=False,
        methods=["POST"],
        url_name="report-z-report",
        url_path="report-z-report",
    )
    def report_z_report(self, request):
        generated_pdf = z_report(request)
        # Return a JSON response with the base64 encoded PDF
        return JsonResponse(
            {
                "pdf_base64": generated_pdf.decode("utf-8"),
                "message": "Z-Report PDF Reported.",
            }
        )

    @action(["POST"], False, "generate-invoice")
    def generate_invoice(self, request):
        try:
            data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError as e:
            return JsonResponse({"message": f"JSON decoding error: {e}"})

        pdf = generate_invoice(request, data["transactionId"])

        return JsonResponse({"pdf_base64": pdf, "message": "Invoice PDF generated."})

    @action(["POST"], False, "generate-invoices")
    def generate_invoices(self, request):
        try:
            data = json.loads(request.body)
        except JSONDecodeError as e:
            return JsonResponse({"message": f"JSON decoding error: {e}"})

        pdfs = [
            pdf
            for transaction_id in data.get("transactionIds")
            if (pdf := generate_invoice(request, transaction_id))
        ]

        return JsonResponse({"pdfs": pdfs, "message": "Invoice PDF generated."})

    @action(
        detail=False,
        methods=["POST"],
        url_name="generate-receipt",
        url_path="generate-receipt",
    )
    def generate_receipt(self, request):
        generated_pdf = generate_receipt(request, is_landing_page=False)
        # Return a JSON response with the base64 encoded PDF

        return JsonResponse(
            {
                "pdf_base64": generated_pdf.decode("utf-8"),
                "message": "Invoice PDF generated.",
            }
        )

    @action(
        detail=False,
        methods=["GET"],
        url_name="get-ledger-dynamic-filter",
        url_path="get-ledger-dynamic-filter",
    )
    def ledger_dynamic_filter(self, request):
        try:
            data = get_ledger_dynamic_filter(request)
            serializer = LedgerDynamicFilterSerializer(data=data, many=True)

            if serializer.is_valid():
                return Response(
                    {"status": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "error", "error": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["GET"], url_name="get-ledger", url_path="get-ledger")
    def get_ledger(self, request):

        qs = self.queryset.filter(booking__lot_id=request.user.lot_id)
        report_type = request.GET.get("reportType")
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        group_by = request.GET.get("groupBy", None)
        specific_grouping = request.GET.getlist("dynamicGrouping[]", None)

        try:
            qs = qs.exclude(transaction_status=["Refund"])

            if start_date_time and end_date_time:
                if report_type and "sales" in report_type:
                    refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & Q(transaction_status="Refund")
                        & Q(
                            transaction_datetime__range=(start_date_time, end_date_time)
                        )
                    )

                    # Filter for non-Refund transactions
                    non_refund_qs = qs.filter(
                        Q(booking__booking_status__is_latest=True)
                        & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                        & (
                            Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=False,
                                booking__room_bookings__actual_checkin_date_time__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                            | Q(
                                booking__room_bookings__actual_checkin_date_time__isnull=True,
                                booking__booking_status__check_in_datetime__range=(
                                    start_date_time,
                                    end_date_time,
                                ),
                            )
                        )
                    )

                    qs = refund_qs | non_refund_qs

                if report_type == "collections":
                    qs = qs.filter(
                        transaction_datetime__range=(start_date_time, end_date_time)
                    )

            latest_booking_status = Prefetch(
                "booking__booking_status",
                queryset=BookingStatus.objects.filter(is_latest=True),
                to_attr="latest_booking_status",
            )

            qs = qs.prefetch_related(latest_booking_status)

            detailed_qs = []

            if group_by:
                if group_by == "paymentMethod":
                    qs = group_by_payment_method(
                        qs=qs, specific_grouping=specific_grouping
                    )

                    converted_detailed_qs = []
                    for transaction in qs:
                        converted_detailed = convert_to_report_format(
                            transaction["transactions"]
                        )

                        converted_detailed_data = {
                            "payment_method_id": transaction["payment_method_id"],
                            "payment_method_name": transaction["payment_method_name"],
                            "transactions": converted_detailed,
                        }

                        converted_detailed_qs.append(converted_detailed_data)

                    detailed_serializer = LedgerPaymentMethodDetailedSerializer(
                        converted_detailed_qs, many=True
                    )

                    summary_qs = calculate_transaction_summary(
                        qs=qs, report_type="sales"
                    )
                    summary_serializer = TransactionSummaryGroupByPaymentMethod(
                        summary_qs, many=True
                    )

                if group_by == "paymentType":
                    detailed_qs = group_by_payment_type(
                        qs=qs, specific_grouping=specific_grouping
                    )

                    converted_detailed_qs = []
                    for transaction in detailed_qs:
                        converted_detailed = convert_to_report_format(
                            transaction["transactions"]
                        )

                        converted_detailed_data = {
                            "payment_type_id": transaction["payment_type_id"],
                            "payment_type_name": transaction["payment_type_name"],
                            "transactions": converted_detailed,
                        }

                        converted_detailed_qs.append(converted_detailed_data)

                    detailed_serializer = LedgerPaymentTypeDetailedSerializer(
                        converted_detailed_qs, many=True
                    )

                    summary_qs = calculate_transaction_summary(
                        qs=detailed_qs, report_type="sales"
                    )
                    summary_serializer = TransactionSummaryGroupBypaymentType(
                        summary_qs, many=True
                    )

                if group_by == "bookingPlatform":
                    detailed_qs = group_by_booking_platform(
                        qs=qs, specific_grouping=specific_grouping
                    )

                    converted_detailed_qs = []
                    for transaction in detailed_qs:
                        converted_detailed = convert_to_report_format(
                            transaction["transactions"]
                        )

                        converted_detailed_data = {
                            "booking_platform_id": transaction["booking_platform_id"],
                            "booking_platform": transaction["booking_platform"],
                            "transactions": converted_detailed,
                        }

                        converted_detailed_qs.append(converted_detailed_data)

                    detailed_serializer = LedgerBookingPlatformDetailedSerializer(
                        converted_detailed_qs, many=True
                    )

                    summary_qs = calculate_transaction_summary(
                        qs=detailed_qs, report_type="sales"
                    )
                    summary_serializer = TransactionSummaryGroupByBookingPlatform(
                        summary_qs, many=True
                    )

                if group_by == "roomType":
                    detailed_qs = group_by_room_type(
                        qs=qs, specific_grouping=specific_grouping
                    )

                    converted_detailed_qs = []
                    for transaction in detailed_qs:
                        converted_detailed = convert_to_report_format(
                            transaction["transactions"]
                        )

                        converted_detailed_data = {
                            "room_type_name": transaction["room_type_name"],
                            "transactions": converted_detailed,
                        }

                        converted_detailed_qs.append(converted_detailed_data)

                    detailed_serializer = LedgerRoomTypeDetailedSerializer(
                        converted_detailed_qs, many=True
                    )

                    summary_qs = calculate_transaction_summary(
                        qs=detailed_qs, report_type="sales"
                    )
                    summary_serializer = TransactionSummaryGroupByRoomType(
                        summary_qs, many=True
                    )

                if group_by == "promoCode":
                    detailed_qs = group_by_promotion(
                        qs=qs, specific_grouping=specific_grouping
                    )

                    converted_detailed_qs = []
                    for transaction in detailed_qs:
                        converted_detailed = convert_to_report_format(
                            transaction["transactions"]
                        )

                        converted_detailed_data = {
                            "promotion_name": transaction["promotion_name"],
                            "transactions": converted_detailed,
                        }

                        converted_detailed_qs.append(converted_detailed_data)

                    detailed_serializer = LedgerPromotionDetailedSerializer(
                        converted_detailed_qs, many=True
                    )

                    summary_qs = calculate_transaction_summary(
                        qs=detailed_qs, report_type="sales"
                    )
                    summary_serializer = TransactionSummaryGroupByPromotion(
                        summary_qs, many=True
                    )

            return Response(
                {
                    "status": "success",
                    "summaryData": summary_serializer.data,
                    "detailedData": detailed_serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="collection-summary-transaction",
    )
    def collection_summary_transactions(self, request):
        shift_id_list = request.GET.getlist("shift_id[]", None)
        date = request.GET.getlist("date[]", None)
        try:
            transactions_obj = Transaction.objects.all()
            if len(shift_id_list) > 0:
                transactions_obj = transactions_obj.filter(shift_id__in=shift_id_list)
            elif len(date) > 0:
                date_list = [
                    datetime.strptime(date_str, "%Y-%m-%d").date() for date_str in date
                ]
                transactions_obj = transactions_obj.filter(
                    shift__start_shift_datetime__date__in=date_list
                )

            transactions_obj = transactions_obj.values(
                "payment_type_id",
                payment_type_name=F("payment_type__payment_type"),
            ).annotate(total=Sum("debit_amount"))

            return_serializer = CollectionSummarySerializer(transactions_obj, many=True)

            return Response(return_serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ZReportViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = []
    permission_classes = []
    throttle_classes = []

    @swagger_auto_schema(
        operation_description="Send Z-Report for a specific date or date range (dates must be before today)",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'token': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Authentication token",
                    example="CapsuleTransitTokenKey"
                ),
                'start_date': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Start date in DD-MM-YYYY format (optional, defaults to yesterday if not provided, must be before today)",
                    example="27-05-2025"
                ),
                'end_date': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="End date in DD-MM-YYYY format (optional, if provided will process date range, must be before today)",
                    example="28-05-2025"
                )
            },
            required=['token']
        ),
        responses={
            200: openapi.Response(
                description="Report sent successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example="Success"),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example="Report Sent")
                    }
                )
            ),
            400: openapi.Response(description="Bad request - invalid date format or future date"),
            403: openapi.Response(description="Forbidden - invalid token")
        }
    )
    @action(
        detail=False,
        methods=["POST"],
        url_name="send-z-report",
        url_path="send-z-report",
    )
    def send_z_report(self, request):
        if (
            request.data.get("token") is not None
            and request.data["token"] == "CapsuleTransitTokenKey"
        ):
            # Get date parameters
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")
            
            # Get today's date for validation
            today = datetime.now().date()
            
            # Validate date parameters if provided
            if start_date:
                try:
                    start_date_obj = datetime.strptime(start_date, "%d-%m-%Y").date()
                    if start_date_obj >= today:
                        return Response(
                            {"error": "start_date must be before today"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except ValueError:
                    return Response(
                        {"error": "Invalid start_date format. Use DD-MM-YYYY format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            if end_date:
                try:
                    end_date_obj = datetime.strptime(end_date, "%d-%m-%Y").date()
                    if end_date_obj >= today:
                        return Response(
                            {"error": "end_date must be before today"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except ValueError:
                    return Response(
                        {"error": "Invalid end_date format. Use DD-MM-YYYY format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Validate date range
                if start_date:
                    if start_date_obj > end_date_obj:
                        return Response(
                            {"error": "Start date cannot be after end date"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
            # Send the report(s)
            if start_date and end_date:
                current_date = start_date_obj
                while current_date <= end_date_obj:
                    send_report(current_date)
                    current_date += timedelta(days=1)
            elif start_date:
                send_report(start_date_obj)
            else:
                send_report()

            return JsonResponse(
                {
                    "status": "Success",
                    "message": "Report Sent",
                }
            )
        else:
            return Response(
                {"message": "Forbidden"},
                status=status.HTTP_403_FORBIDDEN,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_name="get-mahb-report",
        url_path="get-mahb-report",
    )
    def get_mahb_report(self, request):
        try:
            start_date = request.GET.get("start_date", None)
            end_date = request.GET.get("end_date", None)

            start_date = (
                datetime.strptime(start_date, "%d-%m-%Y").date()
                if start_date is not None
                else datetime.date(2024, 10, 5)
            )
            end_date = (
                datetime.strptime(end_date, "%d-%m-%Y").date()
                if end_date is not None
                else datetime.date.today() - timedelta(days=1)
            )

            values = {}

            # Generate loop
            current_date = start_date
            while current_date <= end_date:
                gateway_lots = Lot.objects.filter(mahb_lot__isnull=False)
                for lot in gateway_lots:
                    # for lot in gateway_lots:
                    now = datetime.now()
                    today = now.date()

                    # Calculate yesterday's date
                    yesterday = current_date
                    day_before_yesterday = current_date - timedelta(days=1)

                    # Define the start and end of the range for yesterday
                    start_date_time = datetime.combine(
                        day_before_yesterday, time(16, 0)
                    )
                    end_date_time = datetime.combine(yesterday, time(15, 59, 59, 999))

                    qs = Transaction.objects.filter(booking__lot_id=lot.lot_id)

                    qs = qs.exclude(
                        payment_type_id__payment_type__in=[
                            "Complimentary",
                            "Recovery Services",
                        ]
                    )

                    if start_date_time and end_date_time:
                        qs = qs.exclude(transaction_status="Void")

                        refund_qs = qs.filter(
                            Q(booking__booking_status__is_latest=True)
                            & Q(transaction_status="Refund")
                            & Q(
                                transaction_datetime__range=(
                                    start_date_time,
                                    end_date_time,
                                )
                            )
                        )

                        # Filter for non-Refund transactions
                        non_refund_qs = qs.filter(
                            Q(booking__booking_status__is_latest=True)
                            & ~Q(
                                transaction_status="Refund"
                            )  # Exclude Refund transactions
                            & (
                                Q(
                                    booking__room_bookings__actual_checkin_date_time__isnull=False,
                                    booking__room_bookings__actual_checkin_date_time__range=(
                                        start_date_time,
                                        end_date_time,
                                    ),
                                )
                                | Q(
                                    booking__room_bookings__actual_checkin_date_time__isnull=True,
                                    booking__booking_status__check_in_datetime__range=(
                                        start_date_time,
                                        end_date_time,
                                    ),
                                )
                            )
                        )

                        qs = refund_qs | non_refund_qs

                    latest_booking_status = Prefetch(
                        "booking__booking_status",
                        queryset=BookingStatus.objects.filter(is_latest=True),
                        to_attr="latest_booking_status",
                    )

                    room_bookings = Prefetch(
                        "booking__room_bookings",
                        queryset=RoomBooking.objects.all(),
                        to_attr="room_booking_list",
                    )

                    qs = qs.prefetch_related(latest_booking_status, room_bookings)
                    qs = qs.distinct()
                    qs = calculate_z_report_mahb(qs)

                    sum = 0

                    for item in qs:
                        item["transaction_date"] = item["transaction_date"].strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                        item["transaction_total_amount"] = float(
                            item["transaction_total_amount"]
                        )
                        item["transaction_rounding_amount"] = float(
                            item["transaction_rounding_amount"]
                        )
                        for sales_item in item["sales_item"]:
                            sales_item["unit_price"] = float(sales_item["unit_price"])
                            sales_item["sub_total"] = float(sales_item["sub_total"])
                            sales_item["line_tax_amount"] = float(
                                sales_item["line_tax_amount"]
                            )

                        item["collection"][0]["amount"] = float(
                            item["collection"][0]["amount"]
                        )

                        sum += float(item["transaction_total_amount"])

                    value_to_append = {
                        "date": current_date,
                        "total_amount": round(sum, 2),
                    }

                    if lot.mahb_lot in values:
                        values[lot.mahb_lot].append(value_to_append)
                    else:
                        values[lot.mahb_lot] = [value_to_append]

                current_date += timedelta(days=1)

            return Response(
                {"status": "success", "data": values}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
