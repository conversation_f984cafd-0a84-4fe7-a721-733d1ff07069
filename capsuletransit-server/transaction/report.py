from django.db.models import Prefetch, Q
from datetime import datetime, timedelta, time
import logging
from decimal import Decimal

from bookings.models import BookingStatus, RoomBooking
from lot.models import Lot
from transaction.models import Transaction
from transaction.services.transaction import (
    calculate_z_report_klia_paymentfile,
    calculate_z_report_klia_salesfile,
    calculate_z_report_gateway_detailed,
    calculate_z_report_gateway_summary,
    calculate_z_report_mahb,
    group_by_actual_check_in,
    group_by_date,
)
from transaction.z_report import z_report, z_report_mahb_json, z_report_txt

logger = logging.getLogger("django_crontab")


class MockRequest:
    def __init__(self):
        self.data = {}


def send_report(date=None):
    try:
        gateway_lots = Lot.objects.all()
        for lot in gateway_lots:
            # for lot in gateway_lots:
            now = datetime.now()
            today = now.date()

            if date:
                process_date = date
            else:
                process_date = today - timedelta(days=1)

            # Define the start and end of the range for process_date
            start_date_time = datetime.combine(process_date - timedelta(days=1), time(16, 0))
            end_date_time = datetime.combine(process_date, time(15, 59, 59, 999))
                
            logger.info(f"ReportZReport cron job is starting. Date: {start_date_time}")
        
            qs = Transaction.objects.filter(booking__lot_id=lot.lot_id)

            qs = qs.exclude(
                payment_type_id__payment_type__in=[
                    "Complimentary",
                    "Recovery Services",
                ]
            )

            if start_date_time and end_date_time:
                qs = qs.exclude(transaction_status="Void")

                refund_qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & Q(transaction_status="Refund")
                    & Q(transaction_datetime__range=(start_date_time, end_date_time))
                )

                # Filter for non-Refund transactions
                non_refund_qs = qs.filter(
                    Q(booking__booking_status__is_latest=True)
                    & ~Q(transaction_status="Refund")  # Exclude Refund transactions
                    & (
                        Q(
                            booking__room_bookings__actual_checkin_date_time__isnull=False,
                            booking__room_bookings__actual_checkin_date_time__range=(
                                start_date_time,
                                end_date_time,
                            ),
                        )
                        | Q(
                            booking__room_bookings__actual_checkin_date_time__isnull=True,
                            booking__booking_status__check_in_datetime__range=(
                                start_date_time,
                                end_date_time,
                            ),
                        )
                    )
                )

                qs = refund_qs | non_refund_qs

            latest_booking_status = Prefetch(
                "booking__booking_status",
                queryset=BookingStatus.objects.filter(is_latest=True),
                to_attr="latest_booking_status",
            )

            room_bookings = Prefetch(
                "booking__room_bookings",
                queryset=RoomBooking.objects.all(),
                to_attr="room_booking_list",
            )

            if lot.gateawaymall_lot is not None:
                # Run for Lots that have gateway lot number
                detailed_qs = qs.prefetch_related(latest_booking_status, room_bookings)
                detailed_qs = detailed_qs.distinct()
                detailed_qs = calculate_z_report_gateway_detailed(detailed_qs)
                detailed_qs = convert_detailed_zreport(detailed_qs)

                detailed_filename = (
                    f"{lot.gateawaymall_lot}_{process_date.strftime('%d%m%Y')}_ZRPT.pdf"
                )

                summary_qs = qs.prefetch_related(latest_booking_status, room_bookings)
                summary_qs = summary_qs.distinct()
                summary_qs = group_by_actual_check_in(summary_qs)
                summary_qs = calculate_z_report_gateway_summary(summary_qs)

                total_sum = sum(float(entry["total"]) for entry in summary_qs)

                summary_qs = [
                    {
                        "item": 1,
                        "date": process_date.strftime("%Y-%m-%d"),
                        "total": total_sum,
                    }
                ]

                summary_filename = (
                    f"{lot.gateawaymall_lot}_{process_date.strftime('%d%m%Y')}_A.txt"
                )

                detailed_request = MockRequest()

                detailed_request.data = {
                    "data": detailed_qs,
                    "start_date": start_date_time + timedelta(hours=8),
                    "end_date": end_date_time + timedelta(hours=8),
                    "report_file": True,
                    "file_name": detailed_filename,
                }

                summary_request = MockRequest()
                summary_request.data = {
                    "data": summary_qs,
                    "report_file": True,
                    "file_name": summary_filename,
                    "version": "summary",
                    "lot_id": lot.lot_id,
                }

                if len(detailed_qs) > 0:
                    z_report(detailed_request, lot_number=lot.lot_id)

                if len(summary_qs) > 0:
                    z_report_txt(summary_request)

            elif lot.mahb_lot is not None:
                qs = qs.prefetch_related(latest_booking_status, room_bookings)
                qs = qs.distinct()
                qs = calculate_z_report_mahb(qs)

                for item in qs:
                    item["transaction_date"] = (
                        item["transaction_date"] + timedelta(hours=8)
                    ).strftime("%Y-%m-%d %H:%M:%S")
                    item["transaction_total_amount"] = float(
                        item["transaction_total_amount"]
                    )
                    item["transaction_rounding_amount"] = float(
                        item["transaction_rounding_amount"]
                    )
                    for sales_item in item["sales_item"]:
                        sales_item["unit_price"] = float(sales_item["unit_price"])
                        sales_item["sub_total"] = float(sales_item["sub_total"])
                        sales_item["line_tax_amount"] = float(
                            sales_item["line_tax_amount"]
                        )

                    item["collection"][0]["amount"] = float(
                        item["collection"][0]["amount"]
                    )

                if len(qs) > 0:
                    file_name = f"{lot.mahb_lot}_{'KLIA2' if lot.lot_id == 2 else 'KLIA'}_{process_date.strftime('%d%m%Y')}.json"
                    z_report_mahb_json(qs, file_name, lot.lot_id)

        logger.info("ReportZReport cron job completed successfully.")

    except Exception as e:
        logger.error("Error occurred in ReportZReport cron job: %s", e, exc_info=True)


def convert_detailed_zreport(
    unformatted_detailed_data,
):
    sales = sum(Decimal(data["sales"]) for data in unformatted_detailed_data)
    footer = {
        "item": "",
        "type": "",
        "id": "",
        "details": "",
        "paymentDate": "",
        "checkIn": "",
        "checkOut": "",
        "sales": f"Total Amount: {sales:.2f}",
    }

    formatted_detailed_data = []

    for data in unformatted_detailed_data:
        formatted_data = data.copy()  # Make a copy to avoid modifying the original
        formatted_data["item"] = str(data["item"])
        formatted_data["sales"] = str(data["sales"])
        formatted_data["details"] = str(data["details"][0]["item_name"])
        formatted_data["check_in"] = (
            str(
                (
                    datetime.fromisoformat(data["check_in"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if formatted_data["check_in"] != "N/A"
            else formatted_data["check_in"]
        )
        formatted_data["check_out"] = (
            str(
                (
                    datetime.fromisoformat(data["check_out"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if formatted_data["check_out"] != "N/A"
            else formatted_data["check_out"]
        )
        formatted_data["payment_date"] = (
            str(
                (
                    datetime.fromisoformat(data["payment_date"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if formatted_data["payment_date"] != "N/A"
            else formatted_data["payment_date"]
        )

        formatted_detailed_data.append(formatted_data)

    formatted_detailed_data.append(footer)

    return formatted_detailed_data


def convert_sales_zreport(
    unformatted_sales_data,
):
    total_amount = sum(Decimal(data["total_amount"]) for data in unformatted_sales_data)
    total_service_charge = sum(
        Decimal(data["service_charge_amount"]) for data in unformatted_sales_data
    )
    total_tax = sum(Decimal(data["tax_amount"]) for data in unformatted_sales_data)
    footer = {
        "transaction_id": "",
        "total_amount": f"Total Amount: {total_amount:.2f}",
        "total_service": f"Total Amount: {total_service_charge:.2f}",
        "tax_amount": f"Total Amount: {total_tax:.2f}",
        "status": "",
        "sales_date": "",
        "business_date": "",
        "need_update_sales": "",
    }

    formatted_sales_data = []

    for data in unformatted_sales_data:
        formatted_data = data.copy()  # Make a copy to avoid modifying the original
        formatted_data["sales_date"] = (
            str(
                (
                    datetime.fromisoformat(data["sales_date"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if formatted_data["sales_date"] != "-"
            else "-"
        )
        formatted_data["business_date"] = (
            str(
                (
                    datetime.fromisoformat(data["business_date"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if formatted_data["business_date"] != "-"
            else "-"
        )
        formatted_sales_data.append(formatted_data)

    formatted_sales_data.append(footer)

    return formatted_sales_data


def convert_payment_zreport(
    unformatted_payment_data,
):
    total_amount = sum(Decimal(data["amount"]) for data in unformatted_payment_data)
    footer = {
        "transaction_id": "",
        "amount": f"Total Amount: {total_amount:.2f}",
        "currecy_code": "",
        "currency_amount": "",
        "currency_exchange_rate": "",
        "payment_date": "",
        "business_date": "",
        "payment_method": "",
        "payment_type": "",
    }

    formatted_payment_data = []

    for data in unformatted_payment_data:
        formatted_data = data.copy()  # Make a copy to avoid modifying the original
        formatted_data["payment_date"] = (
            str(
                (
                    datetime.fromisoformat(data["payment_date"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if data["payment_date"] != "-"
            else "-"
        )
        formatted_data["business_date"] = (
            str(
                (
                    datetime.fromisoformat(data["business_date"]) + timedelta(hours=8)
                ).strftime("%d/%m/%Y")
            )
            if data["business_date"] != "-"
            else "-"
        )
        formatted_payment_data.append(formatted_data)

    formatted_payment_data.append(footer)

    return formatted_payment_data
