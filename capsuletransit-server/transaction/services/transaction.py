import base64
import copy
from decimal import Decimal
import json
import math
import os
from base64 import b64decode, b64encode
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, List, Mapping, Optional
from django.http import HttpResponse
from rest_framework import status
import pytz
import dotenv

import requests
from accounts.utils import get_current_shift
from bookings.models import (
    Booking,
    BookingStatus,
    LockerBooking,
    RoomBooking,
    ShowerBooking,
    TransferBooking,
)
from constant.enums import TransactionItemCategory
from bookingplatform.models import Platform
from django.conf import settings
from django.db.models import Sum
from django.shortcuts import render
from django.utils import timezone
from django.utils.timezone import datetime as dt
from landingpage.serailizers import GenerateReceiptSerializer
from lot.models import Lot, Settings
from lot.utils import (
    get_lot_settings_by_lot_id,
    get_lot_settings_by_lot_with_serializer,
)
from payment.models import PaymentMethod, PaymentType
from promotions.models import Promotion
from rooms.models import RoomType
from transaction.models import Transaction
from transaction.serializer import (
    GenerateReceiptHMSSerializer,
    TransactionSerializer,
    UpdateTransactionStatusSerializer,
)
from transaction.utils import (
    calculate_amount_percentage,
    calculate_booking_duration,
    find_tax_percentage,
    generate_invoice_no,
    map_payment_method,
    map_payment_method_v2,
    map_payment_type,
)
from django.db import transaction as ts

dotenv.load_dotenv()


def transactions_confirm_booking(booking_id):
    """
    Function to  confirm booking of a transaction given booking_id
    """
    with ts.atomic():
        transactions = Transaction.objects.filter(
            booking_id=booking_id, is_room_booking=True, transaction_status="Paid"
        ).aggregate(
            total_sum=Sum("sum"),
            total_debit_amount=Sum("debit_amount"),
            total_promotions_amount=Sum("promotion_amount"),
            total_adjustment_amount=Sum("adjustements_amount"),
            total_roundings=Sum("rounding"),
        )

        if not transactions["total_sum"]:
            return

        promo_amount = (
            transactions["total_promotions_amount"]
            if transactions["total_promotions_amount"]
            else 0
        )

        adjustments_amount = abs(
            transactions["total_adjustment_amount"]
            if transactions["total_adjustment_amount"]
            else 0
        )

        total_roundings = abs(
            transactions["total_roundings"] if transactions["total_roundings"] else 0
        )

        booking: Booking = Booking.objects.select_for_update().get(
            booking_id=booking_id
        )
        billed_amount = booking.billed_room_bookings_amount
        if (
            transactions["total_debit_amount"]
            + promo_amount
            + adjustments_amount
            + total_roundings
        ) >= billed_amount and booking.can_be_confirmed:
            booking.confirm_booking()
            return True

    return False


def transaction_confirm_booking_for_not_room_booking(booking_id):

    with ts.atomic():
        room_booking_exist = RoomBooking.objects.filter(booking_id=booking_id).exists()
        if room_booking_exist:
            return False

        locker_bookings = LockerBooking.objects.filter(booking_id=booking_id)
        shower_bookings = ShowerBooking.objects.filter(booking_id=booking_id)

        if not locker_bookings.exists() and not shower_bookings.exists():
            return False

        booking: Booking = Booking.objects.select_for_update().get(
            booking_id=booking_id
        )

        billed_amount = 0
        if locker_bookings.exists():
            for item in locker_bookings:
                billed_amount += item.sum

        if shower_bookings.exists():
            for item in shower_bookings:
                billed_amount += item.sum

        transactions = Transaction.objects.filter(
            booking_id=booking_id, is_room_booking=False, transaction_status="Paid"
        ).aggregate(
            total_sum=Sum("sum"),
            total_debit_amount=Sum("debit_amount"),
            total_promotions_amount=Sum("promotion_amount"),
            total_adjustment_amount=Sum("adjustements_amount"),
        )

        promo_amount = (
            transactions["total_promotions_amount"]
            if transactions["total_promotions_amount"]
            else 0
        )

        debit_amount = (
            transactions["total_debit_amount"]
            if transactions["total_debit_amount"]
            else 0
        )

        adjustments_amount = abs(
            transactions["total_adjustment_amount"]
            if transactions["total_adjustment_amount"]
            else 0
        )

        if (
            debit_amount + promo_amount + adjustments_amount
        ) >= billed_amount and booking.can_be_confirmed:
            booking.confirm_booking()
            return True

    return False


def update_status(data: UpdateTransactionStatusSerializer):
    """
    Update transaction status
    """
    validated_data = data.validated_data

    try:
        transaction = Transaction.objects.get(pk=validated_data["transaction_id"])
    except:
        raise Exception("transaction not found")

    if validated_data["status"] == transaction.transaction_status:
        raise Exception(f'transaction status already {validated_data["status"]}')

    if validated_data["status"] == "Paid":
        if transaction.credit_amount != 0:
            transaction.debit_amount = transaction.credit_amount
            transaction.credit_amount = 0

            transaction.transaction_datetime = dt.now()
            transaction.payment_type_id = validated_data["payment_type_id"]

            transaction.save()

    transaction.transaction_status = validated_data["status"]
    transaction.save()

    serializer = TransactionSerializer(transaction)
    return serializer.data


def create_new_pending_transaction(
    booking_id,
    transaction_details,
    sum,
    account_id,
    is_room_booking: bool = False,
    remarks: str = "",
    promotion=None,
    promotion_amount=0,
    roundings=0,
    shift=None,
    for_landing_page=None,
):
    """
    Create new pending transaction given booking_id, previous transaction, transaction details, sum, account_id
    """

    booking = Booking.objects.get(pk=booking_id)

    new_invoice = generate_invoice_no(
        booking_no=booking.booking_no, transaction_date=datetime.now()
    )

    tax_percentage = 0
    service_charge_percentage = 0
    lot_settings = get_lot_settings_by_lot_with_serializer(booking.lot_id)

    for setting in lot_settings:
        if setting["settings_name"] == "SST":
            tax_number_only = setting["settings_description"].strip("%")
            tax_percentage = int(tax_number_only)

        if setting["settings_name"] == "Service Charge":
            service_charge_number_only = setting["settings_description"].strip("%")
            service_charge_percentage = int(service_charge_number_only)

    tax = calculate_amount_percentage(
        price=sum + promotion_amount, percentage=tax_percentage
    )
    service_charge = calculate_amount_percentage(
        price=sum, percentage=service_charge_percentage
    )

    if not shift:
        shift = get_current_shift(account_id=account_id)

    if promotion_amount < 0 and promotion is not None:
        used_promotion = Promotion.objects.filter(promo_code=promotion, archived=False)
        if len(used_promotion) > 0:
            used_promotion = used_promotion[0]
            transaction_details.append(
                {
                    "item_id": str(used_promotion.promotion_id),
                    "item_name": used_promotion.promo_code
                    + " "
                    + used_promotion.details,
                    "item_type": "Promotion",
                    "category": TransactionItemCategory.PROMOTION,
                    "price": float(promotion_amount),
                }
            )

    for setting in lot_settings:
        amount = 0

        if service_charge > 0 and setting["settings_name"] == "Service Charge":
            amount = service_charge

        if tax > 0 and setting["settings_name"] == "SST":
            amount = tax

        if (
            setting["settings_name"] == "SST"
            or setting["settings_name"] == "Service Charge"
        ):
            transaction_details.append(
                {
                    "item_id": str(setting["settings_id"]),
                    "item_name": str(setting["settings_name"]),
                    "item_type": str(setting["settings_category"]),
                    "quantity": str(1),
                    "price": round(amount, 2),
                    "category": (
                        TransactionItemCategory.TAX
                        if setting["settings_name"] == "SST"
                        else TransactionItemCategory.SERVICE_CHARGE
                    ),
                }
            )

    if roundings is not None and roundings is not 0:
        transaction_details.append(
            {
                "item_id": "Rounding",
                "item_name": "Rounding",
                "item_type": "Rounding",
                "category": TransactionItemCategory.ROUNDING,
                "price": float(roundings),
            },
        )

    details_str = json.dumps(transaction_details)

    pay_later = PaymentType.objects.get(payment_type="Paylater")

    new_transaction = Transaction.objects.create(
        booking_id=booking.pk,
        customer_id=booking.customer_staying.pk,
        payment_type_id=pay_later.pk,
        currency_id=1,  # static
        shift_id=shift.pk,
        invoice_no=new_invoice,
        payment_details=details_str,
        sum=sum,
        tax_amount=tax,
        service_charge_amount=service_charge,
        adjustments="",
        adjustements_amount=0,
        promotion_amount=(
            promotion_amount * -1 if for_landing_page else promotion_amount
        ),
        credit_amount=float(sum)
        + tax
        + service_charge
        + float(promotion_amount)
        + float(roundings),
        debit_amount=0,
        rounding=roundings,
        transaction_status="Pending Payment",
        is_latest=True,
        is_room_booking=is_room_booking,
        items=transaction_details,
        guest_given=0,
        guest_change=0,
        pan="",
        payment_reference="",
        payment_remarks=remarks,
    )

    return new_transaction


def create_modified_transaction(
    previous_transaction: Transaction,
    new_booking: Booking,
    new_transaction_details,
    new_shift_id,
    remarks: str = "",
):
    new_invoice = generate_invoice_no(
        booking_no=new_booking.booking_no, transaction_date=timezone.now()
    )

    details_str = json.dumps(new_transaction_details)

    new_transaction = Transaction.objects.create(
        booking_id=new_booking.pk,
        customer_id=previous_transaction.customer_id,
        payment_type_id=previous_transaction.payment_type_id,
        currency_id=previous_transaction.currency_id,
        shift_id=new_shift_id,
        invoice_no=new_invoice,
        payment_details=details_str,
        sum=previous_transaction.sum,
        tax_amount=previous_transaction.tax_amount,
        service_charge_amount=previous_transaction.service_charge_amount,
        adjustments=previous_transaction.adjustments,
        adjustements_amount=previous_transaction.adjustements_amount,
        promotion_amount=previous_transaction.promotion_amount,
        credit_amount=previous_transaction.credit_amount,
        debit_amount=previous_transaction.debit_amount,
        rounding=previous_transaction.rounding,
        transaction_status=previous_transaction.transaction_status,
        is_latest=True,
        is_room_booking=previous_transaction.is_room_booking,
        items=new_transaction_details,
        transaction_datetime=previous_transaction.transaction_datetime,
        payment_remarks=remarks,
        guest_given=previous_transaction.guest_given,
        guest_change=previous_transaction.guest_change,
        pan=previous_transaction.pan,
        payment_reference=previous_transaction.payment_reference,
    )

    return new_transaction


def group_by_booking(qs):
    """
    group transaction by booking
    """
    grouped_transactions = {}

    for transaction in qs:
        latest_booking_status = getattr(
            transaction.booking, "latest_booking_status", None
        )[0]
        booking_id = transaction.booking
        booking_num = transaction.booking.booking_no
        booking_status = latest_booking_status
        if booking_id not in grouped_transactions:
            grouped_transactions[booking_id] = {
                "booking_id": booking_id,
                "booking_no": booking_num,
                "booking_status": booking_status.booking_status,
                "transactions": [],
            }
        grouped_transactions[booking_id]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())
    return grouped_data


def group_by_transaction(qs):
    """
    format the transaction data into grouped format
    """

    grouped_transactions = {}

    for transaction in qs:
        invoice_num = transaction.invoice_no
        transaction_id = transaction.transaction_id
        if transaction_id not in grouped_transactions:
            grouped_transactions[transaction_id] = {
                "transaction_id": transaction_id,
                "invoice_no": invoice_num,
                "transactions": [],
            }
        grouped_transactions[transaction_id]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())
    return grouped_data


def group_by_payment_method(qs, specific_grouping):
    """
    group transaction by its payment type
    """
    qs = qs.prefetch_related("payment_type")

    grouped_transactions = {}

    if specific_grouping:
        qs = qs.filter(
            payment_type__payment_method__payment_method__in=specific_grouping
        )

    for transaction in qs:
        payment_method_id = (
            transaction.payment_type.payment_method.payment_method_id
            if transaction.payment_type
            else None
        )
        payment_method = (
            transaction.payment_type.payment_method.payment_method
            if transaction.payment_type
            else None
        )
        if payment_method_id not in grouped_transactions:
            grouped_transactions[payment_method_id] = {
                "payment_method_id": payment_method_id,
                "payment_method_name": payment_method,
                "transactions": [],
            }
        grouped_transactions[payment_method_id]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_payment_type(qs, specific_grouping=None):
    """
    group transaction by its payment type
    """
    qs = qs.prefetch_related("payment_type")

    grouped_transactions = {}

    if specific_grouping:
        qs = qs.filter(payment_type__payment_type__in=specific_grouping)

    for transaction in qs:
        payment_type_id = transaction.payment_type_id
        payment_type_type = (
            transaction.payment_type.payment_type if transaction.payment_type else None
        )
        if payment_type_id not in grouped_transactions:
            grouped_transactions[payment_type_id] = {
                "payment_type_id": payment_type_id,
                "payment_type_name": payment_type_type,
                "transactions": [],
            }
        grouped_transactions[payment_type_id]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_booking_platform(qs, specific_grouping=None):
    """
    group transaction by its booking platform
    """
    qs = qs.prefetch_related("booking__platform")

    grouped_transactions = {}

    if specific_grouping:
        qs = qs.filter(booking__platform__platform__in=specific_grouping)

    for transaction in qs:
        booking_platform_id = transaction.booking.platform.platform_id

        if booking_platform_id not in grouped_transactions:
            grouped_transactions[booking_platform_id] = {
                "booking_platform_id": booking_platform_id,
                "booking_platform": transaction.booking.platform.platform,
                "transactions": [],
            }
        grouped_transactions[booking_platform_id]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_booking_duration(qs):
    """
    group transaction by its booking duration
    """
    qs = qs.prefetch_related("booking__booking_status")

    grouped_transactions = {}

    for transaction in qs:
        booking = transaction.booking
        check_in_datetime = (
            booking.booking_status.filter(is_latest=True).first().check_in_datetime
        )
        check_out_datetime = (
            booking.booking_status.filter(is_latest=True).first().check_out_datetime
        )
        booking_duration_hours = calculate_booking_duration(
            check_in_datetime, check_out_datetime
        )
        booking_duration_hours = math.floor(booking_duration_hours)

        if booking_duration_hours not in grouped_transactions:
            grouped_transactions[booking_duration_hours] = {
                "booking_duration": booking_duration_hours,
                "transactions": [],
            }
        grouped_transactions[booking_duration_hours]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_room_type(qs, specific_grouping=None):
    """
    group transaction by room type
    if the transaction have multiple room, then separate the transaction if the roomtype is different
    """
    # grouped_transactions = {"POS": {"room_type_name": "POS", "transactions": []}}

    grouped_transactions = {}

    for transaction in qs:
        is_room_booking = transaction.is_room_booking
        items = transaction.items

        if not is_room_booking:
            # grouped_transactions["POS"]["transactions"].append(transaction)
            continue

        room_type_list = []

        for room_type_info in items:
            room_type_name = room_type_info.get("item_type")
            category = room_type_info.get("category")

            if (
                category == TransactionItemCategory.ROOM_SALES
                or TransactionItemCategory.ROOM_UPGRADE
            ):
                if room_type_name not in specific_grouping:
                    continue

                if room_type_name not in grouped_transactions:
                    grouped_transactions[room_type_name] = {
                        "room_type_name": room_type_name,
                        "transactions": [],
                    }

                if room_type_name not in room_type_list:
                    room_type_list.append(room_type_name)

        if len(room_type_list) < 2 and len(room_type_list) > 0:
            grouped_transactions[room_type_list[0]]["transactions"].append(transaction)
            continue

        for type in room_type_list:
            tax_percentage = 0
            service_charge_percentage = 0
            if transaction.sum != 0:
                tax_percentage = (transaction.tax_amount / transaction.sum) * 100
                service_charge_percentage = (
                    transaction.service_charge_amount / transaction.sum
                ) * 100

            filtered_items_data = [
                item for item in transaction.items if item.get("item_type") == type
            ]

            new_sum = 0

            for item in filtered_items_data:
                new_sum += Decimal(item["price"])

            new_tax = (
                Decimal(new_sum) * Decimal(tax_percentage / 100)
                if tax_percentage != 0
                else 0
            )
            new_service_charge = Decimal(new_sum) * Decimal(
                service_charge_percentage / 100
            )

            prev_item_tax = [
                item for item in transaction.items if item.get("item_type") == "Tax"
            ]
            if new_tax != 0:
                prev_item_tax[0]["price"] = float(new_tax)
            filtered_items_data.append(prev_item_tax[0])

            prev_item_service_charge = [
                item
                for item in transaction.items
                if item.get("item_type") == "Service Charge"
            ]

            if new_service_charge != 0:
                prev_item_service_charge[0]["price"] = float(new_service_charge)
            filtered_items_data.append(prev_item_service_charge[0])

            new_adjustment_amount = Decimal(
                transaction.adjustements_amount / len(room_type_list)
                if transaction.adjustements_amount
                else 0
            )
            new_promotion_amount = Decimal(
                transaction.promotion_amount / len(room_type_list)
                if transaction.promotion_amount
                else 0
            )

            new_debit_amount = transaction.debit_amount
            new_credit_amount = transaction.credit_amount
            if new_credit_amount != 0:
                new_credit_amount = (
                    new_tax
                    + new_sum
                    + new_service_charge
                    + new_adjustment_amount
                    - new_promotion_amount
                )

            elif new_debit_amount != 0:
                new_debit_amount = (
                    new_tax
                    + new_sum
                    + new_service_charge
                    + new_adjustment_amount
                    - new_promotion_amount
                )

            # crete new transaction instance for the seperated transaction (display purpose only)
            payment_details = json.dumps(filtered_items_data)
            new_transaction = Transaction(
                transaction_id=transaction.pk,
                customer=transaction.customer,
                payment_type=transaction.payment_type,
                pan=transaction.pan,
                currency=transaction.currency,
                shift=transaction.shift,
                invoice_no=transaction.invoice_no,
                payment_details=payment_details,
                sum=new_sum,
                tax_amount=new_tax,
                service_charge_amount=new_service_charge,
                adjustments=transaction.adjustments,
                adjustements_amount=new_adjustment_amount,
                promotion_amount=new_promotion_amount,
                credit_amount=new_credit_amount,
                debit_amount=new_debit_amount,
                transaction_datetime=transaction.transaction_datetime,
                is_latest=transaction.is_latest,
                is_room_booking=transaction.is_room_booking,
                items=filtered_items_data,
                booking=transaction.booking,
            )

            grouped_transactions[type]["transactions"].append(new_transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_promotion(qs, specific_grouping=None):
    """
    group transaction by oromotion
    if the transaction have multiple room, then separate the transaction if the roomtype is different
    """
    grouped_transactions = {}

    for transaction in qs:
        items = transaction.items

        for promotion in items:
            promotion_name = promotion.get("item_name")
            category = promotion.get("category")

            if category != TransactionItemCategory.PROMOTION:
                continue

            if specific_grouping and promotion_name not in specific_grouping:
                continue

            if promotion_name not in grouped_transactions:
                grouped_transactions[promotion_name] = {
                    "promotion_name": promotion_name,
                    "transactions": [],
                }

            grouped_transactions[promotion_name]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


def group_by_date(qs):
    """
    Group transactions by date
    """
    grouped_transactions = defaultdict(list)

    for transaction in qs:
        if transaction.transaction_datetime is None:
            pass
            # grouped_transactions["null"].append(transaction)
        else:
            transaction_date = (
                transaction.transaction_datetime + timedelta(hours=8)
            ).date()
            grouped_transactions[transaction_date].append(transaction)

    # Sort the grouped data by date in descending order
    grouped_data = sorted(
        [
            {"date": date_key, "transactions": transactions}
            for date_key, transactions in grouped_transactions.items()
        ],
        key=lambda x: x["date"],
        reverse=True,
    )

    return grouped_data


def group_by_actual_check_in(qs):
    """
    Group transactions by date
    """
    grouped_transactions = defaultdict(list)

    for transaction in qs:
        if transaction.transaction_datetime is None:
            continue

        room_booking_list = getattr(transaction.booking, "room_booking_list", None)
        latest_booking_status = getattr(
            transaction.booking, "latest_booking_status", None
        )
        check_in = (
            room_booking_list[0].actual_checkin_date_time + timedelta(hours=8)
            if room_booking_list is not None
            and len(room_booking_list) > 0
            and room_booking_list[0].actual_checkin_date_time
            else latest_booking_status[0].check_in_datetime + timedelta(hours=8)
        ).date()
        if transaction.transaction_status == "Refund":
            check_in = (transaction.transaction_datetime + timedelta(hours=8)).date()
        grouped_transactions[check_in].append(transaction)

    # Sort the grouped data by date in descending order
    grouped_data = sorted(
        [
            {"date": date_key, "transactions": transactions}
            for date_key, transactions in grouped_transactions.items()
        ],
        key=lambda x: x["date"],
        reverse=True,
    )

    return grouped_data


def group_by_checkin_date(qs):
    """
    Group transactions by check in date
    """
    grouped_transactions = {}

    for transaction in qs:
        latest_booking_status = getattr(
            transaction.booking, "latest_booking_status", None
        )[0]
        check_in_datetime = latest_booking_status.check_in_datetime
        if check_in_datetime:
            date = check_in_datetime.date()

            if date not in grouped_transactions:
                grouped_transactions[date] = {
                    "date": date,
                    "transactions": [],
                }
            grouped_transactions[date]["transactions"].append(transaction)

    grouped_data = list(grouped_transactions.values())

    return grouped_data


# Explanation: Code is Deprecated due to the slowness in the logic, it has been updated with the function above
# def group_by_checkin_date(qs):
#     """
#     Group transactions by check in date
#     """

#     qs = qs.prefetch_related("booking__booking_status")
#     grouped_transactions = {}

#     for transaction in qs:
#         booking = transaction.booking
#         check_in_datetime = (
#             booking.booking_status.filter(is_latest=True).first().check_in_datetime
#         )

#         date = check_in_datetime.date()

#         if date not in grouped_transactions:
#             grouped_transactions[date] = {
#                 "date": date,
#                 "transactions": [],
#             }
#         grouped_transactions[date]["transactions"].append(transaction)

#     grouped_data = list(grouped_transactions.values())

#     return grouped_data


# def calculate_total_amount(qs):
#     """
#     to calculate
#     - total sum
#     - total credit amount
#     - total debit amount
#     - total adjustment amount
#     - total promotion amount
#     of the grouped transaction
#     """
#     for data in qs:

#         total_sum = 0
#         credit_amount = 0
#         debit_amount = 0
#         adjustment_amount = 0
#         promotion_amount = 0

#         for transaction in data["transactions"]:
#             total_sum += transaction.sum if transaction.sum else 0
#             credit_amount += transaction.credit_amount if transaction.credit_amount else 0
#             debit_amount += transaction.debit_amount if transaction.debit_amount else 0
#             adjustment_amount += transaction.adjustements_amount if transaction.adjustements_amount else 0
#             promotion_amount += transaction.promotion_amount if transaction.promotion_amount else 0

#         data["total_sum"] = total_sum
#         data["total_credit_amount"] = credit_amount
#         data["total_debit_amount"] = debit_amount
#         data["total_adjustment_amount"] = adjustment_amount
#         data["total_promotion_amount"] = promotion_amount

#     return qs


def calculate_transaction_summary(qs, report_type):
    """
    calculate and process data for summary verisons of report
    """
    for data in qs:
        payment_date = ""
        room_sales = 0
        no_show = 0
        pos = 0
        service_charge = 0
        tax = 0
        promotion = 0
        adjustment = 0
        sum = 0
        roundings = 0
        for transaction in data["transactions"]:
            if transaction.transaction_status == "Void":
                continue

            payment_date = f"{payment_date}{', ' if payment_date != '' else ''}{transaction.transaction_datetime}"

            latest_booking_status = getattr(
                transaction.booking, "latest_booking_status", None
            )

            if (
                transaction.is_room_booking
                and latest_booking_status[0].booking_status != "No Show"
                and not latest_booking_status[0].has_no_show
            ):
                room_sales += transaction.sum if transaction.sum else 0

            if (
                transaction.is_room_booking
                and latest_booking_status[0].booking_status == "No Show"
                or transaction.is_room_booking
                and latest_booking_status[0].has_no_show
            ):

                transaction_sum = transaction.sum if transaction.sum else 0

                if transaction.transaction_datetime and report_type == "sales":
                    time_difference = abs(
                        latest_booking_status[0].check_in_datetime
                        - transaction.transaction_datetime
                    )

                    is_no_show = False
                    if (
                        latest_booking_status[0].has_no_show
                        or latest_booking_status[0].booking_status == "No Show"
                    ):
                        is_no_show = True

                    if is_no_show and time_difference > timedelta(days=1):
                        transaction_sum = -1 * transaction_sum

                no_show += transaction_sum

            if transaction.is_room_booking == False:
                pos += transaction.sum if transaction.sum else 0

            service_charge += (
                transaction.service_charge_amount
                if transaction.service_charge_amount
                else 0
            )
            tax += transaction.tax_amount if transaction.tax_amount else 0
            promotion += (
                transaction.promotion_amount if transaction.promotion_amount else 0
            )
            adjustment += (
                transaction.adjustements_amount
                if transaction.adjustements_amount
                else 0
            )

            roundings += transaction.rounding if transaction.rounding else 0

            sum += transaction.sum if transaction.sum else 0

        data["date"] = payment_date
        data["room_sales"] = room_sales
        data["no_show"] = no_show
        data["pos"] = pos
        data["gross"] = room_sales + no_show + pos
        data["service_charge"] = service_charge
        data["roundings"] = roundings
        data["tax"] = tax
        data["promotion"] = promotion
        data["adjustments"] = adjustment
        data["total"] = sum + adjustment - promotion + service_charge + tax + roundings

    return qs


def calculate_transaction_detailed(
    qs, group_by, report_type, start_date_time, end_date_time
):
    # Code below is to check whether the filter is more than a day for range
    range_above_1_day = abs(
        datetime.strptime(end_date_time, "%Y-%m-%dT%H:%M:%S.%fZ")
        - datetime.strptime(start_date_time, "%Y-%m-%dT%H:%M:%S.%fZ")
    ) > timedelta(days=1)

    no_show_transaction = []

    for data in qs[:]:
        latest_booking_status = getattr(
            data["transactions"][0].booking, "latest_booking_status", None
        )
        room_booking_list = getattr(
            data["transactions"][0].booking, "room_booking_list", None
        )

        booking = data["transactions"][0].booking

        locker_booking_list = LockerBooking.objects.filter(booking=booking)
        shower_booking_list = ShowerBooking.objects.filter(booking=booking)

        transfer_info = "-"
        if (
            "Transfer" in latest_booking_status[0].booking_status
            and group_by == "booking"
        ):
            transfer_booking = TransferBooking.objects.filter(
                from_booking=data["booking_id"]
            ).first()

            if transfer_booking:
                transfer_info = f"To {transfer_booking.to_booking.booking_no} / From {transfer_booking.from_booking.booking_no}"

        payment_date = ""
        payment_type_list = []
        pan_list = []
        payment_status_list = []
        transaction_sum = 0
        transaction_debit_credit_sum = 0
        transaction_tax = 0
        promotions = 0
        adjustments = 0
        service_charge = 0
        roundings = 0
        adjustment_string = []

        room_booking_length = 0

        for transaction in data["transactions"]:
            if transaction.transaction_status == "Void":
                continue

            # check if transaction value need reverse
            need_reverse = False
            if report_type == "sales":
                need_reverse = check_need_reverse(
                    booking_id=booking,
                    transaction_datetime=transaction.transaction_datetime,
                    is_room_booking=transaction.is_room_booking,
                    latest_booking_status=latest_booking_status[0],
                    room_booking=room_booking_list,
                    range_above_1_day=range_above_1_day,
                    start_date_time=start_date_time,
                )
                # Reason why the code is put down here, is to help readability a bit, to override the previous function
                if group_by == "booking" and latest_booking_status[
                    0
                ].booking_status in ["Check In", "Check Out", "Overstay"]:
                    need_reverse = False

            # add payment date into sting list
            payment_date = transaction.transaction_datetime

            # add payment type list
            transaction_payment_type = (
                transaction.payment_type.payment_type
                if transaction.payment_type
                else ""
            )
            if transaction_payment_type not in payment_type_list:
                payment_type_list.append(transaction_payment_type)

            # add pan list
            transaction_pan = transaction.pan if transaction.pan else ""
            if transaction_pan not in pan_list:
                pan_list.append(transaction_pan)

            # add payment status list
            name_exists = False
            for item in payment_status_list:
                if item["transaction_status"] == transaction.transaction_status:
                    item["count"] += 1
                    name_exists = True
                    break
            if not name_exists:
                payment_status_list.append(
                    {"transaction_status": transaction.transaction_status, "count": 1}
                )

            # add sum
            transaction_sum += -1 * transaction.sum if need_reverse else transaction.sum

            total = (
                transaction.debit_amount
                if transaction.debit_amount != 0
                else transaction.credit_amount
            )

            transaction_debit_credit_sum += (
                -1 * total if need_reverse and transaction.debit_amount > 0 else total
            )

            # add tax
            tax = transaction.tax_amount if transaction.tax_amount else 0
            transaction_tax += -1 * tax if need_reverse else tax

            # add promo
            promo = transaction.promotion_amount if transaction.promotion_amount else 0
            promotions += -1 * promo if need_reverse else promo

            # add adjustments
            adjustment = (
                transaction.adjustements_amount
                if transaction.adjustements_amount
                else 0
            )
            adjustments += -1 * adjustment if need_reverse else adjustment

            # add service charge
            sst = (
                transaction.service_charge_amount
                if transaction.service_charge_amount
                else 0
            )
            service_charge += -1 * sst if need_reverse else sst

            # add rounding
            rounding = transaction.rounding if transaction.rounding else 0

            roundings += -1 * rounding if need_reverse else rounding

            # Check if the list is not empty and the first element is not empty
            if transaction.adjustments and transaction.adjustments[0]:
                adjustment_string.append(transaction.adjustments)

            items = transaction.items

            if group_by == "transaction":
                # Loop through all items and sum quantities if category matches
                room_booking_length += sum(
                    int(item["quantity"])
                    for item in items
                    if item["category"] == TransactionItemCategory.ROOM_SALES
                )
            elif group_by == "booking":
                # Use the length of the booking list
                room_booking_length = len(room_booking_list)

        payment_type_list = [i for i in payment_type_list if i != ""]
        method = ""
        for payment_type in payment_type_list:
            method = f"{method}{'/' if method != '' else ''}{payment_type}"

        pan_list = [i for i in pan_list if i != ""]
        pan = ""
        for pan_data in pan_list:
            pan = f"{pan}{'/' if pan != '' else ''}{pan_data}"

        payment_status = ""
        for payment_status_data in payment_status_list:
            payment_status = f"{payment_status}{'/' if payment_status != '' else ''}{payment_status_data['count']} {payment_status_data['transaction_status']}"

        interval = (
            latest_booking_status[0].check_out_datetime
            - latest_booking_status[0].check_in_datetime
        )
        interval_hours = int(interval.total_seconds() / 3600)

        room_types = []
        for room_booking in room_booking_list:
            room_booking_type = room_booking.room.room_type.type_name
            name_exist = False
            for type in room_types:
                if room_booking_type == type["name"]:
                    type["count"] += 1
                    name_exist = True
                    break

            if not name_exist:
                room_types.append({"name": room_booking_type, "count": 1})
        room_type = ""
        for type in room_types:
            room_type = f"{room_type}{'/' if room_type != '' else ''}{type['count']} {type['name']}"

        is_pos = True

        if (
            len(room_booking_list) > 0
            or len(locker_booking_list) > 0
            or len(shower_booking_list) > 0
        ):
            is_pos = False

        no_show_date = None

        if latest_booking_status[0].has_no_show:
            initial_confirm_booking = BookingStatus.objects.filter(
                booking=booking, booking_status="Confirm Booking"
            ).order_by("status_datetime")
            no_show_date = initial_confirm_booking[0].check_in_datetime

        data["transfer_from_to"] = transfer_info
        data["booking_date"] = booking.booking_made_datetime
        data["payment_date"] = payment_date
        data["method"] = method if method != "" else "-"
        data["pan"] = pan if pan != "" else "-"
        data["payment_status"] = payment_status
        data["booking_status"] = latest_booking_status[0].booking_status
        data["check_in_date"] = latest_booking_status[0].check_in_datetime
        data["actual_check_in_date"] = (
            room_booking_list[0].actual_checkin_date_time
            if len(room_booking_list) > 0
            and room_booking_list[0].actual_checkin_date_time is not None
            else "-"
        )
        data["check_out_date"] = latest_booking_status[0].check_out_datetime
        data["actual_check_out_date"] = (
            room_booking_list[0].actual_checkout_date_time
            if len(room_booking_list) > 0
            and room_booking_list[0].actual_checkout_date_time is not None
            else "-"
        )
        data["ota"] = booking.ota_code if booking.ota_code else "-"
        data["source"] = booking.platform.platform
        data["guest"] = booking.customer_booked
        data["total_rooms"] = room_booking_length
        data["interval"] = interval_hours
        data["room_type"] = room_type if len(room_booking_list) > 0 else "-"
        data["sales"] = transaction_sum
        data["tax"] = transaction_tax
        data["promotions"] = promotions
        data["adjustments"] = adjustments
        data["adjustment_string"] = (
            ",".join(adjustment_string) if adjustment_string else "-"
        )
        data["service_charge"] = service_charge
        data["roundings"] = roundings
        data["total"] = transaction_debit_credit_sum
        data["booking_id"] = booking.pk
        data["booking_no"] = booking.booking_no
        data["booking_remarks"] = booking.details

        data["is_pos"] = is_pos
        data["no_show_date"] = no_show_date

        if (
            group_by == "transaction"
            and range_above_1_day is False
            and need_reverse is True
            and latest_booking_status[0].has_no_show
            and latest_booking_status[0].booking_status
            in ["Confirm Booking", "Check In", "Check Out", "Overstay"]
        ):
            temp_data = copy.deepcopy(data)
            for key, value in temp_data.items():
                if isinstance(value, (int, float, Decimal)):
                    temp_data[key] = value * -1

            no_show_transaction.append(temp_data)

    qs = qs + no_show_transaction

    qs = sorted(
        qs,
        key=lambda x: (
            x["method"]
            in ["Complimentary", "Recovery Services"],  # False comes before True
            x.get("booking_no", ""),
            (
                x.get("payment_date")
                if x.get("payment_date")
                else datetime.min.replace(tzinfo=pytz.UTC)
            ),
        ),
    )
    return qs


def calculate_detailed_data(qs):
    for data in qs:
        latest_booking_status = getattr(
            data["transactions"][0].booking, "latest_booking_status", None
        )

        tax_sum = 0
        service_charge_sum = 0
        latest_transaction_datetime = data["transactions"][0].transaction_datetime
        booking_datetime = data["transactions"][0].booking.booking_made_datetime
        booking_status = latest_booking_status[0].booking_status
        customer = data["transactions"][0].booking.customer_booked

        for transaction in data["transactions"]:
            tax_sum += float(transaction.tax_amount)
            service_charge_sum += float(transaction.service_charge_amount)

            transaction_datetime = transaction.transaction_datetime
            if transaction_datetime > latest_transaction_datetime:
                latest_transaction_datetime = transaction_datetime

        data["total_transaction"] = len(data["transactions"])
        data["total_tax_amount"] = tax_sum
        data["total_service_charge_amount"] = service_charge_sum
        data["latest_transaction_datetime"] = latest_transaction_datetime
        data["booking_datetime"] = booking_datetime
        data["booking_status"] = booking_status
        data["main_customer"] = customer

    return qs


def calculate_advanced_summary_data(qs, report_type):

    for data in qs:

        sales = 0
        sst = 0
        promotion = 0
        adjustment = 0
        roundings = 0
        for transaction in data["transactions"]:
            if transaction.is_room_booking:
                sales += transaction.sum
                sst += transaction.tax_amount
                promotion += transaction.promotion_amount
                adjustment += transaction.adjustements_amount
                roundings += transaction.rounding

        data["sales"] = sales
        data["sst"] = sst
        data["promotion"] = promotion
        data["adjustment"] = adjustment
        data["roundings"] = roundings
        data["total_deposit"] = sales + sst + roundings + adjustment - promotion

    return qs


def calculate_advanced_detalied_data(qs, report_type, group_by):
    for data in qs:

        sales = 0
        sst = 0
        promotion = 0
        adjustment = 0
        roundings = 0
        payment_date = None
        main_transaction = None
        for transaction in data["transactions"]:
            if transaction.is_room_booking:

                if payment_date and transaction.transaction_datetime:
                    payment_date = (
                        transaction.transaction_datetime
                        if payment_date < transaction.transaction_datetime
                        else payment_date
                    )

                if not payment_date:
                    payment_date = transaction.transaction_datetime

                sales += transaction.sum
                sst += transaction.tax_amount
                promotion += transaction.promotion_amount
                adjustment += transaction.adjustements_amount
                roundings += transaction.rounding

            if not main_transaction and transaction.is_room_booking:
                main_transaction = transaction

        booking = main_transaction.booking
        latest_booking_status = getattr(booking, "latest_booking_status", None)[0]
        room_booking_list = getattr(booking, "room_booking_list", None)

        malaysia_tz = pytz.timezone("Asia/Kuala_Lumpur")

        check_in_datetime = latest_booking_status.check_in_datetime.astimezone(
            malaysia_tz
        )

        check_in_date = check_in_datetime.date()
        booking_status = latest_booking_status.booking_status

        data["payment_date"] = payment_date
        data["check_in_date"] = check_in_date
        data["booking_id"] = booking.booking_no
        data["booking_status"] = booking_status
        data["sales"] = sales
        data["promotion"] = promotion
        data["adjustment"] = adjustment
        data["roundings"] = roundings
        data["sst"] = sst
        data["total_deposit"] = sales + sst + roundings + adjustment - promotion

        if report_type == "used" or report_type == "ledger":
            actual_check_in_datetime = room_booking_list[0].actual_checkin_date_time

            if actual_check_in_datetime:
                actual_check_in_date = actual_check_in_datetime.astimezone(
                    malaysia_tz
                ).date()
                data["actual_check_in_date"] = actual_check_in_date

    return qs


def calculate_net_amount(data):
    to_calculate = data.debit_amount if data.debit_amount != 0 else data.credit_amount
    # return (
    #     to_calculate
    #     - data.tax_amount
    #     - data.service_charge_amount
    #     - data.promotion_amount
    #     + data.rounding
    # )
    return to_calculate - data.tax_amount - data.service_charge_amount


def calculate_z_report_gateway_detailed(qs):
    """
    calculate and process data for detailed z-report for klia
    """
    new_array = []
    item_count = 0
    for index, data in enumerate(qs):
        item = item_count + 1
        type = ""
        id = ""
        details = []
        payment_date = ""
        check_in = "-"
        check_out = "-"
        sales = 0

        if data.transaction_datetime is None:
            continue

        payment_date = f"{payment_date}{', ' if payment_date != '' else ''}{data.transaction_datetime if data.transaction_datetime else '-'}"

        latest_booking_status = getattr(data.booking, "latest_booking_status", None)
        room_booking_list = getattr(data.booking, "room_booking_list", None)

        if latest_booking_status[0].booking_status == "Booked":
            continue

        locker_booking = LockerBooking.objects.filter(booking=data.booking)
        shower_booking = ShowerBooking.objects.filter(booking=data.booking)

        if data.is_room_booking:
            if latest_booking_status[0].booking_status != "No Show":
                type = "HOTEL"
                id = data.booking.booking_no

                customer_name = "-"

                customer_staying = data.booking.customer_staying

                if customer_staying is not None:
                    customer_name = (
                        data.booking.customer_staying.firstname
                        + " "
                        + data.booking.customer_staying.lastname
                    )

                details = [{"item_name": customer_name}]
                check_in = (
                    str(room_booking_list[0].actual_checkin_date_time)
                    if room_booking_list[0].actual_checkin_date_time
                    else str(latest_booking_status[0].check_in_datetime)
                )
                check_out = (
                    str(room_booking_list[0].actual_checkout_date_time)
                    if room_booking_list[0].actual_checkout_date_time
                    else str(latest_booking_status[0].check_out_datetime)
                )

                sales += calculate_net_amount(data)
            else:
                continue

        elif data.is_room_booking == False and len(room_booking_list) > 0:

            def has_add_1_hour(items):
                for item in items:
                    if item.get("item_type") == "Add 1 hour":
                        return True
                return False

            type = "POS"
            id = data.booking.booking_no
            details = (
                [{"item_name": data.adjustments}]
                if has_add_1_hour(data.items)
                else data.items
            )
            check_in = "N/A"
            check_out = "N/A"
            sales += calculate_net_amount(data)

        elif len(locker_booking) > 0:
            type = "LOCKER"
            id = data.booking.booking_no

            duration_hours = (
                latest_booking_status[0].check_out_datetime
                - latest_booking_status[0].check_in_datetime
            ).total_seconds() / 3600
            details = [{"item_name": str(int(duration_hours)) + " hrs"}]
            check_in = (
                str(locker_booking[0].locker_start_rent_datetime)
                if locker_booking[0].locker_start_rent_datetime
                else str(latest_booking_status[0].check_in_datetime)
            )
            check_out = (
                str(locker_booking[0].locker_end_rent_datetime)
                if locker_booking[0].locker_end_rent_datetime
                else str(latest_booking_status[0].check_out_datetime)
            )

            sales += calculate_net_amount(data)

        elif len(shower_booking) > 0:
            type = "SHOWER"
            id = data.booking.booking_no
            duration_hours = (
                latest_booking_status[0].check_out_datetime
                - latest_booking_status[0].check_in_datetime
            ).total_seconds() / 3600
            details = [{"item_name": str(int(duration_hours)) + " hrs"}]

            check_in = (
                str(shower_booking[0].shower_start_rent_datetime)
                if shower_booking[0].shower_start_rent_datetime
                else str(latest_booking_status[0].check_in_datetime)
            )
            check_out = (
                str(shower_booking[0].shower_end_rent_datetime)
                if shower_booking[0].shower_end_rent_datetime
                else str(latest_booking_status[0].check_out_datetime)
            )

            sales += calculate_net_amount(data)

        else:

            type = "POS"
            id = data.invoice_no
            details = data.items
            check_in = "N/A"
            check_out = "N/A"
            sales += calculate_net_amount(data)

        if data.transaction_status == "Refund":
            type = type + " (Refund)"

        new_data = {
            "item": item if item else "",
            "type": type if type else "",
            "id": id if id else "",
            "details": details if details else "",
            "payment_date": payment_date if payment_date else "",
            "check_in": check_in,
            "check_out": check_out,
            "sales": format(sales, ".2f"),
        }

        new_array.append(new_data)
        item_count += 1
    return new_array


def calculate_z_report_gateway_summary(qs):
    """
    calculate and process data for summary z-report for klia
    """
    new_array = []
    for index, data in enumerate(qs):
        item = index + 1
        date = ""
        room_sales = 0
        pos_sales = 0
        total = 0

        date = f"{date}{', ' if date != '' else ''}{data['date']}"

        transactions = data["transactions"]

        for transaction in transactions:
            latest_booking_status = getattr(
                transaction.booking, "latest_booking_status", None
            )

            if latest_booking_status is not None:
                if latest_booking_status[0].booking_status == "Booked":
                    continue

            if transaction.is_room_booking and latest_booking_status is not None:
                if latest_booking_status[0].booking_status == "No Show":
                    continue

                room_sales += calculate_net_amount(transaction)

            else:
                pos_sales += calculate_net_amount(transaction)

        total = room_sales + pos_sales

        new_data = {
            "item": item,
            "date": date if date else "",
            "room_sales": format(room_sales, ".2f"),
            "pos_sales": format(pos_sales, ".2f"),
            "total": format(total, ".2f"),
        }

        new_array.append(new_data)
    return new_array


# DEPRECATED: This function is deprecated as MAHB changes the format of the z report
def calculate_z_report_klia_salesfile(qs):
    """
    calculate and process data for detailed z-report for klia
    """
    new_array = []

    for data in qs:
        transaction_id = (
            data.booking.booking_no
            if data.booking.customer_booked != "Non Guest"
            else data.invoice_no
        )
        # total_amount = (
        #     data.debit_amount if data.debit_amount != 0 else data.credit_amount
        # )
        # tax_amount = data.tax_amount

        # Temporary since Z Report OTA are having weird
        lot_id = data.booking.lot_id
        tax_percentage = (
            Decimal(
                Settings.objects.filter(settings_category="Tax", lot=lot_id)
                .first()
                .settings_description[0]
            )
            / 100
        )
        debit_amount = Decimal(
            data.debit_amount if data.debit_amount != 0 else data.credit_amount
        )
        tax_amount = round(
            debit_amount - Decimal(debit_amount / (1 + tax_percentage)), 2
        )
        total_amount = debit_amount - tax_amount

        service_charge_amount = data.service_charge_amount
        status = ""
        sales_date = ""
        business_date = ""
        need_update_sales = False
        latest_booking_status = getattr(data.booking, "latest_booking_status", None)

        if latest_booking_status[0].booking_status == "Booked":
            continue

        if data.is_room_booking:
            if latest_booking_status[0].booking_status == "No Show":
                continue

        if data.transaction_status == "Paid":
            status = "COMPLETED"
        elif data.transaction_status == "Pending Payment":
            status = "PENDING"
        else:
            status = "CANCELLED"

        sales_date = f"{sales_date}{', ' if sales_date != '' else ''}{data.transaction_datetime if data.transaction_datetime is not None else '-'}"
        business_date = f"{business_date}{', ' if business_date != '' else ''}{data.transaction_datetime if data.transaction_datetime is not None else '-'}"

        new_data = {
            "transaction_id": transaction_id,
            "total_amount": total_amount,
            "service_charge_amount": service_charge_amount,
            "tax_amount": tax_amount,
            "status": status,
            "sales_date": sales_date,
            "business_date": business_date,
            "need_update_sales": need_update_sales,
        }

        new_array.append(new_data)
    return new_array


# DEPRECATED: This function is deprecated as MAHB changes the format of the z report
def calculate_z_report_klia_paymentfile(qs):
    """
    Return the new array
    calculate and process data for detailed z-report for klia
    """
    new_array = []

    for data in qs:
        transaction_id = (
            data.booking.booking_no
            if data.booking.customer_booked != "Non Guest"
            else data.invoice_no
        )
        amount = data.debit_amount if data.debit_amount != 0 else data.credit_amount
        currency_code = data.currency.currency_code
        currency_amount = 1
        currency_exchange_rate = 1
        payment_date = ""
        business_date = ""
        payment_method = (
            map_payment_method(data.payment_type.payment_method.payment_method)
            if data.payment_type
            else "-"
        )
        payment_type = map_payment_type(data.payment_type.payment_type)

        latest_booking_status = getattr(data.booking, "latest_booking_status", None)

        if latest_booking_status[0].booking_status == "Booked":
            continue

        if data.is_room_booking:
            if latest_booking_status[0].booking_status == "No Show":
                continue

        payment_date = f"{payment_date}{', ' if payment_date != '' else ''}{data.transaction_datetime if data.transaction_datetime else '-'}"
        business_date = f"{business_date}{', ' if business_date != '' else ''}{data.transaction_datetime if data.transaction_datetime else '-'}"

        new_data = {
            "transaction_id": transaction_id,
            "amount": amount,
            "currency_code": currency_code,
            "currency_amount": currency_amount,
            "currency_exchange_rate": currency_exchange_rate,
            "payment_date": payment_date,
            "business_date": business_date,
            "payment_method": payment_method,
            "payment_type": payment_type,
        }

        new_array.append(new_data)

    sorted_new_array = sorted(new_array, key=lambda x: x["payment_date"], reverse=True)
    return sorted_new_array


def get_staff_inhand_cash(shift_id):

    transactions = Transaction.objects.filter(
        shift_id=shift_id, payment_type__payment_type="Cash"
    ).exclude(transaction_status__in=["Void", "Pending Payment"])

    total_cash = 0
    for transaction_data in transactions:
        total_cash += transaction_data.debit_amount + transaction_data.credit_amount

    return total_cash


ct_logo_file_path = os.path.join(settings.BASE_DIR, "static", "images", "Logo.svg")
chg_logo_file_path = os.path.join(settings.BASE_DIR, "static", "images", "chg-logo.png")


def generate_invoice_context(transaction_id: str) -> Mapping[str, Any]:
    transaction = Transaction.objects.get(transaction_id=transaction_id)
    payment_type: PaymentType = transaction.payment_type
    booking: Booking = transaction.booking
    lot: Lot = booking.lot
    tax_setting = lot.settings_set.get(settings_category="Tax", settings_name="SST")
    room_bookings: List[RoomBooking] = booking.room_bookings.all()
    check_in_datetime = min(
        [room_booking.derived_check_in_datetime for room_booking in room_bookings],
        default=None,
    )
    check_out_datetime = max(
        [room_booking.derived_check_out_datetime for room_booking in room_bookings],
        default=None,
    )

    with open(ct_logo_file_path, "rb") as img_file:
        ct_logo = b64encode(img_file.read()).decode()
    with open(chg_logo_file_path, "rb") as img_file:
        chg_logo = b64encode(img_file.read()).decode()

    return {
        "capsuleLogo": ct_logo,
        "chgLogo": chg_logo,
        "capsuleHotelLocation": lot.full_name,
        "companyLegalName": f"{lot.lot_description} ({lot.reg_no})",
        "companySSTID": lot.sst_no,
        "sstPercentage": tax_setting.settings_description,
        "fullAddressInfo": lot.address,
        "contactInfo": f"{lot.tel} <EMAIL> capsuletransit.com chgworld.com",
        "billedTo": booking.customer_booked,
        "invoiceDate": (
            dt.strftime(transaction.transaction_datetime, "%Y-%m-%d")
            if transaction.transaction_datetime
            else "-"
        ),
        "invoiceNo": transaction.invoice_no,
        "items": transaction.items,
        "checkIn": (
            check_in_datetime.replace(tzinfo=pytz.utc)
            .astimezone(pytz.timezone("Asia/Shanghai"))
            .strftime("%Y-%m-%d %H:%M:%S")
            if check_in_datetime
            else "-"
        ),
        "checkOut": (
            check_out_datetime.replace(tzinfo=pytz.utc)
            .astimezone(pytz.timezone("Asia/Shanghai"))
            .strftime("%Y-%m-%d %H:%M:%S")
            if check_out_datetime
            else "-"
        ),
        "bookingNo": booking.booking_no,
        "bookingSource": booking.platform.platform,
        "taxAmount": f"{transaction.tax_amount:.2f}",
        "sumAmount": f"{transaction.sum + transaction.adjustements_amount:.2f}",
        "debitAmount": f"{transaction.debit_amount:.2f}",
        "creditAmount": f"{transaction.credit_amount:.2f}",
        "totalCharges": f"{transaction.debit_amount + transaction.credit_amount:.2f}",
        "rounding": f"{transaction.rounding:.2f}",
        "payment": payment_type.payment_type,
    }


def generate_invoice(request, transaction_id) -> Optional[str]:
    context = generate_invoice_context(transaction_id)
    content = render(request, "invoice.html", context).content

    # Get the PDF service endpoint, fallback to localhost for development
    pdf_service_endpoint = os.environ.get('HTML_TO_PDF_SERVICE_BASE_ENDPOINT', '').strip()
    if not pdf_service_endpoint:
        pdf_service_endpoint = 'http://localhost:3000'  # Default for development

    response = requests.post(
        f"{pdf_service_endpoint}/html2pdfraw",
        content,
        headers={"Content-Type": "text/html"},
    )

    if response.status_code == 200:
        data = response.json()
        return data["pdf"]


def generate_receipt(request, is_landing_page):

    try:

        if is_landing_page == True:
            serializer = GenerateReceiptSerializer(data=request.data)
            if not serializer.is_valid():
                return requests.Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            transaction = Transaction.objects.get(
                booking__booking_id=serializer.validated_data["booking_id"]
            )
        else:
            serializer = GenerateReceiptHMSSerializer(data=request.data)
            if not serializer.is_valid():
                return requests.Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            validated_data = serializer.validated_data

            # data = json.loads(request.body.decode("utf-8"))
            # transaction_id = data[0]["transactionId"]
            transaction = Transaction.objects.get(pk=validated_data["transaction_id"])

        credit_amount = transaction.credit_amount
        debit_amount = transaction.debit_amount
        payment_type = transaction.payment_type
        
        print('payment_type', payment_type)
        tax_amount = transaction.tax_amount
        
        print('transaction', transaction)
        print('transaction.transaction_datetime', transaction.transaction_datetime)
        print('transaction.transaction_datetime', transaction.debit_amount, transaction.credit_amount)
        payment_date = (
            transaction.transaction_datetime.replace(tzinfo=pytz.utc)
            .astimezone(pytz.timezone("Asia/Shanghai"))
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        change_amount = transaction.guest_change if (transaction.guest_change) else 0
        paid_amount = (
            transaction.guest_given
            if str(payment_type) == "Cash"
            else transaction.debit_amount + transaction.credit_amount
        )
        service_charge = transaction.service_charge_amount
        item_details = transaction.items
        is_room_booking = transaction.is_room_booking
        sum = transaction.sum

        booking = Booking.objects.get(booking_no=transaction.booking)

        booking_date = booking.booking_made_datetime
        platform = booking.platform
        guest = (
            booking.customer_staying
            if booking.customer_staying is not None
            else booking.customer_booked
        )
        invoice_no = booking.booking_no

        booking_id = booking.booking_id
        try:
            room_booking = RoomBooking.objects.get(booking=booking_id)
            room_code = room_booking.room
        except:
            room_code = 0

        lot_number = booking.lot
        lot = Lot.objects.get(lot_number=lot_number)

        lot_reg = lot.reg_no
        lot_sst = lot.sst_no
        lot_address = lot.address
        lot_tel = lot.tel
        try:
            locker = LockerBooking.objects.get(room_code=room_code)
            locker_no = locker.locker
        except:
            locker_no = "No Locker"

        booking_status = BookingStatus.objects.get(
            booking_id=booking_id, is_latest=True
        )
        check_in = booking_status.check_in_datetime
        check_out = booking_status.check_out_datetime
        formatted_check_in = (
            booking_status.check_in_datetime.replace(tzinfo=pytz.utc)
            .astimezone(pytz.timezone("Asia/Shanghai"))
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        formatted_check_out = (
            booking_status.check_out_datetime.replace(tzinfo=pytz.utc)
            .astimezone(pytz.timezone("Asia/Shanghai"))
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        duration = check_out - check_in
        duration = duration.total_seconds() / 3600

        room_sales_amount = 0
        for item in transaction.items:
            if (
                item.get("category", "") == TransactionItemCategory.ROOM_SALES
                or item.get("category", "") == TransactionItemCategory.ADJUSTMENT
            ):
                room_sales_amount += float(item.get("price", 0))

        promotion_amount = transaction.promotion_amount
        adjustment_amount = transaction.adjustements_amount
        rounding = transaction.rounding
    except json.JSONDecodeError as e:
        # Handle the decoding error
        print(f"JSON decoding error: {e}")

    template_context = {
        "lot_number": lot_number,
        "lot_reg": lot_reg,
        "lot_sst": lot_sst,
        "lot_address": lot_address,
        "lot_tel": lot_tel,
        "booking_date": booking_date,
        "room_code": room_code,
        "platform": platform,
        "guest": guest,
        "invoice_no": invoice_no,
        "credit_amount": credit_amount,
        "debit_amount": debit_amount,
        "payment_type": payment_type,
        "tax_amount": tax_amount,
        "change_amount": change_amount,
        "payment_date": payment_date,
        "paid_amount": paid_amount,
        "service_charge": service_charge,
        "item_details": item_details,
        "sum": sum,
        "sst": service_charge + tax_amount,
        "locker_no": locker_no,
        "check_in": formatted_check_in,
        "check_out": formatted_check_out,
        "interval": duration,
        "is_room_booking": is_room_booking,
        "room_sales_amount": room_sales_amount,
        "promotion_amount": promotion_amount,
        "adjustment_amount": adjustment_amount,
        "rounding": rounding,
    }

    html_content = render(request, "receipt.html", template_context).content

    # Get the PDF service endpoint, fallback to localhost for development
    pdf_service_endpoint = os.environ.get('HTML_TO_PDF_SERVICE_BASE_ENDPOINT', '').strip()
    if not pdf_service_endpoint:
        pdf_service_endpoint = 'http://localhost:3000'  # Default for development

    response = requests.post(
        f"{pdf_service_endpoint}/html2pdfraw",
        data=html_content,
        headers={"Content-Type": "text/html"},
    )
    if response.status_code == 200:
        data = response.json()
        pdf_b64string = data["pdf"]

        # Convert base64 string to bytes
        # pdf_bytes = base64.b64decode(pdf_b64string)

        return pdf_b64string.encode("utf-8")

        # Return PDF as a file attachment
        # response = HttpResponse(pdf_bytes, content_type="application/pdf")
        # response["Content-Disposition"] = 'attachment; filename="receipt.pdf"'
        # return response


def get_ledger_dynamic_filter(request):
    filter_type = request.GET.get("filterType", None)

    data = []

    if filter_type == "paymentMethod":
        payment_methods = PaymentMethod.objects.all()
        data = [
            {
                "id": payment_method.payment_method_id,
                "name": payment_method.payment_method,
            }
            for payment_method in payment_methods
        ]
    elif filter_type == "paymentType":
        payment_types = PaymentType.objects.all()
        data = [
            {
                "id": payment_type.payment_type_id,
                "name": payment_type.payment_type,
            }
            for payment_type in payment_types
        ]
    elif filter_type == "bookingPlatform":
        booking_platforms = Platform.objects.all()
        data = [
            {
                "id": str(booking_platform.platform_id),
                "name": booking_platform.platform,
            }
            for booking_platform in booking_platforms
        ]
    elif filter_type == "roomType":
        room_types = RoomType.objects.filter(roomzone__lot_id=request.user.lot_id)
        data = [
            {
                "id": str(room_type.type_id),
                "name": room_type.type_name,
            }
            for room_type in room_types
        ]
    elif filter_type == "promoCode":
        promo_codes = Promotion.objects.all()
        data = [
            {
                "id": str(promo_code.promotion_id),
                "name": promo_code.promo_code,
            }
            for promo_code in promo_codes
        ]
    else:
        raise Exception("Parameter is not Accepted")
    return data


def convert_to_report_format(qs):
    data = []

    for transaction in qs:
        latest_booking_status = getattr(
            transaction.booking, "latest_booking_status", None
        )

        duration = "-"

        if latest_booking_status is not None and len(latest_booking_status) > 0:
            duration = (
                latest_booking_status[0].check_out_datetime
                - latest_booking_status[0].check_in_datetime
            ).total_seconds() / 3600

        formatted_data = {
            "booking_id": (
                transaction.booking.booking_no
                if transaction.booking.booking_no is not None
                else "-"
            ),
            "booking_date": (
                transaction.booking.booking_made_datetime.isoformat()
                if transaction.booking.booking_made_datetime is not None
                else "-"
            ),
            "transaction_date": (
                transaction.transaction_datetime.isoformat()
                if transaction.transaction_datetime is not None
                else "-"
            ),
            "staff": transaction.shift.staffshift.staff.name,
            "payment_type": transaction.payment_type.payment_type,
            "pan": (transaction.pan if transaction.pan is not None else "-"),
            "payment_reference": (
                transaction.payment_reference
                if transaction.payment_reference is not None
                and transaction.payment_reference != ""
                else "-"
            ),
            "payment_remarks": (
                transaction.payment_remarks
                if transaction.payment_remarks is not None
                and transaction.payment_remarks != ""
                else "-"
            ),
            "transaction_status": transaction.transaction_status,
            "booking_status": (
                latest_booking_status[0].booking_status
                if latest_booking_status is not None and len(latest_booking_status) > 0
                else "-"
            ),
            "check_in_date": (
                latest_booking_status[0].check_in_datetime.isoformat()
                if latest_booking_status is not None and len(latest_booking_status) > 0
                else "-"
            ),
            "check_out_date": (
                latest_booking_status[0].check_out_datetime.isoformat()
                if latest_booking_status is not None and len(latest_booking_status) > 0
                else "-"
            ),
            "ota_ref_code": (
                transaction.booking.ota_code if transaction.booking.ota_code else "-"
            ),
            "booking_platform": transaction.booking.platform.platform,
            "guest": (
                transaction.customer.firstname + " " + transaction.customer.lastname
                if transaction.customer is not None
                else "-"
            ),
            "duration": duration,
            "sales": transaction.sum,
            "tax": transaction.tax_amount,
            "promotion": transaction.promotion_amount,
            "adjustment": transaction.adjustements_amount,
            "total": (
                transaction.debit_amount
                if transaction.debit_amount > 0
                else transaction.credit_amount
            ),
            "booking_remarks": (
                transaction.booking.details
                if transaction.booking.details is not None
                and transaction.booking.details != ""
                else "-"
            ),
        }

        data.append(formatted_data)

    return data


def check_need_reverse(
    booking_id: str,
    transaction_datetime: datetime,
    is_room_booking: bool,
    latest_booking_status: BookingStatus,
    room_booking: List[RoomBooking],
    range_above_1_day: bool,
    start_date_time: str = None,
):
    need_reverse = False
    if range_above_1_day is True:
        if latest_booking_status.has_no_show:
            need_reverse = False

    if (
        transaction_datetime
        and is_room_booking
        and len(room_booking) > 0
        and room_booking[0].actual_checkin_date_time is not None
        and range_above_1_day is False
    ):
        time_difference = abs(
            room_booking[0].actual_checkin_date_time
            - latest_booking_status.check_in_datetime
        )

        if (
            latest_booking_status.has_no_show
            or latest_booking_status.booking_status == "No Show"
            and time_difference > timedelta(days=1)
        ):

            booking_statuses = BookingStatus.objects.filter(
                booking=booking_id, booking_status__in=["No Show", "Check In"]
            ).order_by("check_in_datetime")

            # check_in_status = booking_statuses.filter(booking_status="Check In").first()

            earliest_no_show = booking_statuses[0]

            if (
                room_booking[0].actual_checkin_date_time + timedelta(hours=8)
            ).date() == (
                earliest_no_show.check_in_datetime + timedelta(hours=8)
            ).date():
                need_reverse = False
            else:
                need_reverse = True

        if start_date_time:
            start_date_time_date = (
                datetime.strptime(start_date_time, "%Y-%m-%dT%H:%M:%S.%fZ")
                + timedelta(hours=8)
            ).date()
            actual_check_in_date = (
                room_booking[0].actual_checkin_date_time + timedelta(hours=8)
            ).date()

            if start_date_time_date != actual_check_in_date:
                need_reverse = False

    return need_reverse


# def pay_pending_transaction_for_signal(transaction_id):

#     transaction_obj = Transaction.objects.get(pk = transaction_id)

#     transaction_obj.debit_amount = transaction_obj.credit_amount
#     transaction_obj.credit_amount = 0
#     transaction_obj.transaction_status = "Paid"
#     transaction_obj.transaction_datetime = timezone.now()
#     transaction_obj.payment_type_id = 1
#     transaction_obj.save()


def calculate_z_report_mahb(qs):
    qs = qs.filter(transaction_status__in=["Refund", "Paid"])

    if len(qs) < 1:
        return []

    lot = Lot.objects.filter(lot_id=qs[0].booking.lot_id).first()

    formatted_data = []

    for transaction in qs:

        if transaction.debit_amount == 0:
            continue

        room_booking_list = getattr(transaction.booking, "room_booking_list", None)
        latest_booking_status = getattr(
            transaction.booking, "latest_booking_status", None
        )[0]

        if (
            transaction.is_room_booking
            and latest_booking_status.booking_status == "No Show"
        ):
            continue

        store = lot.mahb_lot if lot.mahb_lot else "-"

        transaction_type = (
            "RETURN" if transaction.transaction_status == "Refund" else "SALES"
        )

        transaction_status = "V" if transaction.transaction_status == "Refund" else "P"

        transaction_id = transaction.invoice_no

        transaction_date = (
            room_booking_list[0].actual_checkin_date_time
            if room_booking_list is not None
            and len(room_booking_list) > 0
            and room_booking_list[0].actual_checkin_date_time
            else latest_booking_status.check_in_datetime
        )
        if transaction.transaction_status == "Refund":
            transaction_date = transaction.transaction_datetime

        transaction_total_amount = (
            transaction.debit_amount
            if transaction.debit_amount != 0
            else transaction.credit_amount
        )

        transaction_service_charge_amount = (
            transaction.service_charge_amount
            if transaction.service_charge_amount
            else 0
        )
        # transaction_rounding_amount = (
        #     round(transaction.rounding, 2) if transaction.rounding else 0
        # )
        transaction_rounding_amount = 0

        tax_percentage = find_tax_percentage(transaction)
        transaction_promotion_amount = float(transaction.promotion_amount)

        sales_item = []
        for item in transaction.items:
            if item.get("category") not in [
                TransactionItemCategory.TAX,
                TransactionItemCategory.SERVICE_CHARGE,
                TransactionItemCategory.PROMOTION,
            ]:
                item_name = (
                    item.get("item_name")
                    if "Overstay" in item.get("item_name")
                    else item.get("item_type")
                )
                tax_percentage = (
                    tax_percentage
                    if item.get("category") != TransactionItemCategory.ROUNDING
                    else 0
                )
                unit_price = float(item.get("price"))
                promo_discount = (
                    unit_price
                    if transaction_promotion_amount > abs(unit_price)
                    else transaction_promotion_amount
                )
                unit_price_after_promo = unit_price - promo_discount
                quantity = float(item.get("quantity", 1))
                tax_amount = (
                    unit_price_after_promo * quantity * tax_percentage
                    if item.get("category") != TransactionItemCategory.ROUNDING
                    else 0
                )
                line_tax_amount = round(tax_amount, 2)
                sub_total = (unit_price_after_promo * quantity) + line_tax_amount

                item_data = {
                    "item_code": item.get("item_name"),
                    "item_name": item_name,
                    "sold_qty": item.get("quantity", 1),
                    "unit_price": round(unit_price_after_promo, 2),
                    "sub_total": round(sub_total, 2),
                    "line_discount_amount": (
                        transaction_promotion_amount if len(sales_item) == 0 else 0
                    ),
                    "line_tax_percentage": tax_percentage,
                    "line_tax_amount": line_tax_amount,
                    "line_tax_type": "SST",
                    "product_dictionary_l1": "Services",
                    "product_dictionary_l2": "Transit Lounge",
                    "product_dictionary_l3": "Sleeping Pod",
                    "product_dictionary_l4": "",
                    "product_dictionary_l5": "",
                }
                transaction_promotion_amount -= promo_discount
                sales_item.append(item_data)
            # PROMOTION ARE NOW PUT INSIDE LINE_DISCOUNT
            # if item.get("category") == TransactionItemCategory.PROMOTION:
            #     item_data = {
            #         "item_code": item.get("item_name"),
            #         "item_name": item.get("item_type"),
            #         "sold_qty": 1,
            #         "unit_price": round(unit_price, 2),
            #         "sub_total": round(unit_price, 2),
            #         "line_discount_amount": round(unit_price, 2),
            #         "line_tax_percentage": 0,
            #         "line_tax_amount": 0,
            #         "line_tax_type": "SST",
            #         "product_dictionary_l1": "Services",
            #         "product_dictionary_l2": "Transit Lounge",
            #         "product_dictionary_l3": "Sleeping Pod",
            #         "product_dictionary_l4": "",
            #         "product_dictionary_l5": "",
            #     }
            #     sales_item.append(item_data)

        total_sub_total = sum(item["sub_total"] for item in sales_item)
        remainder = round(float(transaction_total_amount) - total_sub_total, 2)

        if remainder != 0:
            # Check if an item with item_code "Rounding" exists
            rounding_item = next(
                (item for item in sales_item if item["item_code"] == "Rounding"), None
            )

            if rounding_item:
                # Update the existing rounding item
                rounding_item["unit_price"] = round(
                    rounding_item["unit_price"] + remainder, 2
                )
                rounding_item["sub_total"] = round(
                    rounding_item["sub_total"] + remainder, 2
                )
            else:
                # Create a new rounding item
                rounding_item_data = {
                    "item_code": "Rounding",
                    "item_name": "Rounding",
                    "sold_qty": 1,
                    "unit_price": remainder,
                    "sub_total": remainder,
                    "line_discount_amount": 0,
                    "line_tax_percentage": 0,
                    "line_tax_amount": 0,
                    "line_tax_type": "SST",
                    "product_dictionary_l1": "Services",
                    "product_dictionary_l2": "Transit Lounge",
                    "product_dictionary_l3": "Sleeping Pod",
                    "product_dictionary_l4": "",
                    "product_dictionary_l5": "",
                }
                sales_item.append(rounding_item_data)

        collection = [
            {
                "method": (
                    map_payment_method_v2(
                        transaction.payment_type.payment_method.payment_method
                    )
                    if transaction.payment_type
                    else "Others"
                ),
                "payment_type": (
                    map_payment_type(transaction.payment_type.payment_type)
                    if transaction.payment_type
                    else "Others"
                ),
                "amount": transaction.debit_amount,
            }
        ]

        sales_data = {
            "store": store,
            "transaction_type": transaction_type,
            "transaction_status": transaction_status,
            "transaction_id": transaction_id,
            "transaction_date": transaction_date,
            "transaction_total_amount": transaction_total_amount,
            "transaction_service_charge_amount": transaction_service_charge_amount,
            "transaction_rounding_amount": transaction_rounding_amount,
            "sales_item": sales_item,
            "collection": collection,
        }

        formatted_data.append(sales_data)

    return formatted_data


def calculate_z_report_mahb_grouping(qs):
    grouped_data = defaultdict(lambda: {"total": 0})

    # Group by transaction date
    for transaction in qs:
        # Extract the transaction date
        transaction_date = (transaction["transaction_date"] + timedelta(hours=8)).date()

        # Aggregate unit prices and quantities from SalesItem
        sum = 0
        for item in transaction["sales_item"]:
            sum += float(item["unit_price"]) * float(item["sold_qty"])
            grouped_data[transaction_date]["total"] = round(
                grouped_data[transaction_date]["total"]
                + (float(item["unit_price"]) * float(item["sold_qty"])),
                2,
            )

    # Convert the grouped data to a list for better readability
    result = [
        {"transaction_date": date, **values} for date, values in grouped_data.items()
    ]

    sorted_data = sorted(result, key=lambda x: x["transaction_date"])

    return sorted_data
