import base64
from io import BytesIO
import ftplib
import json
import paramiko
import pdfkit, datetime, os
from django.http import JsonResponse
import pandas as pd
from requests import request as req


# Django receive the request from React Frontend
def z_report(request, lot_number=None):
    # receive data in multiarray format and arrange them in pandas dataframe
    data = []
    data_keys = []

    for dictionary in request.data["data"]:
        values_array = list(dictionary.values())
        data.append(values_array)

    data_keys = list(request.data["data"][0].keys())

    keys = []

    for key in data_keys:
        if key == "sales":
            keys.append("Sales(RM)")
            continue

        # Capitalize Words
        words = key.split("_")
        capitalized_words = [word.capitalize() for word in words]
        # Join the words with spaces
        readable_string = " ".join(capitalized_words)

        keys.append(readable_string)

    df = pd.DataFrame(
        data,
        columns=keys,
    )

    start_date = request.data["start_date"]
    end_date = request.data["end_date"]

    # Generate the PDF include the title Capsule HMS ZReport for (Today Date)
    html = f"""<html>
        <head>
            <style>
                table, th, td {{
                    border: 1px solid black;
                    border-collapse: collapse;
                }}
                th, td {{
                    padding: 15px;
                    text-align: left;
                }}
            </style>
        </head>
        <body>
            <h1>Capsule HMS ZReport for {start_date} - {end_date}</h1>
            <table>
                {df.to_html(index=False)}
            </table>
        </body>
    </html>"""

    # Get the PDF service endpoint, fallback to localhost for development
    pdf_service_endpoint = os.environ.get('HTML_TO_PDF_SERVICE_BASE_ENDPOINT', '').strip()
    if not pdf_service_endpoint:
        pdf_service_endpoint = 'http://localhost:3000'  # Default for development

    response = req(
        "post",
        f"{pdf_service_endpoint}/html2pdfraw",
        data=html,
        headers={"Content-Type": "text/html"},
    )
    if response.status_code == 200:
        data = response.json()
        pdf_b64string = data["pdf"]

        # Code to Save PDF in Project Folder
        # pdf_data = base64.b64decode(pdf_b64string)
        # with open("output.pdf", "wb") as f:
        #     f.write(pdf_data)

        if request.data.get("report_file") and request.data["report_file"] == True:
            sftp_upload(pdf_b64string, request.data.get("file_name"), lot_number)

        return pdf_b64string.encode("utf-8")


def z_report_txt(request, lot_number=None):
    data = request.data

    lot_number = data["lot_id"]

    if lot_number == 1:
        tenant_id = "L10201"
    if lot_number == 4:
        tenant_id = "L2M0603"

    with open("output.txt", "w") as file:
        if data["version"] == "salesFile" or data["version"] == "paymentFile":
            headers = data["data"][0].keys()

            for entry in data["data"]:
                row = [str(entry.get(header, "")) for header in headers]
                file.write("|".join(row) + "\n")
        else:
            for entry in data["data"]:
                line = f"{tenant_id},{datetime.datetime.strptime( entry['date'], '%Y-%m-%d').strftime('%d%m%Y')},{entry['total']:.2f}\n"
                file.write(line)

    # Read the content of the text file
    with open("output.txt", "rb") as file:
        file_content = file.read()

    # Encode the file content into Base64
    encoded_content = base64.b64encode(file_content).decode()

    if data.get("report_file") and data["report_file"] == True:
        sftp_upload(encoded_content, data.get("file_name"), lot_number=lot_number)

    return encoded_content.encode("utf-8")


def convertKeysToPascalCase(obj: any) -> any:
    if isinstance(obj, dict):
        return {
            "".join(
                word.capitalize() for word in key.split("_")
            ): convertKeysToPascalCase(value)
            for key, value in obj.items()
        }
    elif isinstance(obj, list):
        return [convertKeysToPascalCase(item) for item in obj]
    else:
        return obj


def z_report_mahb_json(data, file_name, lot_number):
    converted_data = []
    for item in data:
        new_item = convertKeysToPascalCase(item)
        converted_data.append(new_item)

    with open("mahb.json", "w") as file:
        json.dump(converted_data, file, indent=4)

    with open("mahb.json", "rb") as file:
        file_content = file.read()

    # Encode the file content into Base64
    encoded_content = base64.b64encode(file_content).decode()
    sftp_upload(encoded_content, file_name, lot_number=lot_number)


def sftp_upload(file, file_name, lot_number):
    file_content = base64.b64decode(file)

    server = ""
    tenant_id = ""
    password = ""
    port = ""

    lot_config = {
        1: "LANDSIDE_Z_REPORT",
        2: "AIRSIDE_Z_REPORT",
        3: "SLEEPLOUNGE_Z_REPORT",
        4: "MAX_Z_REPORT",
    }

    if lot_number in lot_config:
        prefix = lot_config[lot_number]
        server = os.environ[f"{prefix}_SERVER"]
        tenant_id = os.environ[f"{prefix}_USERNAME"]
        password = os.environ[f"{prefix}_PASSWORD"]
        port = int(os.environ[f"{prefix}_PORT"])

    if server and tenant_id and password:
        if port == 21:
            ftp = ftplib.FTP()
            ftp.connect(server, port)
            ftp.login(tenant_id, password)

            # Use BytesIO to handle the file content in memory
            with BytesIO(file_content) as file_obj:
                # Ensure the file pointer is at the beginning
                file_obj.seek(0)

                # Upload the file
                ftp.storbinary("STOR /" + file_name, file_obj)

            # Close the FTP connection
            ftp.quit()
        if port == 22:
            if ".pdf" in file_name:
                return

            transport = paramiko.Transport((server, port))

            try:
                # Connect to the server
                transport.connect(username=tenant_id, password=password)

                # Open an SFTP session
                sftp = paramiko.SFTPClient.from_transport(transport)

                remote_file_path = f"/{file_name}"

                with BytesIO(file_content) as file_obj:
                    file_obj.seek(0)
                    sftp.putfo(file_obj, remote_file_path)

            finally:
                sftp.close()
                transport.close()


def legacy_sftp_upload():
    # Create a new SSH client
    ftp = ftplib.FTP()
    ftp.connect("ftpgateway.wct.my", 21)
    ftp.login("L10201", "PUKEDUS")

    # Send the file to the remote server
    with open("L10201_22122020_ZRPT.pdf", "rb") as file:
        ftp.storbinary("STOR /L10201_22122020_ZRPT.pdf", file)

    # Close the connection
    ftp.quit()


# Django receive the request from React Frontend
def legacy_z_report(request):
    # receive data in multiarray format and arrange them in pandas dataframe
    # data = request.data
    # data = [ item, type, ID, details, Payment Date, Check In, Check Out, Sales(RM) ]
    data = [
        [
            "1",
            "Room",
            "R001",
            "Standard Room",
            "2020-12-01",
            "2020-12-01",
            "2020-12-02",
            "100.00",
        ],
        [
            "2",
            "Room",
            "R002",
            "Deluxe Room",
            "2020-12-01",
            "2020-12-01",
            "2020-12-02",
            "150.00",
        ],
        [
            "3",
            "Room",
            "R003",
            "Superior Room",
            "2020-12-01",
            "2020-12-01",
            "2020-12-02",
            "200.00",
        ],
    ]

    df = pd.DataFrame(
        data,
        columns=[
            "ID",
            "Type",
            "Name",
            "Details",
            "Payment Date",
            "Check In",
            "Check Out",
            "Sales",
        ],
    )
    df["Sales"] = df["Sales"].astype(float)
    df["Payment Date"] = pd.to_datetime(df["Payment Date"])
    df["Check In"] = pd.to_datetime(df["Check In"])
    df["Check Out"] = pd.to_datetime(df["Check Out"])

    # Generate the PDF include the title Capsule HMS ZReport for (Today Date)
    html = f"""
    <html>
        <head>
            <style>
                table, th, td {{
                    border: 1px solid black;
                    border-collapse: collapse;
                }}
                th, td {{
                    padding: 15px;
                    text-align: left;
                }}
            </style>
        </head>
        <body>
            <h1>Capsule HMS ZReport for {pd.to_datetime('today').strftime('%Y-%m-%d')}</h1>
            <table>
                {df.to_html(index=False)}
            </table>
        </body>
    </html>
    """

    # Convert the HTML to PDF
    current_date = datetime.datetime.now().strftime("%d%m%Y")
    pdf_filename = f"L10201_{current_date}_ZRPT.pdf"
    pdfkit.from_string(html, pdf_filename)

    # save to the desktop C:\gatewaysales
    pdfkit.from_string(html, "C:\\gatewaysales\\" + pdf_filename)

    # Upload the PDF to the SFTP server
    # sftp_upload()

    # Create a folder Sent in C:\gatewaysales if not exist and move the file to the folder
    if not os.path.exists("C:\\gatewaysales\\Sent"):
        os.makedirs("C:\\gatewaysales\\Sent")
    os.rename(
        "C:\\gatewaysales\\" + pdf_filename, "C:\\gatewaysales\\Sent\\" + pdf_filename
    )

    # Return a response to the frontend
    return JsonResponse(
        {"message": "Z-Report has been generated and uploaded to the server."}
    )
