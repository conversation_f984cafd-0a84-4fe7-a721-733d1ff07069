from transaction.utils import calculate_amount_percentage



def test_calculate_amount_percentage():
    # Test case 1: Calculate 10% of 100
    result1 = calculate_amount_percentage(100.0, 10)
    assert result1 == 10.0

    # Test case 2: Calculate 20% of 50
    result2 = calculate_amount_percentage(50.0, 20)
    assert result2 == 10.0

    # Test case 3: Calculate 0% of 75
    result3 = calculate_amount_percentage(75.0, 0)
    assert result3 == 0.0

    # Test case 4: Calculate 100% of 60
    result4 = calculate_amount_percentage(60.0, 100)
    assert result4 == 60.0

    # Test case 5: Calculate 25% of 0
    result5 = calculate_amount_percentage(0.0, 25)
    assert result5 == 0.0

    # Test case 6: Calculate 5% of -100
    result6 = calculate_amount_percentage(-100.0, 5)
    assert result6 == -5.0





