import django
from django.conf import settings

import pytest
from django.db import connections

from capsuletransit import settings as DJANGO_SETTINGS
from django.conf import settings
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT


def run_sql(sql):
    conn = psycopg2.connect(database='postgres')
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    cur.execute(sql)
    conn.close()

@pytest.fixture(scope='session')
def test():
    with open('test.txt', 'w') as f:
        f.write('test')

        
# @pytest.fixture(scope='session')
# def django_db_setup():
#     from django.conf import settings
    yield

    for connection in connections.all():
        connection.close()

    # run_sql(f'DROP DATABASE {test_db_name}')



from django.core.management import call_command

@pytest.fixture(scope='session')
def django_db_setup(django_db_setup, django_db_blocker):
    with django_db_blocker.unblock():
        call_command('loaddata', 'output.json')


# def pytest_configure():
#     settings_keys = dir(DJANGO_SETTINGS)
#     instance = {key: getattr(DJANGO_SETTINGS, key) for key in settings_keys if key.isupper()}

#     settings.configure(**instance)
#     django.setup()
    