from django.http import Http404
from django.db.models import Q, ExpressionWrapper, IntegerField, Sum
from merch.models import Merch
from merch.serializer import MerchSerializer
from transaction.models import Transaction as TransactionModel

from django.utils import timezone


def get_merch_list(request):
    lot = request.user.lot_id
    merchs = Merch.objects.all().order_by("item_name").filter(lot=lot)

    active_merch = merchs.filter(is_archived=False)
    inactive_merch = merchs.filter(is_archived=True)

    active_merch_num = active_merch.filter(is_archived=False).count()
    inactive_merch_num = inactive_merch.filter(is_archived=True).count()

    active_merch_stock = active_merch.exclude(
        item_details__in=["Locker", "Shower"]
    ).aggregate(
        total_stock_difference=ExpressionWrapper(
            Sum("stock_in_quantity", output_field=IntegerField())
            - Sum("stock_out_quantity", output_field=IntegerField()),
            output_field=IntegerField(),
        )
    )[
        "total_stock_difference"
    ]

    inactive_merch_stock = inactive_merch.exclude(
        item_details__in=["Locker", "Shower"]
    ).aggregate(
        total_stock_difference=ExpressionWrapper(
            Sum("stock_in_quantity", output_field=IntegerField())
            - Sum("stock_out_quantity", output_field=IntegerField()),
            output_field=IntegerField(),
        )
    )[
        "total_stock_difference"
    ]

    if request.query_params.get("search"):
        search_query = request.query_params.get("search")
        merchs = merchs.filter(
            Q(item_name__icontains=search_query)
            | Q(item_details__icontains=search_query)
        )
    if request.query_params.get("sku"):
        search_query = request.query_params.get("sku")
        merchs = merchs.filter(Q(sku=search_query) | Q(qr_code=search_query.lower()))

    no_archived = request.query_params.get("no_archived")
    if no_archived == "true":
        merchs = merchs.filter(is_archived=False)

    serializer = MerchSerializer(merchs, many=True)
    return (
        serializer.data,
        active_merch_num,
        inactive_merch_num,
        active_merch_stock,
        inactive_merch_stock,
    )


def get_merch(request, **kwargs):
    try:
        merch = Merch.objects.get(**kwargs)
    except Merch.DoesNotExist as e:
        raise Http404("Merch does not exist")
    serializer = MerchSerializer(merch)
    return serializer.data


def create_and_save_merch(request):
    serializer = MerchSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(lot=request.user.lot_id)
    else:
        raise Exception(serializer.errors)


def update_and_save_merch(request, merch_id):
    try:
        merch = Merch.objects.get(merch_id=merch_id)
    except Merch.DoesNotExist as e:
        raise Http404("Merch does not exist")
    serializer = MerchSerializer(merch, data=request.data)
    if serializer.is_valid():
        serializer.save()
    else:
        raise Exception(serializer.errors)


# def delete_merch_from_db(request, merch_id):
#   try:
#     merch = Merch.objects.get(merch_id=merch_id)
#   except Merch.DoesNotExist as e:
#     raise Http404("Merch does not exist")
#   merch.delete()


def archive_merch_service(request, merch_id):
    try:
        merch = Merch.objects.get(merch_id=merch_id)
    except Merch.DoesNotExist as e:
        raise Http404("Merch does not exist")
    merch.is_archived = not merch.is_archived
    merch.save()


def deduct_merch_after_transaction(transaction_data: TransactionModel):

    deducted_merch_list = []
    for item in transaction_data.items:
        if item["item_type"] == "Merch":
            deducted_merch_list.append(
                {"id": item["item_id"], "qty": int(item["quantity"])}
            )

    if len(deducted_merch_list) == 0:
        return

    merch_ids = [item["id"] for item in deducted_merch_list]

    merch_list = Merch.objects.filter(merch_id__in=merch_ids)

    for merch in merch_list:
        for item in deducted_merch_list:
            if str(merch.pk) == item["id"]:
                merch.stock_out_quantity += item["qty"]
                merch.save()
