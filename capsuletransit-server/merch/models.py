from uuid import uuid4

from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import RESTRICT, ForeignKey
from lot.models import Lot


class Merch(models.Model):
    merch_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Merch Booking ID",
    )
    item_name = models.CharField(max_length=100)
    item_details = models.CharField(max_length=100)
    qr_code = models.CharField(unique=True, max_length=255)
    sku = models.CharField(max_length=20, unique=True)
    sum = models.DecimalField(max_digits=100, decimal_places=2, help_text="guest price")
    non_guest_sum = models.DecimalField(
        max_digits=100, decimal_places=2, help_text="non-guest price", default=0.0
    )
    cost = models.DecimalField(
        max_digits=100, decimal_places=2, help_text="cost of product", default=0.0
    )
    stock_in_quantity = models.IntegerField(
        null=False, default=0, validators=[MinValueValidator(0)]
    )
    stock_out_quantity = models.IntegerField(
        null=False, default=0, validators=[MinValueValidator(0)]
    )
    last_updated = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False, null=True, blank=True)
    lot = ForeignKey(Lot, RESTRICT, related_name="merch")

    class Meta:
        unique_together = (("item_name", "lot"), ("sku", "lot"))

    def available_quantity(self):
        return self.stock_in_quantity - self.stock_out_quantity
