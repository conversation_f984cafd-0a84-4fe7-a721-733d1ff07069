# Generated by Django 4.2.3 on 2024-03-07 17:25

import django.core.validators
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Merch",
            fields=[
                (
                    "merch_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Merch Booking ID",
                    ),
                ),
                ("item_name", models.CharField(max_length=100, unique=True)),
                ("item_details", models.CharField(max_length=100)),
                ("qr_code", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("sku", models.CharField(max_length=20, unique=True)),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2, help_text="guest price", max_digits=100
                    ),
                ),
                (
                    "non_guest_sum",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="non-guest price",
                        max_digits=100,
                    ),
                ),
                (
                    "cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="cost of product",
                        max_digits=100,
                    ),
                ),
                (
                    "stock_in_quantity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "stock_out_quantity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "is_archived",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
            ],
        ),
    ]
