from django.db import transaction
from django.shortcuts import get_object_or_404, render
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.http import JsonResponse
from django.db import IntegrityError
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from merch.models import Merch
from merch.serializer import MerchSerializer

from djangorestframework_camel_case.parser import (
    CamelCaseMultiPartParser,
    CamelCaseFormParser,
)
from djangorestframework_camel_case.render import CamelCaseJSONRenderer

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from merch.services.merch import (
    archive_merch_service,
    create_and_save_merch,
    get_merch,
    get_merch_list,
    update_and_save_merch,
)


class MerchViewSet(viewsets.ViewSet):  # defunct
    queryset = Merch.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = MerchSerializer
    parser_classes = (CamelCase<PERSON>ult<PERSON><PERSON><PERSON>Parser, CamelCaseFormParser)
    renderer_classes = (CamelCaseJ<PERSON><PERSON>enderer,)

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-merch",
        url_name="get-all-merch",
        name="get_all_merch",
    )
    def get_all_merch(self, request):
        try:
            merch = get_merch_list(request)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"merch": merch, "message": "Retrieved all merch"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-merch-by-id/(?P<merch_id>[a-zA-Z0-9-]+)",
        url_name="get-merch-by-id",
        name="get_merch_by_id",
    )
    def get_merch_by_id(self, request, merch_id):
        try:
            merch = get_merch(request, merch_id=merch_id, is_archived=False)
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"merch": merch, "message": "Retrieved merch"}, status=status.HTTP_200_OK
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-merch",
        url_name="create-merch",
        name="create_merch",
    )
    def create_merch(self, request):
        try:
            with transaction.atomic():
                merch = create_and_save_merch(request)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"merch": merch, "message": "Created merch"}, status=status.HTTP_200_OK
        )

    @action(
        methods=["PUT", "PATCH"],
        detail=False,
        url_path="update-merch/(?P<merch_id>[a-zA-Z0-9-]+)",
        url_name="update-merch",
        name="update_merch",
    )
    def update_merch(self, request, merch_id):
        try:
            merch = update_and_save_merch(request, merch_id)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"merch": merch, "message": "Updated merch"}, status=status.HTTP_200_OK
        )

    # @action(
    #     methods=["DELETE"],
    #     detail=False,
    #     url_path="update-merch/(?P<merch_id>[a-zA-Z0-9-]+)",
    #     url_name="update-merch",
    #     name="update_merch",
    # )
    # def delete_merch(self, request, merch_id):
    #   try:
    #     delete_merch_from_db(request, merch_id)
    #   except Exception as e:
    #     return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
    #   return Response({
    #     "message": "Deleted merch"
    #   }, status=status.HTTP_200_OK)

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-merch-by-sku/(?P<sku>[a-zA-Z0-9-]+)",
        url_name="get-merch-by-sku",
        name="get_merch_by_sku",
    )
    def get_merch_by_sku(self, request, sku):
        try:
            merch = get_merch(request, sku=sku, is_archived=False)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"merch": merch, "message": "Retrieved merch"}, status=status.HTTP_200_OK
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="archive-merch/(?P<merch_id>[a-zA-Z0-9-]+)",
        url_name="archive-merch",
        name="archive_merch",
    )
    def archive_merch(self, request, merch_id):
        try:
            archive_merch_service(request, merch_id)
        except Merch.DoesNotExist as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)


class MerchModelViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = MerchSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseMultiPartParser, CamelCaseFormParser)
    lookup_field = "merch_id"

    def get_queryset(self):
        return Merch.objects.filter(is_archived=False)

    def list(self, request):
        try:
            (
                merchs,
                active_merch_num,
                inactive_merch_num,
                active_merch_stock,
                inactive_merch_stock,
            ) = get_merch_list(request)
        except Exception as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {
                "merchs": merchs,
                "active_merch_num": active_merch_num,
                "inactive_merch_num": inactive_merch_num,
                "active_merch_stock": active_merch_stock,
                "inactive_merch_stock": inactive_merch_stock,
                "message": "Retrieved all merch",
            },
            status=status.HTTP_200_OK,
        )

    def create(self, request):
        serializer = MerchSerializer(data=request.data)
        if serializer.is_valid():
            try:
                model = serializer.save()
                model.save()
                return Response(
                    {"status": "success", "message": "new merch registered"},
                    status=status.HTTP_201_CREATED,
                )
            except IntegrityError as e:
                return Response(
                    {"status": "error", "message": "Duplicate QR code"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, merch_id=None):
        queryset = Merch.objects.all()
        customer = get_object_or_404(queryset, pk=merch_id)
        serializer = MerchSerializer(customer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, merch_id=None):
        try:
            archive_merch_service(request, merch_id)
        except Merch.DoesNotExist as e:
            return Response({"message": e}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Merch Modified"}, status=status.HTTP_200_OK)

    @swagger_auto_schema(request_body=MerchSerializer, responses={200: "success"})
    @action(
        methods=["PUT"],
        detail=False,
        url_path="archive/(?P<merch_id>[0-9a-f-]+)",
        url_name="archive",
        permission_classes=[],
    )
    def archive_merch(self, request, merch_id):
        try:
            merch = Merch.objects.get(pk=merch_id)
            merch.is_archived = True
            merch.save()
            return Response(
                {"status": "success", "msg": "merch archive success"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(request_body=MerchSerializer, responses={200: "success"})
    @action(
        methods=["PUT"],
        detail=False,
        url_path="un-archive/(?P<merch_id>[0-9a-f-]+)",
        url_name="un-archive",
        permission_classes=[],
    )
    def unarchive_merch(self, request, merch_id):
        try:
            merch = Merch.objects.get(pk=merch_id)
            merch.is_archived = False
            merch.save()
            return Response(
                {"status": "success", "msg": "unarchive merch success"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
