from factory.django import DjangoModelFactory
from factory import Faker, Iterator, Sequence, fuzzy, lazy_attribute
from factory.faker import faker as faker

from guests.models import Customer

from .models import Platform, PLATFORM_TYPE, Booking

PLATFORM_TYPE_ARR = [x[0] for x in PLATFORM_TYPE]
class PlatformFactory(DjangoModelFactory):
    class Meta:
        model = Platform

    platform_id = Faker("uuid4")
    platform = Iterator(PLATFORM_TYPE_ARR)
    is_archive = False


class BookingFactory(DjangoModelFactory):
    class Meta:
        model = Booking

    booking_id = Faker("uuid4")
    booking_no = Sequence(lambda n: "CKLIA-%d" % (343000 + n))

    @lazy_attribute
    def booking_made_datetime(self):
        fake = faker.Faker()
        return fake.date_time_between(start_date="today -1 month", end_date="today +1 moenth")
    platform_id = fuzzy.FuzzyChoice(Platform.objects.all())
    customer_staying = fuzzy.FuzzyChoice(Customer.objects.all())
    customer_booked = f"{Faker('firstname')} {Faker('lastname')}"

    @lazy_attribute
    def adult_pax(self):
        fake = faker.Faker()
        return fake.random_number(digits=3)
    
    @lazy_attribute
    def child_pax(self):
        fake = faker.Faker()
        return fake.random_number(digits=3)
    details = Faker("sentence", nb_words=15)

    @lazy_attribute
    def sum(self):
        fake = faker.Faker()
        return fake.randomize_nb_elements(max=200, min=10)

