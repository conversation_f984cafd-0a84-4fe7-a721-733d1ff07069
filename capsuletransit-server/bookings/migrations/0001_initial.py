# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("bookingplatform", "0001_initial"),
        ("lockers", "0001_initial"),
        ("guests", "0001_initial"),
        ("merch", "0001_initial"),
        ("lot", "0001_initial"),
        ("rooms", "0001_initial"),
        ("shower", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Booking",
            fields=[
                (
                    "booking_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Booking ID",
                    ),
                ),
                (
                    "booking_no",
                    models.CharField(
                        editable=False,
                        max_length=20,
                        null=True,
                        unique=True,
                        verbose_name="Booking Number",
                    ),
                ),
                (
                    "booking_made_datetime",
                    models.DateTimeField(
                        editable=False, verbose_name="Datetime of the Booking Made"
                    ),
                ),
                ("last_updated_at", models.DateTimeField(auto_now=True)),
                ("added_at", models.DateTimeField(auto_now_add=True)),
                (
                    "customer_booked",
                    models.CharField(
                        blank=True,
                        help_text="placeholder name for customer who booked via Hotel Website or OTA",
                        max_length=100,
                        null=True,
                        verbose_name="Booking Customer",
                    ),
                ),
                (
                    "adult_pax",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Number of Adults",
                    ),
                ),
                (
                    "child_pax",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Number of Children",
                    ),
                ),
                (
                    "details",
                    models.CharField(
                        blank=True,
                        help_text="details of all relevant booking items in a string",
                        max_length=350,
                        null=True,
                        verbose_name="Details",
                    ),
                ),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="total amount needed to be paid",
                        max_digits=10,
                        verbose_name="Sum",
                    ),
                ),
                (
                    "ota_code",
                    models.CharField(
                        default="-", max_length=255, null=True, verbose_name="OTA Code"
                    ),
                ),
                (
                    "customer_staying",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.customer",
                        verbose_name="Customer Staying ID",
                    ),
                ),
                (
                    "lot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT, to="lot.lot"
                    ),
                ),
                (
                    "platform",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="bookingplatform.platform",
                        verbose_name="Booking Platform",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BookingNumberCounter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("branch_name", models.CharField(max_length=100, unique=True)),
                ("counter", models.PositiveIntegerField()),
                ("last_updated", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="TransferBooking",
            fields=[
                (
                    "transfer_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "from_booking",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="bookings.booking",
                    ),
                ),
                (
                    "to_booking",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="transferred_bookings",
                        to="bookings.booking",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ShowerBooking",
            fields=[
                (
                    "shower_booking_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="total amount of price paid for this shower if booked individually",
                        max_digits=100,
                        verbose_name="Price Paid Sum",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Check In", "Check In"),
                            ("Check Out", "Check Out"),
                            ("Cancelled", "Cancelled"),
                            ("Booked", "Booked"),
                            ("Confirm Booking", "Confirm Booking"),
                            ("No Show", "No Show"),
                            ("Transfer from Airside", "Transfer from Airside"),
                            ("Transfer from Landslide", "Transfer from Landslide"),
                            ("Reservation", "Reservation"),
                        ],
                        default="Booked",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "shower_start_rent_datetime",
                    models.DateTimeField(verbose_name="Shower Start Rent Datetime"),
                ),
                (
                    "shower_end_rent_datetime",
                    models.DateTimeField(verbose_name="Shower End Rent Datetime"),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="shower_bookings",
                        to="bookings.booking",
                        verbose_name="Booking ID",
                    ),
                ),
                (
                    "shower",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="shower.shower",
                        verbose_name="Shower ID",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RoomBooking",
            fields=[
                (
                    "room_booking_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Room Booking ID",
                    ),
                ),
                (
                    "details",
                    models.CharField(max_length=100, null=True, verbose_name="Details"),
                ),
                (
                    "actual_checkin_date_time",
                    models.DateTimeField(
                        null=True, verbose_name="Actual Checkin Datetime"
                    ),
                ),
                (
                    "actual_checkout_date_time",
                    models.DateTimeField(
                        null=True, verbose_name="Actual Checkout Datetime"
                    ),
                ),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="record total amount of price paid from this room booking",
                        max_digits=100,
                        verbose_name="Price Paid Sum",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Check In", "Check In"),
                            ("Check Out", "Check Out"),
                            ("Booked", "Booked"),
                            ("Confirm Booking", "Confirm Booking"),
                            ("Overstayed", "Overstayed"),
                            ("No Show", "No Show"),
                            ("Transfer from Airside", "Transfer from Airside"),
                            ("Transfer from Landside", "Transfer from Landside"),
                            ("Reservation", "Reservation"),
                        ],
                        default="Booked",
                        help_text="determine room booking status",
                        max_length=50,
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="room_bookings",
                        to="bookings.booking",
                        verbose_name="Booking ID",
                    ),
                ),
                (
                    "person_in_charge",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.customer",
                        verbose_name="Person In Charge",
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="room_bookings",
                        to="rooms.room",
                        verbose_name="Room ID",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MerchBooking",
            fields=[
                (
                    "merch_booking_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "quantity",
                    models.IntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Pending", "Pending"),
                            ("Paid", "Paid"),
                            ("Cancelled", "Cancelled"),
                        ],
                        default="Pending",
                        max_length=50,
                    ),
                ),
                ("purchase_datetime", models.DateTimeField(auto_now_add=True)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="bookings.booking",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.customer",
                    ),
                ),
                (
                    "merch",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT, to="merch.merch"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="LockerBooking",
            fields=[
                (
                    "locker_booking_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "sum",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="total amount of price paid for this locker if booked individually",
                        max_digits=100,
                        verbose_name="Price Paid Sum",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Check In", "Check In"),
                            ("Check Out", "Check Out"),
                            ("Cancelled", "Cancelled"),
                            ("Booked", "Booked"),
                            ("Confirm Booking", "Confirm Booking"),
                            ("No Show", "No Show"),
                            ("Transfer from Airside", "Transfer from Airside"),
                            ("Transfer from Landslide", "Transfer from Landslide"),
                            ("Reservation", "Reservation"),
                        ],
                        default="Booked",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "locker_start_rent_datetime",
                    models.DateTimeField(verbose_name="Locker Start Rent Datetime"),
                ),
                (
                    "locker_end_rent_datetime",
                    models.DateTimeField(verbose_name="Locker End Rent Datetime"),
                ),
                ("room_code", models.CharField(default="", max_length=50)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="locker_bookings",
                        to="bookings.booking",
                    ),
                ),
                (
                    "locker",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="lockers.locker",
                        verbose_name="Locker ID",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ExtraGuest",
            fields=[
                (
                    "extra_guest_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("id_no", models.CharField(max_length=15)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="bookings.booking",
                    ),
                ),
                (
                    "id_type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.idtype",
                        verbose_name="ID Type",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ConsentSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("session_id", models.UUIDField(default=uuid.uuid4)),
                (
                    "consent_form",
                    models.BinaryField(
                        default=None,
                        help_text="image of signature converted to binary from checkin",
                        null=True,
                    ),
                ),
                ("pin", models.CharField(max_length=6)),
                (
                    "last_updated",
                    models.DateTimeField(
                        editable=False, verbose_name="Datetime of last update"
                    ),
                ),
                (
                    "cashier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ConsentForm",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        editable=False, verbose_name="Datetime of ConsentForm Made"
                    ),
                ),
                (
                    "consent_checkin",
                    models.BinaryField(
                        default=None,
                        help_text="image of signature converted to binary from checkin",
                        null=True,
                    ),
                ),
                (
                    "consent_checkout",
                    models.BinaryField(
                        default=None,
                        help_text="image of signature converted to binary from checkin",
                        null=True,
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="bookings.booking",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BookingStatus",
            fields=[
                (
                    "booking_status_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Booking Status ID",
                    ),
                ),
                (
                    "booking_status",
                    models.CharField(
                        choices=[
                            ("Check In", "Check In"),
                            ("Check Out", "Check Out"),
                            ("Cancelled", "Cancelled"),
                            ("Booked", "Booked"),
                            ("Confirm Booking", "Confirm Booking"),
                            ("No Show", "No Show"),
                            ("Transfer from Airside", "Transfer from Airside"),
                            ("Transfer from Landslide", "Transfer from Landslide"),
                            ("Reservation", "Reservation"),
                        ],
                        max_length=50,
                        verbose_name="Booking Status",
                    ),
                ),
                (
                    "check_in_datetime",
                    models.DateTimeField(verbose_name="Booking Check in Datetime"),
                ),
                (
                    "check_out_datetime",
                    models.DateTimeField(verbose_name="Booking Check out Datetime"),
                ),
                (
                    "max_check_out_datetime",
                    models.DateTimeField(
                        verbose_name="Maximum booking Check out Datetime"
                    ),
                ),
                (
                    "status_datetime",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="The Datetime the Booking Status is generated",
                        verbose_name="Status Datetime Made",
                    ),
                ),
                (
                    "is_latest",
                    models.BooleanField(
                        default=True, verbose_name="Is the Booking Status the Latest"
                    ),
                ),
                (
                    "has_no_show",
                    models.BooleanField(
                        default=False,
                        help_text="to indicate the booking has already no show",
                        verbose_name="Has no show",
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="booking_status",
                        to="bookings.booking",
                        verbose_name="Booking ID",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BookingSessionRoomLock",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expiry_at", models.DateTimeField()),
                ("token", models.CharField(max_length=5, null=True)),
                (
                    "room",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="session_lock",
                        to="rooms.room",
                    ),
                ),
            ],
        ),
    ]
