# Generated by Django 4.2.3 on 2024-03-21 08:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bookings', '0004_roombooking_staah_room_reservation_id_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='booking',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='booking',
            name='is_cancelled',
            field=models.BooleanField(default=False, verbose_name='Is Cancelled'),
        ),
        migrations.AddConstraint(
            model_name='booking',
            constraint=models.UniqueConstraint(condition=models.Q(('is_cancelled', False)), fields=('platform', 'ota_code'), name='unique_ota_code_platform'),
        ),
    ]
