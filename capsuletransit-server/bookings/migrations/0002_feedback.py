# Generated by Django 4.2.3 on 2024-03-08 08:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("bookings", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="FeedBack",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("rating", models.<PERSON>r<PERSON>ield(max_length=20)),
                ("feedback", models.Char<PERSON>ield(max_length=200)),
                ("room_selected", models.Char<PERSON>ield(max_length=50)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="booking",
                        to="bookings.booking",
                    ),
                ),
            ],
        ),
    ]
