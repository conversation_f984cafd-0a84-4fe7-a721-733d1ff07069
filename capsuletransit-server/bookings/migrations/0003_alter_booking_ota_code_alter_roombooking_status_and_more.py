# Generated by Django 4.2.3 on 2024-03-18 10:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bookingplatform', '0002_platform_can_late_payment'),
        ('bookings', '0002_feedback'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='booking',
            name='ota_code',
            field=models.CharField(max_length=255, null=True, verbose_name='OTA Code'),
        ),
        migrations.AlterField(
            model_name='roombooking',
            name='status',
            field=models.CharField(choices=[('Check In', 'Check In'), ('Check Out', 'Check Out'), ('Cancelled', 'Cancelled'), ('Booked', 'Booked'), ('Confirm Booking', 'Confirm Booking'), ('Overstayed', 'Overstayed'), ('No Show', 'No Show'), ('Transfer from Airside', 'Transfer from Airside'), ('Transfer from Landside', 'Transfer from Landside'), ('Reservation', 'Reservation')], default='Booked', help_text='determine room booking status', max_length=50),
        ),
        migrations.AlterUniqueTogether(
            name='booking',
            unique_together={('platform', 'ota_code')},
        ),
    ]
