# Generated by Django 4.2.3 on 2024-03-19 07:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rooms', '0003_room_is_upper'),
        ('bookings', '0003_alter_booking_ota_code_alter_roombooking_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='roombooking',
            name='staah_room_reservation_id',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Staah Room Reservation ID'),
        ),
        migrations.AlterUniqueTogether(
            name='roombooking',
            unique_together={('booking', 'staah_room_reservation_id'), ('booking', 'room')},
        ),
    ]
