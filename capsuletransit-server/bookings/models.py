import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Set, <PERSON><PERSON>
from uuid import uuid4

from accounts.models import Account
from bookingplatform.models import Platform
from bookings import exceptions
from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import <PERSON><PERSON>an<PERSON>ield, Char<PERSON>ield, Q, Sum, UniqueConstraint
from django.utils import timezone
from guests.models import Customer, IDType
from lockers.models import Locker
from merch.models import Merch
from rooms.models import Room
from shower.models import Shower

from .utils import calculate_rate_sum

# class Platform(models.Model):
#     platform_id = models.UUIDField(
#         primary_key=True,
#         default=uuid4,
#         editable=False,
#         unique=True,
#         verbose_name="Booking Platform ID",
#     )
#     platform = models.CharField(max_length=50, verbose_name="Booking Platform")
#     is_archive = models.BooleanField(
#         default=False, verbose_name="Is Booking Platform archived?"
#     )

#     def __str__(self):
#         return self.platform


class Booking(models.Model):
    booking_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Booking ID",
    )
    booking_no = models.CharField(
        max_length=20,
        unique=True,
        editable=False,
        verbose_name="Booking Number",
        null=True,
    )
    booking_made_datetime = models.DateTimeField(
        verbose_name="Datetime of the Booking Made", editable=False
    )
    last_updated_at = models.DateTimeField(auto_now=True)
    added_at = models.DateTimeField(auto_now_add=True)
    platform = models.ForeignKey(
        Platform, verbose_name="Booking Platform", on_delete=models.RESTRICT
    )
    customer_staying = models.ForeignKey(
        Customer,
        null=True,
        verbose_name="Customer Staying ID",
        on_delete=models.RESTRICT,
    )
    customer_booked = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Booking Customer",
        help_text="placeholder name for customer who booked via Hotel Website or OTA",
    )
    adult_pax = models.IntegerField(
        default=0, verbose_name="Number of Adults", validators=[MinValueValidator(0)]
    )
    child_pax = models.IntegerField(
        default=0, verbose_name="Number of Children", validators=[MinValueValidator(0)]
    )
    details = models.CharField(
        max_length=2000,
        null=True,
        blank=True,
        verbose_name="Details",
        help_text="details of all relevant booking items in a string",
    )
    # details taken from Room Booking, Shower Booking, Locker Booking, Merchandise Purchase tables
    sum = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="Sum",
        help_text="total amount needed to be paid",
    )
    ota_code = models.CharField(max_length=255, verbose_name="OTA Code", null=True)
    lot = models.ForeignKey("lot.Lot", on_delete=models.RESTRICT)
    is_cancelled = BooleanField("Is Cancelled", default=False)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=["platform", "ota_code"],
                name="unique_ota_code_platform",
                condition=Q(is_cancelled=False),
            )
        ]

    @property
    def can_be_confirmed(self):
        """
        Check if the booking can be confirmed\n
        """
        return self.booking_status.filter(
            Q(is_latest=True) &
            Q(
                Q(booking_status="Booked") |
                Q(booking_status__icontains="Transfer From")
            )
        ).exists()

    def __str__(self):
        return self.booking_no if self.booking_no else "Reservation"

    def add_room(
        self,
        room_id: uuid.UUID,
        pic: uuid.UUID,
        remarks: str,
        date_range: Tuple[datetime, datetime],
        status : str = "Booked",
        actual_checkin_date_time : str = None
    ) -> "RoomBooking":
        """
        @params
        Add room to the booking
        @room_id : uuid.UUID. The ID of the room to be added
        @pic : uuid.UUID The ID of the person in charge of the room
        @remarks : str The remarks of the room booking
        @date_range : Tuple[datetime.datetime, datetime.datetime] The date range of the room booking

        """

        # Recalculate sum

        check_in_datetime = date_range[0]
        check_out_datetime = date_range[1]

        room = Room.objects.get(room_id=room_id)
        booking_price = calculate_rate_sum(
            start_date=check_in_datetime,
            end_date=check_out_datetime,
            room_rate=room.related_room_rate(platfrom_id=self.platform.pk),
        )

        room_booking = RoomBooking(
            booking=self,
            room_id=room_id,
            person_in_charge_id=pic,
            details=remarks,
            sum=booking_price,
            status=status,
            actual_checkin_date_time=actual_checkin_date_time
        )

        # Add Total booking Price
        self.sum += booking_price

        self.room_bookings.add(
            room_booking,
            bulk=False,
        )
        self.save()

        return room_booking

    def confirm_booking(self):
        """
        Change booking status to Confirm booking, this will append new booking status to the booking status
        """
        try:
            latest_booking_status = (
                self.booking_status.all().select_for_update().get(is_latest=True)
            )
            self.booking_status.all().update(is_latest=False)

        except BookingStatus.DoesNotExist:
            raise Exception("Booking Status not found")

        try:
            if latest_booking_status.booking_status == "Confirm Booking":
                raise Exception("Booking already confirmed")
        except exceptions.UnableConfirmBooking:
            raise Exception("Booking already confirmed")

        self.booking_status.add(
            BookingStatus(
                booking_status="Confirm Booking",
                check_in_datetime=latest_booking_status.check_in_datetime,
                check_out_datetime=latest_booking_status.check_out_datetime,
                max_check_out_datetime=latest_booking_status.max_check_out_datetime,
                status_datetime=timezone.now(),
                is_latest=True,
            ),
            bulk=False,
        )

        room_bookings = RoomBooking.objects.filter(booking_id=self.booking_id)
        for room_booking in room_bookings:
            room_booking.room.status = "occupied, cleaned"
            room_booking.room.save()

    def check_in(
        self,
        new_check_in_datetime = None,
        new_check_out_datetime = None,
        new_max_check_out_datetime = None,
        is_no_show = False
    ):
        """
        Change booking status to Check In, this will append new booking status to the booking status, and change room status
        """
        try:

            booking_status: BookingStatus = self.booking_status.get(is_latest=True)
            if booking_status.booking_status == "Check In":
                raise exceptions.UnableCheckIn("Booking already checked in")
            if (
                booking_status.booking_status != "Confirm Booking"
                and booking_status.booking_status != "No Show"
                and booking_status.booking_status != "Transfer From Landside"
                and booking_status.booking_status != "Transfer From Airside"
                and not self.platform.can_late_payment
            ):
                raise exceptions.UnableCheckIn("Booking status not confirmed")

            if booking_status.booking_status == "Check Out":
                raise exceptions.UnableCheckIn("Booking already check out")

            room_bookings: Set[RoomBooking] = self.room_bookings.all()
            for room_booking in room_bookings:
                room_booking.status = "Check In"
                room_booking.actual_checkin_date_time = timezone.now()
                room_booking.room.status = "occupied, cleaned"
                room_booking.save()

            self.booking_status.all().update(is_latest=False)

            self.booking_status.add(
                BookingStatus(
                    booking_status="Check In",
                    check_in_datetime=new_check_in_datetime if new_check_in_datetime else booking_status.check_in_datetime,
                    check_out_datetime=new_check_out_datetime if new_check_out_datetime else booking_status.check_out_datetime,
                    max_check_out_datetime= new_max_check_out_datetime if  new_max_check_out_datetime else booking_status.max_check_out_datetime,
                    status_datetime=timezone.now(),
                    is_latest=True,
                    has_no_show=is_no_show,
                ),
                bulk=False,
            )

            room_bookings = RoomBooking.objects.filter(booking_id=self.booking_id)
            for room_booking in room_bookings:
                room_booking.room.status = "occupied, dirty"
                room_booking.room.save()

            # new_booking_status = BookingStatus.objects.create(
            #     booking_id=booking.pk,
            #     booking_status="Check In",
            #     check_in_datetime=current_booking_status.check_in_datetime,
            #     check_out_datetime=current_booking_status.check_out_datetime,
            #     status_datetime=datetime.now(),
            #     is_latest=True,
            # )
        except BookingStatus.DoesNotExist:
            raise Exception("Booking Status not found")

    @property
    def billed_room_bookings_amount(self) -> float:
        """
        Total Billed Amount of room bookings aggregated for the booking
        """

        billed_amount = self.room_bookings.all().aggregate(total=Sum("sum"))["total"]

        return billed_amount

    def get_latest_booking_status(self):
        return BookingStatus.objects.filter(booking_id=self.pk).latest(
            "status_datetime"
        )

    def get_all_booking_room(self):
        return RoomBooking.objects.filter(booking_id=self.pk).prefetch_related(
            "room", "room__room_type"
        )

    def remove_room_booking(self, room_code):
        room_bookings: Set[RoomBooking] = self.room_bookings.all()

        room_book_to_remove = room_bookings.filter(room__room_code=room_code).first()

        if not room_book_to_remove:
            return False
        
        if self.customer_staying.pk == room_book_to_remove.person_in_charge_id:
            other_room_booking = room_bookings.exclude(room__room_code=room_code).first()

            self.customer_staying_id = other_room_booking.person_in_charge_id
            self.customer_booked = f"{other_room_booking.person_in_charge.firstname} {other_room_booking.person_in_charge.lastname}"
        
        self.sum = float(self.sum) - float(room_book_to_remove.sum)
        self.save()
        
        room_book_to_remove.delete()
        return True
    
    def remove_shower_booking(self, remove_count):
        shower_bookings: Set[ShowerBooking] = self.shower_bookings.all()
        
        if remove_count >= shower_bookings.count():
            return False

        shower_booking_to_remove = shower_bookings[:remove_count]
        
        total_sum = 0
        for shower_book in shower_booking_to_remove:
            total_sum += shower_book.sum
            shower_book.delete()
        
        self.sum = float(self.sum) - float(total_sum)
        self.save()
        
        return True
    
    def remove_locker_booking(self, locker_code):
        locker_bookings: Set[LockerBooking] = self.locker_bookings.all()

        locker_book_to_remove = locker_bookings.filter(locker__code=locker_code).first()

        if not locker_book_to_remove:
            return False
        
        self.sum = float(self.sum) - float(locker_book_to_remove.sum)
        self.save()
        
        locker_book_to_remove.delete()
        return True


class TransferBooking(models.Model):
    transfer_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    from_booking = models.ForeignKey(
        Booking, on_delete=models.RESTRICT, null=True, blank=True
    )
    to_booking = models.ForeignKey(
        Booking,
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name="transferred_bookings",
    )


ROOM_BOOKING_STATUS = (
    ("Check In", "Check In"),
    ("Check Out", "Check Out"),
    ("Cancelled", "Cancelled"),
    ("Booked", "Booked"),
    ("Confirm Booking", "Confirm Booking"),
    ("Overstayed", "Overstayed"),
    ("No Show", "No Show"),
    ("Transfer from Airside", "Transfer from Airside"),
    ("Transfer from Landside", "Transfer from Landside"),
    ("Reservation", "Reservation"),
)

ROOM_TYPES_RANKING = {
    "Suite": 1,
    "Queen": 2,
    "Interstellar Pod": 2,
    "Male Single": 3,
    "Female Single": 3,
    "Mixed Single": 3,
    "Runway Suite" : 1,
    "OKU" : 2,
    "Family Room" : 3,
    "Executive King" : 4,
    "Deluxe King" : 5,
    "Deluxe Queen" : 5,
    "Deluxe Twin" : 5
}


class RoomBooking(models.Model):
    room_booking_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Room Booking ID",
    )
    booking = models.ForeignKey(
        Booking,
        on_delete=models.RESTRICT,
        verbose_name="Booking ID",
        related_name="room_bookings",
    )
    room = models.ForeignKey(
        Room,
        on_delete=models.RESTRICT,
        verbose_name="Room ID",
        related_name="room_bookings",
    )
    person_in_charge = models.ForeignKey(
        Customer, null=True, verbose_name="Person In Charge", on_delete=models.RESTRICT
    )
    details = models.CharField(max_length=100, null=True, verbose_name="Details")
    actual_checkin_date_time = models.DateTimeField(
        null=True, verbose_name="Actual Checkin Datetime"
    )
    actual_checkout_date_time = models.DateTimeField(
        null=True, verbose_name="Actual Checkout Datetime"
    )
    sum = models.DecimalField(
        max_digits=100,
        decimal_places=2,
        verbose_name="Price Paid Sum",
        help_text="record total amount of price paid from this room booking",
    )
    status = models.CharField(
        max_length=50,
        choices=ROOM_BOOKING_STATUS,
        default="Booked",
        help_text="determine room booking status",
    )
    staah_room_reservation_id = CharField(
        max_length=50, null=True, blank=True, verbose_name="Staah Room Reservation ID"
    )

    class Meta:
        unique_together = (
            ("booking", "room"),
            ("booking", "staah_room_reservation_id"),
        )

    def __str__(self):
        return self.status

    @property
    def derived_check_in_datetime(self):
        return (
            self.actual_checkin_date_time
            or self.booking.booking_status.get(is_latest=True).check_in_datetime
        )

    @property
    def derived_check_out_datetime(self):
        if self.actual_checkout_date_time:
            return self.actual_checkout_date_time

        actual_in_datetime = self.actual_checkin_date_time
        booking_status: BookingStatus = self.booking.booking_status.get(is_latest=True)
        in_datetime = booking_status.check_in_datetime
        out_datetime = booking_status.check_out_datetime

        return (
            actual_in_datetime + (out_datetime - in_datetime)
            if actual_in_datetime and in_datetime
            else out_datetime
        )


STATUS_TYPE = (
    ("Check In", "Check In"),
    ("Check Out", "Check Out"),
    ("Cancelled", "Cancelled"),
    ("Booked", "Booked"),
    ("Confirm Booking", "Confirm Booking"),
    ("No Show", "No Show"),
    ("Transfer from Airside", "Transfer from Airside"),
    ("Transfer from Landslide", "Transfer from Landslide"),
    ("Reservation", "Reservation"),
)


class BookingStatus(models.Model):
    booking_status_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Booking Status ID",
    )
    booking = models.ForeignKey(
        Booking,
        on_delete=models.RESTRICT,
        verbose_name="Booking ID",
        related_name="booking_status",
    )
    booking_status = models.CharField(
        max_length=50, choices=STATUS_TYPE, verbose_name="Booking Status"
    )
    check_in_datetime = models.DateTimeField(verbose_name="Booking Check in Datetime", db_index=True)
    check_out_datetime = models.DateTimeField(verbose_name="Booking Check out Datetime", db_index=True)
    max_check_out_datetime = models.DateTimeField(
        verbose_name="Maximum booking Check out Datetime",
        db_index=True
    )
    status_datetime = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Status Datetime Made",
        help_text="The Datetime the Booking Status is generated",
    )
    is_latest = models.BooleanField(
        default=True, verbose_name="Is the Booking Status the Latest"
    )
    has_no_show = models.BooleanField(
        verbose_name="Has no show",
        help_text="to indicate the booking has already no show",
        default=False,
    )

    def __str__(self) -> str:
        return f"{self.booking_status_id} {self.booking_status}"


class LockerBooking(models.Model):
    locker_booking_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    booking = models.ForeignKey(
        Booking, on_delete=models.RESTRICT, related_name="locker_bookings"
    )
    locker = models.ForeignKey(
        Locker, on_delete=models.RESTRICT, verbose_name="Locker ID"
    )
    sum = models.DecimalField(
        max_digits=100,
        decimal_places=2,
        verbose_name="Price Paid Sum",
        help_text="total amount of price paid for this locker if booked individually",
    )
    status = models.CharField(
        choices=STATUS_TYPE,
        max_length=50,
        default="Booked",
        blank=True,
        null=True,
    )
    locker_start_rent_datetime = models.DateTimeField(
        verbose_name="Locker Start Rent Datetime"
    )
    locker_end_rent_datetime = models.DateTimeField(
        verbose_name="Locker End Rent Datetime"
    )
    room_code = models.CharField(max_length=50, default="")


class ShowerBooking(models.Model):
    shower_booking_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    booking = models.ForeignKey(
        Booking,
        on_delete=models.RESTRICT,
        verbose_name="Booking ID",
        related_name="shower_bookings",
    )
    shower = models.ForeignKey(
        Shower, on_delete=models.RESTRICT, verbose_name="Shower ID", null=True
    )
    sum = models.DecimalField(
        max_digits=100,
        decimal_places=2,
        verbose_name="Price Paid Sum",
        help_text="total amount of price paid for this shower if booked individually",
    )
    status = models.CharField(
        choices=STATUS_TYPE,
        max_length=50,
        default="Booked",
        blank=True,
        null=True,
    )
    shower_start_rent_datetime = models.DateTimeField(
        verbose_name="Shower Start Rent Datetime"
    )
    shower_end_rent_datetime = models.DateTimeField(
        verbose_name="Shower End Rent Datetime"
    )


MERCH_BOOKING_STATUS = (
    ("Pending", "Pending"),
    ("Paid", "Paid"),
    ("Cancelled", "Cancelled"),
)


class MerchBooking(models.Model):
    merch_booking_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    merch = models.ForeignKey(Merch, on_delete=models.RESTRICT)
    booking = models.ForeignKey(Booking, on_delete=models.RESTRICT)
    customer = models.ForeignKey(Customer, on_delete=models.RESTRICT)
    quantity = models.IntegerField(
        null=False, default=1, validators=[MinValueValidator(1)]
    )
    status = models.CharField(
        choices=MERCH_BOOKING_STATUS,
        max_length=50,
        default="Pending",
        blank=True,
        null=False,
    )
    purchase_datetime = models.DateTimeField(auto_now_add=True)


class ExtraGuest(models.Model):
    extra_guest_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    booking = models.ForeignKey(Booking, on_delete=models.RESTRICT)
    name = models.CharField(max_length=100)
    id_no = models.CharField(max_length=15)
    id_type = models.ForeignKey(
        IDType, on_delete=models.RESTRICT, verbose_name="ID Type", null=True
    )


class BookingNumberCounter(models.Model):
    branch_name = models.CharField(max_length=100, unique=True)
    counter = models.PositiveIntegerField()  # 4 bytes
    last_updated = models.DateTimeField(auto_now=True)

    class Context:
        def __init__(self, branch_name: str = "KLIA") -> None:
            self.branch_name = branch_name

        def __enter__(self):
            try:
                self.booking_no_counter = (
                    BookingNumberCounter.objects.select_for_update().get(
                        branch_name=self.branch_name
                    )
                )
            except:
                self.booking_no_counter = BookingNumberCounter.objects.create(
                    branch_name=self.branch_name, counter=1
                )
            return self.booking_no_counter

        def __exit__(self, exc_type, exc_value, traceback):

            if exc_type != None:
                return False
            self.booking_no_counter.counter += 1
            self.booking_no_counter.save()


class BookingSessionRoomLock(models.Model):
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expiry_at = models.DateTimeField()
    room = models.OneToOneField(
        Room, on_delete=models.CASCADE, related_name="session_lock"
    )
    token = models.CharField(max_length=5, null=True)

    def reset(self):
        """
        RESET the session lock\n
        set expiry to now \n
        set token to ""
        """
        self.expiry_at = timezone.now()
        self.token = ""

    def validate_token(self, token: str):
        if self.token == "":
            return True
        return self.token == token

    def extend_session(self, token: str, delta=None):
        """
        Extend the session by 5 minutes
        if the token is valid and the session is not expired
        if the session is expired, a new token will take place
        """
        # only be able to extend 5 minutes at one time
        # 5 minutes after
        now = timezone.now()

        # existing Token no longer valid  if the session expired
        if now > self.expiry_at:
            self.reset()

        if not self.validate_token(token):
            raise Exception("Cannot Lock room : ", self.room)

        if (
            self.validate_token(token) and self.expiry_at > now
        ) or self.expiry_at is None:
            if delta is None:
                self.expiry_at = now + timedelta(minutes=1, seconds=3)
            else:
                self.expiry_at = now + delta

            self.token = token  # reset token activation


class ConsentForm(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    booking = models.ForeignKey(
        Booking, on_delete=models.CASCADE, null=True, blank=True
    )
    created_at = models.DateTimeField(
        verbose_name="Datetime of ConsentForm Made", editable=False
    )
    consent_checkin = models.BinaryField(
        unique=False,
        help_text="image of signature converted to binary from checkin",
        null=True,
        default=None,
    )
    consent_checkout = models.BinaryField(
        unique=False,
        help_text="image of signature converted to binary from checkin",
        null=True,
        default=None,
    )


class ConsentSession(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)
    session_id = models.UUIDField(default=uuid4)
    consent_form = models.BinaryField(
        unique=False,
        help_text="image of signature converted to binary from checkin",
        null=True,
        default=None,
    )
    pin = models.CharField(max_length=6)
    last_updated = models.DateTimeField(
        verbose_name="Datetime of last update", editable=False
    )
    cashier = models.ForeignKey(
        Account, on_delete=models.CASCADE, null=True, blank=True
    )


class FeedBack(models.Model):
    booking = models.ForeignKey(
        Booking, on_delete=models.RESTRICT, related_name="booking"
    )
    rating = models.CharField(max_length=20)
    feedback = models.CharField(max_length=200)
    room_selected = models.CharField(max_length=50)
    created_at = models.DateTimeField(editable=False, auto_now_add=True)
    lot = models.ForeignKey("lot.Lot", on_delete=models.RESTRICT, null=True, blank=True)
