from django.core.management.base import BaseCommand
from bookings.tasks import mark_as_overstay
from lot.models import Lot
from bookings.services.updateservice import update_booking_status_no_show
from django.db import transaction

class Command(BaseCommand):
    help = 'Mark bookings No Show'

    def handle(self, *args, **options):
        lots = Lot.objects.all()
        with transaction.atomic():
            for lot in lots:
                update_booking_status_no_show(lot.lot_id)

        self.stdout.write(self.style.SUCCESS('Successfully marked bookings as No Show'))

