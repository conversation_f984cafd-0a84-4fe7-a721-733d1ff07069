from django.dispatch import receiver
from django.db.models.signals import post_save
from django.db import transaction

from .models import BookingStatus
from .services.booking import confirm_late_payment_booking
from .services import points

# @receiver(post_save, sender=BookingStatus)
# def direct_confirm_booking_check(sender, instance : BookingStatus, created, **kwargs):
#     if created:
#         confirm_late_payment_booking(instance=instance)
#         ...



@receiver(post_save, sender=BookingStatus)
def booking_status_signal(sender, instance : BookingStatus, created, **kwargs):
    if created and instance.booking_status == "Check Out":

        with transaction.atomic():
            result = points.add_point(instance.booking_status_id)

            if result["status"] == "failed":
                return
        
        """
        COMMENTED DUE TO REQUIREMENT CHANGES
        """
        # with transaction.atomic():
        #     points.auto_claim_milestone(customer=result['data'])
         
        """
        COMMENTED DUE TO REQUIREMENT CHANGES
        """

            