from django.db.models import <PERSON><PERSON>an<PERSON>ield
from bookings.models import Booking, RoomBooking
import datetime
from datetime import timedelta
from django.db.models import Subquery, OuterRef
from django.db.models import Q, Value, F, Case, When
from django.db.models import F, ExpressionWrapper, DateTimeField
from django.utils import timezone
from bookings.models import Booking, RoomBooking, BookingStatus

from django.db import transaction

# def get_overstay_bookings(now : datetime.datetime, duration : int = 30):
#     """
#     Functions to return queryset of bookings that are overstay by duration\n
#     bookings_roombooking[actual_checkout_date_time] = null
#         &&
#     timezone.now() + 30 mins > bookings_booking[check_out_datetime] which also can be written as 
#     timezone.now() - 30 mins < bookings_roombooking[actual_checkout_date_time]
#     """
#     booking_qs = Booking.objects.all()
#     booking_qs = booking_qs.filter(
#         Q(
#             room_bookings__actual_checkout_date_time__gt=now+datetime.timedelta(minutes=duration)
#         ))
#     return booking_qs


def annotate_overstay_duration_for_booking(qs ):

    latest_max_check_out_datetime = BookingStatus.objects.filter(
        booking=OuterRef('pk'),
        is_latest=True
    ).values('max_check_out_datetime')[:1]
    
    qs = qs.annotate(
        checkout_time_after_grace=ExpressionWrapper(
            F('booking_status__max_check_out_datetime'),
            output_field=DateTimeField()
        ),
        overstay_duration=ExpressionWrapper(
            (timezone.now() - timedelta(hours=8)) - Subquery(latest_max_check_out_datetime),
            output_field=DateTimeField()
        ),
        is_overstay=Case(
            When(
                Q(checkout_time_after_grace__lt=(timezone.now() - timedelta(hours=8))) &
                Q(room_bookings__actual_checkout_date_time=None),
                # | Q(room_bookings__actual_checkout_date_time__gt=F('checkout_time_after_grace')),
                then=Value(True)
            ),
            default=Value(False),
            output_field=BooleanField()
        )
    )

    return qs 

def get_overstay_bookings():

    bookings = Booking.objects.prefetch_related(
        'room_bookings',
        'booking_status'
    )

    current_time = timezone.now()
    result = bookings.filter(
        booking_status__booking_status='Check In',
        booking_status__is_latest=True,
        booking_status__max_check_out_datetime__lt=current_time
    ).exclude(customer_booked = 'Non Guest')
    result = annotate_overstay_duration_for_booking(result)

    return result


def mark_as_overstay():
    """
    Fetch all bookings that are overstay and mark them as overstay
    """
    bookings = get_overstay_bookings()
    bookings_ids = bookings.values_list('booking_id', flat=True)
    
    with transaction.atomic():
        RoomBooking.objects.select_for_update().filter(
            booking_id__in=bookings_ids,
        ).update(
            status="Overstayed"
        )

        for book in bookings:
            prev_booking_status = BookingStatus.objects.select_for_update().filter(
                booking_id = book.pk,
                is_latest = True
            ).first()

            prev_booking_status.is_latest = False
            prev_booking_status.save()

            BookingStatus.objects.create(
                booking_id=book.pk,
                booking_status="Overstayed",
                check_in_datetime=prev_booking_status.check_in_datetime,
                check_out_datetime=prev_booking_status.check_out_datetime,
                status_datetime=timezone.now(),
                max_check_out_datetime =prev_booking_status.max_check_out_datetime,
                is_latest=True,
                has_no_show=prev_booking_status.has_no_show
            )