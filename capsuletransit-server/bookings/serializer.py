import datetime

from bookingplatform.models import Platform
from django.db import transaction
from django.db.models import F
from lockers.serializer import LockerSerializer
from rest_framework import serializers
from rest_framework.serializers import DateTimeField
from rooms.models import Room
from rooms.serializers import RoomDetailSerializer, TiersSerializer

from .models import (
    Booking,
    BookingStatus,
    ConsentForm,
    ExtraGuest,
    Feed<PERSON>ack,
    LockerBooking,
    RoomBooking,
    ShowerBooking,
)


class LockerBookingSerializer(serializers.ModelSerializer):
    locker = LockerSerializer()
    is_extended = serializers.BooleanField(required=False)
    extend_duration_hour = serializers.IntegerField(required=False)
    is_extra = serializers.BooleanField(required=False)

    class Meta:
        model = LockerBooking
        fields = "__all__"


class RoomSerializer(serializers.ModelSerializer):
    class Meta:
        model = Room
        fields = "__all__"


class RoomBookingSerializer(serializers.ModelSerializer):
    person_in_charge = serializers.CharField(
        source="person_in_charge.firstname", required=False, allow_null=True
    )
    person_in_charge_lastname = serializers.CharField(
        source="person_in_charge.lastname", required=False, allow_null=True
    )
    person_in_charge_id = serializers.CharField(
        source="person_in_charge.customer_id", required=False, allow_null=True
    )
    person_in_charge_country = serializers.CharField(
        source="person_in_charge.country", required=False, allow_null=True
    )
    member_id = serializers.CharField(
        source="person_in_charge.member", required=False, allow_null=True
    )
    room_code = serializers.CharField(source="room.room_code")
    room_type = serializers.CharField(source="room.room_type.type_name")
    room_type_color_tags = serializers.ReadOnlyField(source="room.room_type.color_tags")
    room_zone = serializers.CharField(source="room.room_type.roomzone.zone_name")
    room_type_details = serializers.ReadOnlyField(
        source="room.room_type.RoomTypeDetails"
    )
    room_type_id = serializers.ReadOnlyField(source="room.room_type.type_id")
    room_status = serializers.ReadOnlyField(source="room.status")
    derived_check_in_datetime = DateTimeField(read_only=True)
    derived_check_out_datetime = DateTimeField(read_only=True)

    class Meta:
        model = RoomBooking
        fields = "__all__"


class BookingPeriodSerializer(serializers.Serializer):
    room_booking_id = serializers.UUIDField()
    check_in_datetime = serializers.DateTimeField()
    check_out_datetime = serializers.DateTimeField()
    max_check_out_datetime = serializers.DateTimeField()
    customer_firstname = serializers.CharField()
    customer_lastname = serializers.CharField()
    current_booking_status = serializers.CharField()
    booking_no = serializers.CharField()
    booking_platform = serializers.CharField()
    booking_id = serializers.UUIDField()


class PlatformSerializer(serializers.ModelSerializer):
    class Meta:
        ref_name = "PlatformSerializer"
        model = Platform
        fields = ("platform_id", "platform", "is_archive")


class BookingStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookingStatus
        fields = "__all__"


class BookingSerializer(serializers.ModelSerializer):
    platform = PlatformSerializer()
    room_bookings = RoomBookingSerializer(many=True)
    booking_status = BookingStatusSerializer(many=True)

    class Meta:
        model = Booking
        fields = "__all__"


class PlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = Platform
        fields = ("platform_id", "platform", "can_late_payment", "color_tags")


class ShowerBookingSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShowerBooking
        fields = "__all__"


class BookingGuestPortofolioSerializer(serializers.ModelSerializer):
    platform = PlatformSerializer()
    room_bookings = RoomBookingSerializer(many=True, read_only=True)
    booking_status = BookingStatusSerializer(many=True, read_only=True)
    shower_bookings = ShowerBookingSerializer(many=True, read_only=True)
    locker_bookings = LockerBookingSerializer(many=True, read_only=True)

    customer_firstname = serializers.CharField(
        source="customer_staying.firstname", required=False
    )
    customer_lastname = serializers.CharField(
        source="customer_staying.lastname", required=False
    )

    class Meta:
        model = Booking
        fields = "__all__"


class RoomBookingCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomBooking
        exclude = ("booking",)


class RoomStatusCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookingStatus
        exclude = ("booking",)


class BookingReadSerializer(serializers.ModelSerializer):
    room_bookings = RoomBookingSerializer(many=True, read_only=True)
    booking_status = BookingStatusSerializer(many=True, read_only=True)

    class Meta:
        model = Booking
        fields = "__all__"


from django.db import transaction


class BookingSerializerNew(serializers.ModelSerializer):
    room_bookings = RoomBookingCreateSerializer(many=True)
    booking_status = RoomStatusCreateSerializer(many=True)
    booking_no = serializers.CharField()

    def create(self, validated_data):
        with transaction.atomic():
            room_bookings = self.validated_data.pop("room_bookings")

            booking_status = self.validated_data.pop("booking_status")
            booking: Booking = Booking(**self.validated_data)
            booking.save()
            for room_booking in room_bookings:
                room_booking = RoomBooking(**room_booking)
                room_booking.booking = booking
                room_booking.save()
            for status in booking_status:
                status = BookingStatus(**status)
                status.booking = booking
                status.save()
        return BookingGuestPortofolioSerializer(booking)

    class Meta:
        model = Booking
        fields = "__all__"


class CustomBookingSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    booking_no = serializers.CharField()
    customer = serializers.CharField()
    platform = serializers.CharField()
    status = serializers.CharField()
    check_in_time = serializers.DateTimeField()
    room_bookings = serializers.ListField(child=serializers.DictField())

    def to_representation(self, instance):
        return super().to_representation(instance)


class RoomBookingDetailsSerializer(serializers.ModelSerializer):
    room = RoomDetailSerializer()

    class Meta:
        model = RoomBooking
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.pop("booking", None)
        data.pop("person_in_charge", None)
        return data


class BookingStatusDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookingStatus
        fields = ("booking_status", "check_in_datetime", "check_out_datetime")


class BookingReadSerializer(serializers.ModelSerializer):
    booking_status = BookingStatusDetailSerializer(
        source="get_latest_booking_status", read_only=True
    )
    room = RoomBookingDetailsSerializer(
        source="get_all_booking_room", read_only=True, many=True
    )
    platform = PlatformSerializer()

    class Meta:
        model = Booking
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.pop("customer_staying", None)
        return data


"""
NEW SERIALIZER
"""

from rest_framework import serializers


class RoomBookingCreationSerializer(serializers.Serializer):
    picId = serializers.UUIDField()
    roomId = serializers.UUIDField()
    details = serializers.CharField()


class ExpressRoomBookingCreationSerializer(serializers.Serializer):
    room_type_name = serializers.CharField()
    count = serializers.IntegerField()


class ExpressRoomBookingRequestSerializer(serializers.Serializer):
    rooms = ExpressRoomBookingCreationSerializer(many=True)
    check_in_datetime = serializers.DateTimeField()
    duration = serializers.IntegerField()


class BookingCreationSerializer(serializers.Serializer):
    checkInDatetime = serializers.DateTimeField()
    checkOutDatetime = serializers.DateTimeField()
    adult = serializers.IntegerField()
    child = serializers.IntegerField()
    platformId = serializers.UUIDField()
    otaCode = serializers.CharField()
    details = serializers.CharField()
    rooms = RoomBookingCreationSerializer(many=True)


class LockerBookingSerializer(serializers.Serializer):
    class Meta:
        model = LockerBooking
        fields = "__all__"


class BookingRoomTransferUpgradeSerializer(serializers.Serializer):
    # room_booking
    # new_room
    room_booking_id = serializers.UUIDField()
    new_room_id = serializers.UUIDField()


class ExpressBookingSerializer(serializers.Serializer):
    checkInDatetime = serializers.DateTimeField()
    checkOutDatetime = serializers.DateTimeField()
    adult = serializers.IntegerField()
    child = serializers.IntegerField()
    platformId = serializers.UUIDField()
    otaCode = serializers.CharField()
    picId = serializers.UUIDField()
    remarks = serializers.CharField()
    roomsDetails = ExpressRoomBookingCreationSerializer(many=True)


class RegisterLockerSerialier(serializers.Serializer):
    locker_id = serializers.UUIDField()
    room_code = serializers.CharField()


class LockerBookingCreateSerializer(serializers.Serializer):
    start_rent = serializers.DateTimeField()
    end_rent = serializers.DateTimeField()
    booking_id = serializers.UUIDField()
    lockers = RegisterLockerSerialier(many=True)


class ExtendLockerSerializer(serializers.Serializer):
    locker_booking_id = serializers.UUIDField()
    new_end_rent = serializers.DateTimeField()


class TransferLockerSerializer(serializers.Serializer):
    locker_booking_id = serializers.UUIDField()
    new_locker_id = serializers.UUIDField()


class RebindLockerSerializer(serializers.Serializer):
    locker_booking_id = serializers.UUIDField()
    new_room_code = serializers.CharField()


class ShowerBookingCreateSerializer(serializers.Serializer):
    start_rent = serializers.DateTimeField()
    end_rent = serializers.DateTimeField()
    booking_id = serializers.UUIDField()
    shower_ids = serializers.ListField(child=serializers.UUIDField(), allow_empty=False)


class RoomSessionLockRequestSerializer(serializers.Serializer):
    token = serializers.CharField(max_length=5)
    room_ids = serializers.ListField(child=serializers.UUIDField())


class AddBookingRemarksSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    new_remarks = serializers.CharField()


class ExtraGuestSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExtraGuest
        fields = "__all__"


class ExtraGuestItemSerializer(serializers.Serializer):
    name = serializers.CharField()
    id_no = serializers.CharField()
    id_type = serializers.UUIDField()


class AddExtraGuestSerializer(serializers.Serializer):
    extra_guests = ExtraGuestItemSerializer(many=True)
    booking = serializers.UUIDField()

    def validate(self, data):
        extra_guests = data.get("extra_guests")
        booking_id = data.get("booking")

        room_bookings = RoomBooking.objects.filter(booking_id=booking_id).annotate(
            max_pax=F("room__room_type_id__max_pax")
        )

        pic_id_list = []
        total_pax = 0
        for room_booking in room_bookings:
            total_pax += room_booking.max_pax
            if room_booking.person_in_charge.pk not in pic_id_list:
                pic_id_list.append(room_booking.person_in_charge.pk)

        available_pax = total_pax - len(pic_id_list)
        total_extra_guest = ExtraGuest.objects.filter(
            booking_id=booking_id
        ).count() + len(extra_guests)

        if total_extra_guest > available_pax:
            raise serializers.ValidationError("Exceeded max pax")

        for guest in extra_guests:
            name = guest.get("name")
            id_no = guest.get("id_no")

            if len(id_no) > 12:
                raise serializers.ValidationError("IC number maximum 12 number")

        return data


class UpdateBookingStatusInfoSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    pin = serializers.CharField(required=False)
    actual_checkout_datetime = serializers.DateTimeField()


class LockerBookingSerializerForBookingList(serializers.ModelSerializer):
    locker = LockerSerializer()

    class Meta:
        model = LockerBooking
        fields = "__all__"


class BookingSerializerForCheckinOut(BookingSerializer):
    overstay_duration = serializers.SerializerMethodField(
        method_name="get_overstay_duration"
    )
    is_overstay = serializers.BooleanField()
    customer_staying_name = serializers.CharField(required=False, allow_null=True)
    locker_bookings = LockerBookingSerializerForBookingList(
        many=True, read_only=True, required=False
    )
    shower_bookings = ShowerBookingSerializer(many=True, read_only=True, required=False)

    class Meta:
        model = Booking
        fields = "__all__"

    def get_overstay_duration(self, obj):
        if not obj.overstay_duration:
            return

        booking_status = obj.booking_status.first().booking_status
        if not obj.is_overstay and booking_status == "Overstayed":
            obj.is_overstay = True
            obj.overstay_duration = obj.overstay_duration + datetime.timedelta(hours=8)

        overstay: datetime.timedelta = obj.overstay_duration
        days = overstay.days
        minutes = overstay.seconds // 60
        hour = minutes // 60
        minutes = minutes % 60
        return f"{days} days {hour} hours {minutes} minutes"


class BookingAddRoomRoomSerialier(serializers.Serializer):
    room_id = serializers.UUIDField()
    pic_id = serializers.UUIDField()
    remarks = serializers.CharField(required=False)


class BookingAddRoomSerializer(serializers.Serializer):
    """
    A DTO Serializer for adding room to booking
    """

    booking_id = serializers.UUIDField()
    start_time = serializers.IntegerField(
        required=False,
        help_text="Unix epoch format with seconds representing the end time",
    )
    end_time = serializers.IntegerField(
        required=False,
        help_text="Unix epoch format with seconds representing the end time",
    )
    rooms = BookingAddRoomRoomSerialier(many=True)


class ConsentFormSerializer(serializers.ModelSerializer):
    room_no_list = serializers.CharField(required=False, read_only=True)

    class Meta:
        model = ConsentForm
        fields = "__all__"

    def to_representation(self, instance: ConsentForm):
        data = super().to_representation(instance)
        data.update(
            {
                "name": instance.booking.customer_booked,
                "phone_num": instance.booking.customer_staying.phone_number,
                "email": instance.booking.customer_staying.email,
            }
        )
        return data


class SubmitConsentFormInterfaceSerializer(serializers.Serializer):
    consent_form_id = serializers.UUIDField()
    pin = serializers.CharField()
    name = serializers.CharField()
    phone_num = serializers.CharField()
    email = serializers.CharField()
    checkin_signature = serializers.FileField(required=False)
    checkout_signature = serializers.FileField(required=False)


class GetConsentFormInterfaceSerializer(serializers.Serializer):
    consent_form_id = serializers.UUIDField()
    pin = serializers.CharField()
    name = serializers.CharField()
    phone_num = serializers.CharField()
    email = serializers.CharField()
    checkin_signature = serializers.CharField(required=False)


class SubmitConsentSessionPinSerializer(serializers.Serializer):
    pin = serializers.CharField()


class BookingPaymentStatusInformationSerializer(serializers.Serializer):
    pending_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, help_text="Total amount of pending payment"
    )
    paid_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, help_text="Total amount of paid"
    )
    status = serializers.CharField(help_text="Status of the booking Payment")
    overstay_transaction_created = serializers.BooleanField()
    overstay_transaction_paid = serializers.BooleanField()
    transaction_datetime = serializers.DateTimeField()
    payment_type = serializers.CharField()


class OtaRoomSerializer(serializers.Serializer):
    count = serializers.IntegerField()
    room_type_name = serializers.CharField()


class OtaReservationSerializer(serializers.Serializer):
    check_in_datetime = serializers.DateTimeField()
    duration = serializers.IntegerField()
    rooms = OtaRoomSerializer(many=True)


class OtaReservationSummaryZone(serializers.Serializer):
    zone_name = serializers.CharField()
    room_list = serializers.ListField(child=serializers.CharField())


class OtaReservationSummaryType(serializers.Serializer):
    type_name = serializers.CharField()
    zones = OtaReservationSummaryZone(many=True)
    color_code = serializers.CharField()


class OtaReservationSummary(serializers.Serializer):
    total_rooms = serializers.IntegerField()
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    types = OtaReservationSummaryType(many=True)


class PICPOSSerializer(serializers.Serializer):
    person_in_charge_id = serializers.UUIDField()
    person_in_charge_name = serializers.CharField()
    is_member = serializers.BooleanField()


class BookingReadOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = Booking
        fields = "__all__"


class POSGuestSearchSerializer(serializers.Serializer):
    person_in_charge = PICPOSSerializer()
    booking = BookingReadOnlySerializer()
    room_no = serializers.CharField()
    room_type = serializers.CharField()


class ReadPlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = Platform
        fields = "__all__"


class ActiveTierDisplaySerializer(serializers.Serializer):
    platform = ReadPlatformSerializer()
    current_tier = TiersSerializer(allow_null=True)


class NewTransferRoomSerializer(serializers.Serializer):
    prev_room_id = serializers.UUIDField()
    new_room_ids = serializers.ListField(child=serializers.UUIDField())


class TransferBookingSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    room_update = NewTransferRoomSerializer(many=True)
    new_lot = serializers.CharField()


class RescheduleBookingSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    new_check_in_datetime = serializers.DateTimeField()


class FeedbackWriteSerializer(serializers.Serializer):
    booking_no = serializers.CharField()
    rating = serializers.CharField()
    feedback = serializers.CharField(
        allow_blank=True,
        max_length=200,
        error_messages={
            "max_length": "Feedback cannot be longer than 200 characters.",
        },
    )
    room_selected = serializers.CharField(allow_blank=True)


class FeedbackSerializer(serializers.ModelSerializer):
    booking_id = serializers.CharField(source="booking.booking_id")
    booking_no = serializers.CharField(source="booking.booking_no")
    customer_name = serializers.CharField(source="booking.customer_staying")

    class Meta:
        model = FeedBack
        fields = "__all__"


class LockerBookingPosSerializer(serializers.Serializer):
    locker_count = serializers.IntegerField()
    guest_id = serializers.UUIDField()
    start_datetime = serializers.IntegerField()
    duration = serializers.IntegerField()
    locker_rate = serializers.DecimalField(decimal_places=2, max_digits=10)


class LockerForReturnSerializer(serializers.Serializer):
    locker_id = serializers.UUIDField()
    locker_code = serializers.CharField()


class LockerPosReturnSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    lockers = LockerForReturnSerializer(many=True)


class ShowerBookingPosSerializer(serializers.Serializer):
    shower_count = serializers.IntegerField()
    guest_id = serializers.UUIDField()
    start_datetime = serializers.IntegerField()
    duration = serializers.IntegerField()
    shower_rate = serializers.DecimalField(decimal_places=2, max_digits=10)


class ShowerPosReturnSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()


class RoomGuestSerializer(serializers.Serializer):
    pic_id = serializers.UUIDField()
    room_booking_id = serializers.UUIDField()


class GuestAssignationSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
    room_guest = RoomGuestSerializer(many=True)


class RegisterBookingRoomSerializer(serializers.Serializer):
    room_id = serializers.UUIDField()
    details = serializers.CharField(allow_blank=True, allow_null=True)


class RegisterBookingWithoutPicSerializer(serializers.Serializer):
    check_in_datetime = serializers.DateTimeField()
    check_out_datetime = serializers.DateTimeField()
    adult = serializers.IntegerField()
    child = serializers.IntegerField(allow_null=True)
    platform_id = serializers.UUIDField()
    ota_code = serializers.CharField(allow_blank=True, allow_null=True)
    details = serializers.CharField(allow_blank=True, allow_null=True)
    rooms = RegisterBookingRoomSerializer(many=True)
    customer_booked = serializers.CharField()


class FeedbackChartSerializer(serializers.Serializer):
    date = serializers.CharField(required=False)
    average_rate = serializers.DecimalField(decimal_places=1, max_digits=5)


class FeedbackGraphSerializer(serializers.Serializer):
    empty_feedback = serializers.IntegerField()
    filled_feedback = serializers.IntegerField()
    chart_data = FeedbackChartSerializer(many=True)
    start_date = serializers.CharField()
    end_date = serializers.CharField()


class PlatformManagementPageSerializer(serializers.Serializer):
    platform_id = serializers.UUIDField()
    booking_source = serializers.CharField()
    is_ota = serializers.BooleanField()
    ota_code = serializers.CharField()
    color_tags = serializers.CharField()
    archived = serializers.BooleanField()
    can_late_payment = serializers.BooleanField()


class EditPlatformSerializer(serializers.Serializer):
    platform_id = serializers.UUIDField()


class EditPlatformNameSerializer(EditPlatformSerializer):
    platform_name = serializers.CharField()


class EditPlatformIsOtaSerializer(EditPlatformSerializer):
    is_ota = serializers.BooleanField()


class EditPlatformOtaCodeSerializer(EditPlatformSerializer):
    ota_code = serializers.CharField()


class EditPlatformColorTagsSerializer(EditPlatformSerializer):
    color_tags = serializers.CharField()


class PlatformArchiveSerializer(EditPlatformSerializer):
    archived = serializers.BooleanField()


class EditPlatformCanLatePaymentSerializer(EditPlatformSerializer):
    can_late_payment = serializers.BooleanField()


class CreatePlatformSerializer(serializers.Serializer):
    platform = serializers.CharField()
    color_tags = serializers.CharField()
    can_late_payment = serializers.BooleanField()
    ota_code = serializers.CharField(allow_blank=True)


class GuestTrackingBookingStatus(serializers.Serializer):
    booking_status = serializers.CharField()
    check_in_datetime = serializers.CharField()
    check_out_datetime = serializers.CharField()
    max_check_out_datetime = serializers.CharField()


class GuestTrackingPlatform(serializers.Serializer):
    platform = serializers.CharField()


class GuestTrackingRoomBookings(serializers.Serializer):
    room_type = serializers.CharField(source="room.room_type.type_name")


class BookingSerializerForGuestTracking(serializers.Serializer):
    booking_id = serializers.CharField()
    booking_made_datetime = serializers.CharField()
    booking_no = serializers.CharField()
    booking_status = GuestTrackingBookingStatus(many=True)
    customer_booked = serializers.CharField()
    customer_staying_name = serializers.CharField()
    customer_staying_id=serializers.UUIDField()
    platform = GuestTrackingPlatform()
    room_bookings = GuestTrackingRoomBookings(many=True)
    ota_code = serializers.CharField()


class ManualConfirmBookingSerializer(serializers.Serializer):
    booking_id = serializers.UUIDField()
