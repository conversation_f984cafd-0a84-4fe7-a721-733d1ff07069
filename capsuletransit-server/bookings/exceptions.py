class BookingNotFound(Exception):
    pass


class AdultPax(Exception):
    pass


class RoomBooked(Exception):
    pass


class RoomNotExist(Exception):
    pass


class UnableCheckIn(Exception):
    pass


class UnableCheckout(Exception):
    pass


class UnableCancel(Exception):
    pass


class LockerBooked(Exception):
    pass


class LockerBookingNotExist(Exception):
    pass


class ShowerNotExist(Exception):
    pass


class UnableSubmitConsent(Exception):
    pass


class UnableSubmitPin(Exception):
    pass


class UnableTransferBooking(Exception):
    pass


class SettlePayment(Exception):
    pass


class TransferedBooking(Exception):
    pass



class UnableUpgradeRoom(Exception):
    pass

class UnableTransferRoom(Exception):
    pass


class UnableDoBooking(Exception):
    pass

class UnableReschedule(Exception):
    pass