from django.utils import timezone
import time

from rooms.models import Room
from bookings.utils import generate_booking_no

import pytest
from bookings.models import BookingNumberCounter, BookingSessionRoomLock, LockerBooking
from django.db.models import Q
from django.test import TestCase

# Create your tests here.
from django.db import transaction
def lock_booking_number_for_n(branch_name : str, n : int= 5):
    """
    Locks the booking number for n seconds for the given branch name
    """

    with transaction.atomic():
        bnc = BookingNumberCounter.objects.select_for_update().get(branch_name=branch_name)

        print(f"Locking {branch_name} for {n} seconds")
        time.sleep(n)
        print(f"Done locking {bnc.branch_name} Counter {bnc.counter}")


def get_booking_counter(branch_name : str, no_wait=False):
    """
    
    """
    with transaction.atomic():
        bnc = BookingNumberCounter.objects.select_for_update(nowait=no_wait).get(branch_name=branch_name)


        return bnc.counter
    
def test_booking_number_generation():
    booking_no = generate_booking_no("KLIA", 10)

    boooking_no1 = generate_booking_no("KLIA", 1)
    boooking_no2 = generate_booking_no("KLIA", 2)
    assert booking_no == "CKLIA-000010"
    assert boooking_no1 == "CKLIA-000001"
    assert boooking_no2 == "CKLIA-000002"


@pytest.mark.django_db
def test_bookroom_session_lock():
    # Create testing for room session lock
    session = BookingSessionRoomLock()

    room = Room.objects.all().first()
    session.room= room
    session.reset()

    assert session.token == ""
    assert session.validate_token("123") == True


    session.extend_session("123")
    assert session.validate_token("123") == True

    session.save()

    rooms = Room.objects.filter(
        Q(session_lock__isnull=True) |
        Q(session_lock__expiry_at__lt=timezone.now()) 
    )

    r = rooms.filter(room_id=room.room_id).exists()
    assert r == False

