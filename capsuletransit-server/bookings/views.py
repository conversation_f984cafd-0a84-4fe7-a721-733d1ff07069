import base64
import datetime as dt
import traceback
from collections import defaultdict
from datetime import datetime
import calendar

from bookings import exceptions, tasks
from bookings.models import Booking, BookingStatus, RoomBooking, ShowerBooking
from bookings.serializer import (
    ActiveTierDisplaySerializer,
    AddBookingRemarksSerializer,
    AddExtraGuestSerializer,
    BookingAddRoomSerializer,
    BookingCreationSerializer,
    BookingGuestPortofolioSerializer,
    BookingPaymentStatusInformationSerializer,
    BookingReadSerializer,
    BookingRoomTransferUpgradeSerializer,
    BookingSerializer,
    BookingSerializerForGuestTracking,
    BookingSerializerNew,
    ConsentFormSerializer,
    CreatePlatformSerializer,
    EditPlatformCanLatePaymentSerializer,
    EditPlatformColorTagsSerializer,
    EditPlatformIsOtaSerializer,
    EditPlatformNameSerializer,
    EditPlatformOtaCodeSerializer,
    ExpressBookingSerializer,
    ExpressRoomBookingRequestSerializer,
    ExtendLockerSerializer,
    ExtraGuestSerializer,
    FeedbackGraphSerializer,
    FeedbackSerializer,
    FeedbackWriteSerializer,
    GetConsentFormInterfaceSerializer,
    GuestAssignationSerializer,
    LockerBookingCreateSerializer,
    LockerBookingPosSerializer,
    LockerBookingSerializer,
    LockerPosReturnSerializer,
    ManualConfirmBookingSerializer,
    OtaReservationSerializer,
    OtaReservationSummary,
    POSGuestSearchSerializer,
    PlatformArchiveSerializer,
    PlatformManagementPageSerializer,
    RebindLockerSerializer,
    RegisterBookingWithoutPicSerializer,
    RescheduleBookingSerializer,
    RoomBookingSerializer,
    RoomSerializer,
    RoomSessionLockRequestSerializer,
    ShowerBookingCreateSerializer,
    ShowerBookingPosSerializer,
    ShowerBookingSerializer,
    ShowerPosReturnSerializer,
    SubmitConsentFormInterfaceSerializer,
    SubmitConsentSessionPinSerializer,
    TransferBookingSerializer,
    TransferLockerSerializer,
    UpdateBookingStatusInfoSerializer,
)
from bookings.services.booking import (
    create_express_booking,
    get_all_platforms_list,
    get_all_upcoming_bookings_list,
    register_booking,
    transfer_room,
)
from bookings.services.consentform import (
    create_session,
    update_session,
    upload_consent_form,
)
from bookings.services.lockerbooking import *
from bookings.services.showerbooking import *
from bookings.services.updateservice import update_booking_status_no_show
from bookings.tasks import mark_as_overstay
from django.db import connection, transaction
from django.db.models import (
    CharField,
    OuterRef,
    Prefetch,
    Q,
    Subquery,
    Value,
    Count,
    IntegerField,
)
from django.db.models.functions import Concat, TruncDate, Cast, TruncMonth, TruncWeek
from django.http import HttpRequest, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.timezone import datetime, timedelta
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rooms.models import PlatformTier, Room, RoomType
from rooms.serializers import RoomSerializerForExpressBookingRequest
from dateutil.relativedelta import relativedelta
from .services.cancelservice import cancel_web_booking
from bookings.services import points

from .services.booking import (
    add_room_booking_service,
    check_in,
    create_booking_for_pos_transaction,
    exist_room_booking,
    get_available_rooms,
    person_in_charge_exists,
    register_booking_without_pic,
    reserve_ota,
    transfer_booking,
    update_booking_remarks_by_id,
    update_booking_status,
    upgrade_current_room,
)


class BookingView(viewsets.ViewSet):
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-upcoming-bookings",
        url_name="get-all-upcoming-bookings",
        name="get-all-upcoming-bookings",
    )
    def get_all_upcoming_bookings(self, request):
        try:
            datas = get_all_upcoming_bookings_list(request)
            return Response(
                {"status": "success", "message": "booking lists sent", "datas": datas},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=AddBookingRemarksSerializer,
        operation_description="Updates booking remarks",
        responses={200: "success", 500: "internal server error"},
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="update-booking-remarks",
        url_name="update-booking-remarks",
        name="update-booking-remarks",
        permission_classes=[],
    )
    def update_remarks_by_id(self, request):
        serializer = AddBookingRemarksSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    update_booking_remarks_by_id(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "msg": "booking remarks edited",
                    },
                    status=status.HTTP_200_OK,
                )

            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except:
            return Response(
                {"status": "failed", "message": "server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @swagger_auto_schema(
    #     request_body=UpdateBookingStatusInfoSerializer,
    #     responses={
    #         200: "success",
    #         400: "bad request",
    #         500: "internal server error"
    #     }
    # )

    @swagger_auto_schema(
        request_body=BookingCreationSerializer,
        responses={200: "hello"},
        operation_description="Create booking",
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="register",
        url_name="register",
        name="register-booking",
        permission_classes=[IsAuthenticated],
    )
    def create_booking(self, request):
        req_data = request.data

        try:
            booking_data = register_booking(data=req_data, lot_id=request.user.lot_id)
            return Response(
                {
                    "status": "success",
                    "message": "created successfully",
                    "data": {"bookingId": booking_data.pk},
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.AdultPax as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.RoomBooked as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.UnableDoBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.BookingNotFound as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.UnableSubmitConsent as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="manual-confirm-booking",
        url_name="manual-confirm-booking",
        name="manual-confirm-booking",
        permission_classes=[IsAuthenticated],
    )
    def manual_confirm_booking(self, request):
        serializer = ManualConfirmBookingSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                booking = Booking.objects.filter(
                    booking_id=validated_data["booking_id"]
                ).first()

                if not booking:
                    return Response(
                        {"status": "failed", "message": "booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                booking.confirm_booking()

            return Response(
                {"status": "success", "message": "booking confirmed"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="register-no-pic",
        url_name="register-no-pic",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
        renderer_classes=(CamelCaseJSONRenderer,),
        parser_classes=(CamelCaseJSONParser,),
    )
    def create_booking_no_pic(self, request):
        serializer = RegisterBookingWithoutPicSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            booking_data = register_booking_without_pic(
                data=serializer, user=request.user
            )

            return Response(
                {
                    "status": "success",
                    "message": "created successfully",
                    "data": {"bookingId": booking_data.pk},
                },
                status=status.HTTP_200_OK,
            )

        except exceptions.UnableDoBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.BookingNotFound as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=False,
        url_path="update-booking-remarks",
        url_name="update-booking-remarks",
        name="update-booking-remarks",
        permission_classes=[],
    )
    def update_remarks_by_id(self, request):
        serializer = AddBookingRemarksSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    update_booking_remarks_by_id(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "msg": "booking remarks edited",
                    },
                    status=status.HTTP_200_OK,
                )

            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except:
            return Response(
                {"status": "failed", "message": "server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=UpdateBookingStatusInfoSerializer,
        operation_description="Cancel booking",
        responses={200: "success", 400: "bad request", 500: "internal server error"},
    )
    @action(
        detail=False,
        methods=["PUT"],
        url_path="cancel",
    )
    def cancel_booking(self, request):
        serializer = UpdateBookingStatusInfoSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    update_booking_status(data=serializer, action="cancel")
                return Response(
                    {
                        "status": "success",
                        "message": f"booking canceled",
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.UnableCancel as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=UpdateBookingStatusInfoSerializer,
        responses={200: "success", 400: "bad request", 500: "internal server error"},
        operation_description="Check in booking",
    )
    @action(
        detail=False,
        methods=["PUT"],
        url_path="check-in/(?P<booking_id>[0-9a-f-]+)",
    )
    def checkin_booking(self, request, booking_id):
        pin = request.GET.get("pin", None)
        try:
            with transaction.atomic():
                check_in(booking_id=booking_id, user=request.user, pin=pin)
                return Response(
                    {
                        "status": "success",
                        "message": f"check in success",
                    },
                    status=status.HTTP_200_OK,
                )

        except exceptions.BookingNotFound as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except exceptions.UnableCheckIn as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=UpdateBookingStatusInfoSerializer,
        operation_description="Check out booking",
        responses={200: "success", 400: "bad request", 500: "internal server error"},
    )
    @action(detail=False, methods=["PUT"], url_path="check-out")
    def checkout_booking(self, request):
        serializer = UpdateBookingStatusInfoSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    update_booking_status(data=serializer, action="check out")
                return Response(
                    {
                        "status": "success",
                        "message": f"checkout succcess",
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.UnableCheckout as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # @swagger_auto_schema(
    #         request_body=ExpressBookingSerializer

    # )
    @action(
        methods=["POST"],
        detail=False,
        url_path="express-booking",
        url_name="express-booking",
        permission_classes=[],
    )
    def express_booking(self, request):
        try:
            # Get the current room_id from the JSON body
            with transaction.atomic():
                serializer = ExpressBookingSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                room_booking = create_express_booking(serializer)

            return Response(
                {
                    "status": "success",
                    "message": "Room transfer successful",
                    "data": {"booking_id": room_booking.pk},
                }
            )

        except exceptions.RoomNotExist as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": "Server error", "error": str(e)}
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="update-expired-booking",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def update_status_expired_booking(self, request):
        with transaction.atomic():
            update_booking_status_no_show(accounts_lot_id=request.user.lot_id)
        try:
            return Response(
                {"status": "success", "message": "status updated"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "success", "message": "server, error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="update-overstay-booking",
        permission_classes=[IsAuthenticated],
    )
    def update_status_overstay_booking(self, request):
        try:
            with transaction.atomic():
                mark_as_overstay()
            return Response(
                {"status": "success", "message": "status updated"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "success", "message": "server, error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="cancel-website-booking",
        permission_classes=[IsAuthenticated],
    )
    def cancel_website_booking(self, request):
        with transaction.atomic():
            cancel_web_booking()
        try:
            return Response(
                {"status": "success", "message": "status updated"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "success", "message": "server, error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=TransferBookingSerializer,
        responses={200: "success"},
        operation_description="Transfer booking",
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="transfer-booking",
        url_name="transfer-booking",
        name="transfer-booking",
        permission_classes=[IsAuthenticated],
    )
    def transfer_booking(self, request):
        serializer = TransferBookingSerializer(data=request.data)

        try:
            with transaction.atomic():
                if not serializer.is_valid():
                    return Response(
                        {"status": "failed", "message": serializer.errors},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                room_transfered, message = transfer_booking(
                    serializer, user=request.user
                )

                return Response(
                    {
                        "status": "success",
                        "message": message,
                        "data": {"bookingId": room_transfered.pk},
                    },
                    status=status.HTTP_200_OK,
                )

        except exceptions.UnableTransferBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.TransferedBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_406_NOT_ACCEPTABLE,
            )

        except exceptions.SettlePayment as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_402_PAYMENT_REQUIRED,
            )

        except exceptions.BookingNotFound as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=RescheduleBookingSerializer,
        responses={
            200: "success",
            404: "not found",
            403: "failed",
            500: "server error",
        },
    )
    @action(
        methods=["PATCH"],
        detail=False,
        url_path="reschedule",
        url_name="reschedule",
        renderer_classes=(CamelCaseJSONRenderer,),
        parser_classes=(CamelCaseJSONParser,),
        permission_classes=[IsAuthenticated],
    )
    def reschedule_booking(self, request):
        serializer = RescheduleBookingSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():

                validated_data = serializer.validated_data

                try:
                    booking = Booking.objects.get(pk=validated_data["booking_id"])
                except:
                    return Response(
                        {"status": "failed", "message": "booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                current_datetime = timezone.now()

                current_booking_status = BookingStatus.objects.select_for_update().get(
                    booking_id=booking.pk, is_latest=True
                )

                # if (
                #     current_booking_status.check_in_datetime
                #     > validated_data["new_check_in_datetime"]
                # ):
                #     raise exceptions.UnableReschedule(
                #         "check in date must be above current time"
                #     )

                if (
                    current_booking_status.booking_status == "Check In"
                    or current_booking_status.booking_status == "Check Out"
                ):
                    raise exceptions.UnableReschedule(
                        "Only able to reschedule before check in"
                    )

                duration = (
                    current_booking_status.check_out_datetime
                    - current_booking_status.check_in_datetime
                )
                grace_period_duration = (
                    current_booking_status.max_check_out_datetime
                    - current_booking_status.check_out_datetime
                )

                new_check_in_datetime = validated_data["new_check_in_datetime"]
                new_check_out_datetime = (
                    validated_data["new_check_in_datetime"] + duration
                )
                new_max_check_out_datetime = (
                    new_check_out_datetime + grace_period_duration
                )

                room_bookings = RoomBooking.objects.filter(booking_id=booking.pk)

                room_ids = [room_book.room.pk for room_book in room_bookings]

                room_booking_exist = exist_room_booking(
                    start_date=new_check_in_datetime,
                    end_date=new_check_out_datetime,
                    room_ids=room_ids,
                    current_booking_id=booking.pk,
                )

                if len(room_booking_exist) > 0:
                    raise exceptions.UnableReschedule(
                        "there is an occupied room within the new duration"
                    )

                if current_booking_status.booking_status == "No Show":
                    current_booking_status.check_in_datetime = (new_check_in_datetime,)
                    current_booking_status.check_out_datetime = (
                        new_check_out_datetime,
                    )
                    current_booking_status.max_check_out_datetime = (
                        new_max_check_out_datetime,
                    )

                    current_booking_status.save()
                else:
                    current_booking_status.is_latest = False
                    current_booking_status.save()

                    new_booking_status = BookingStatus.objects.create(
                        booking_id=booking.pk,
                        booking_status=current_booking_status.booking_status,
                        check_in_datetime=new_check_in_datetime,
                        check_out_datetime=new_check_out_datetime,
                        status_datetime=datetime.now(),
                        max_check_out_datetime=new_max_check_out_datetime,
                        is_latest=True,
                        has_no_show=current_booking_status.has_no_show,
                    )

            return Response(
                {"status": "success", "message": "reschedule success"},
                status=status.HTTP_200_OK,
            )

        except exceptions.UnableReschedule as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="feedback-form",
        url_name="feedback-form",
        renderer_classes=(CamelCaseJSONRenderer,),
        parser_classes=(CamelCaseJSONParser,),
        permission_classes=[],
    )
    def feedback_form(self, request):
        serializer = FeedbackWriteSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                booking = Booking.objects.get(booking_no=validated_data["booking_no"])
            except:
                return Response(
                    {"status": "failed", "message": "booking not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            room_selected_info = validated_data["room_selected"]
            booking_lot = None
            if validated_data["room_selected"] == "":
                room_booking_exist = RoomBooking.objects.filter(
                    booking_id=booking.pk
                ).exists()
                if room_booking_exist:
                    return Response(
                        {
                            "status": "failed",
                            "message": "this booking have room bookings",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )

                locker_bookings = LockerBooking.objects.filter(booking_id=booking.pk)
                shower_bookings = ShowerBooking.objects.filter(booking_id=booking.pk)
                if locker_bookings.exists():
                    booking_lot = locker_bookings.first().locker.zone.lot_id
                    room_selected_info = "Locker Sales"

                if shower_bookings.exists():
                    room_selected_info = "Shower Sales"

                if (not locker_bookings.exists() and not shower_bookings.exists()) or (
                    locker_bookings.exists() and shower_bookings.exists()
                ):
                    room_selected_info = "Multiple POS Sales"

            else:
                room_booking = RoomBooking.objects.filter(booking_id=booking.pk).first()
                booking_lot = room_booking.room.room_type.roomzone.lot_id_id

            feedback_created = FeedBack.objects.create(
                booking_id=booking.pk,
                rating=validated_data["rating"],
                feedback=validated_data["feedback"],
                room_selected=room_selected_info,
                lot_id=booking_lot,
            )

            return_serializer = FeedbackSerializer(feedback_created)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="get-feedback-by-booking/(?P<booking_id>[0-9a-f-]+)",
        url_name="get-feedback-by-booking",
        renderer_classes=(CamelCaseJSONRenderer,),
        parser_classes=(CamelCaseJSONParser,),
        permission_classes=[],
    )
    def get_feedback_form_by_booking(self, request, booking_id):

        try:

            feedback_form = FeedBack.objects.filter(booking_id=booking_id)

            if not feedback_form.exists():
                return Response(
                    {"status": "failed", "message": "booking or feedback not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return_serializer = FeedbackSerializer(feedback_form, many=True)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["POST"],
        url_path="assign-guest",
        url_name="assign-guest",
        renderer_classes=(CamelCaseJSONRenderer,),
        parser_classes=(CamelCaseJSONParser,),
        permission_classes=[],
    )
    def guest_assign_for_room_booking(self, request):
        serializer = GuestAssignationSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                try:
                    booking = Booking.objects.get(pk=validated_data["booking_id"])
                except:
                    return Response(
                        {"status": "failed", "message": "booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                room_guest = validated_data["room_guest"]

                for item in room_guest:
                    room_booking = RoomBooking.objects.get(pk=item["room_booking_id"])
                    room_booking.person_in_charge_id = item["pic_id"]
                    room_booking.save()

                try:
                    customer = Customer.objects.get(
                        pk=validated_data["room_guest"][0]["pic_id"]
                    )
                except:
                    return Response(
                        {"status": "failed", "message": "customer not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                booking.customer_staying_id = customer.pk
                booking.customer_booked = f"{customer.firstname} {customer.lastname}"
                booking.save()

                transaction_list = Transaction.objects.filter(booking_id=booking.pk)

                for transaction_data in transaction_list:
                    transaction_data.customer_id = customer.pk
                    transaction_data.save()

            return Response(
                {
                    "status": "success",
                    "message": "guest assigned",
                    "data": {
                        "customerStayingId": customer.pk,
                        "name": f"{customer.firstname} {customer.lastname}",
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PlatformView(viewsets.ViewSet):

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-platforms",
        url_name="get-all-platforms",
        name="get-all-platforms",
    )
    def get_all_platforms(self, request):
        try:
            datas = get_all_platforms_list()
            return Response(
                {"status": "success", "message": "platform lists sent", "datas": datas},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PlatformViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = Platform.objects.all()
        return qs

    @action(
        detail=False,
        methods=["POST"],
        url_path="create-platform",
        url_name="create-platform",
        permission_classes=[],
    )
    def create_platform(self, request):
        serializer = CreatePlatformSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                platform = Platform.objects.create(
                    platform=validated_data["platform"],
                    color_tags=validated_data["color_tags"],
                    can_late_payment=validated_data["can_late_payment"],
                    ota_code=validated_data["ota_code"],
                    is_archive=False,
                )

            return Response(
                {
                    "status": "success",
                    "message": "Booking Source Created",
                    "data": platform.pk,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="active-tier-list",
        permission_classes=[],
    )
    def get_active_tier_display(self, request):

        try:
            qs = self.get_queryset().filter(is_archive=False)

            account_lot = request.user.lot_id

            active_tier_list = []
            for data in qs:
                try:
                    platform_tier = PlatformTier.objects.get(
                        platform_id=data.pk, tiers__lot_id=account_lot
                    )
                    current_tier = platform_tier.tiers
                except:
                    current_tier = None

                active_tier_list.append(
                    {"platform": data, "current_tier": current_tier}
                )

            serializer = ActiveTierDisplaySerializer(active_tier_list, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["GET"],
        url_path="platform-list",
        permission_classes=[],
    )
    def get_platform_list_for_management_page(self, request):
        try:
            qs = self.get_queryset()

            platform_data_list = []
            for platform in qs:
                is_ota = False
                if "OTA" in platform.platform:
                    is_ota = True
                platform_data_list.append(
                    {
                        "platform_id": platform.platform_id,
                        "booking_source": platform.platform,
                        "is_ota": is_ota,
                        "ota_code": platform.ota_code,
                        "color_tags": platform.color_tags,
                        "archived": platform.is_archive,
                        "can_late_payment": platform.can_late_payment,
                    }
                )

            return_serializer = PlatformManagementPageSerializer(
                platform_data_list, many=True
            )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="change-name",
        permission_classes=[],
    )
    def change_platform_name(self, request):
        serializer = EditPlatformNameSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            platform.platform = validated_data["platform_name"]
            platform.save()

            return Response(
                {"status": "success", "message": "platform name edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="is-ota",
        permission_classes=[],
    )
    def change_platform_is_ota(self, request):
        serializer = EditPlatformIsOtaSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if not "OTA" in platform.platform and validated_data["is_ota"]:
                platform.platform = f"OTA - {platform.platform}"
                platform.save()

            if "OTA" in platform.platform and not validated_data["is_ota"]:
                current_name = platform.platform
                new_name = current_name[len("OTA -") :].strip()
                platform.platform = new_name
                platform.save()

            return Response(
                {"status": "success", "message": "platform ota edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="ota-code",
        permission_classes=[],
    )
    def change_platform_ota_code(self, request):
        serializer = EditPlatformOtaCodeSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            platform.ota_code = validated_data["ota_code"]
            platform.save()

            return Response(
                {"status": "success", "message": "platform ota code edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="color-tags",
        permission_classes=[],
    )
    def change_platform_color_tags(self, request):
        serializer = EditPlatformColorTagsSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            platform.color_tags = validated_data["color_tags"]
            platform.save()

            return Response(
                {"status": "success", "message": "platform ota code edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="can-late-payment",
        permission_classes=[],
    )
    def change_platform_can_late_payment(self, request):
        serializer = EditPlatformCanLatePaymentSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            platform.can_late_payment = validated_data["can_late_payment"]
            platform.save()

            return Response(
                {"status": "success", "message": "platform can late payment edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PATCH"],
        url_path="archive",
        permission_classes=[],
    )
    def archive_platform(self, request):
        serializer = PlatformArchiveSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                platform = Platform.objects.get(pk=validated_data["platform_id"])
            except:
                return Response(
                    {"status": "failed", "message": "platform not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            platform.is_archive = validated_data["archived"]
            platform.save()

            return Response(
                {
                    "status": "success",
                    "message": f"platform {'archived' if validated_data['archived'] else 'unarchived'}",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class BookingGuestPortofolioView(viewsets.ViewSet):
    serializer_class = BookingGuestPortofolioSerializer
    queryset = Booking.objects.all()
    renderer_classes = (CamelCaseJSONRenderer,)
    authentication_classes = []
    permission_classes = []

    # lookup_field = "booking_id"
    @swagger_auto_schema(
        responses={200: BookingGuestPortofolioSerializer},
        operation_description="Get guest portofolio by booking id",
    )
    def retrieve(self, request, pk=None):
        queryset = Booking.objects.all()
        room = get_object_or_404(queryset, pk=pk)
        serializer = BookingGuestPortofolioSerializer(room)
        data = serializer.data

        for booking in data["room_bookings"]:
            room = Room.objects.get(pk=booking["room"])
            room_max_pax = room.room_type.max_pax

            filtered_locker = [
                i
                for i in data["locker_bookings"]
                if i["room_code"] == booking["room_code"]
            ]

            length = len(filtered_locker)

            if length > room_max_pax:
                for i in range(room_max_pax, length):
                    filtered_locker[i]["is_extra"] = True

        for locker_book in data["locker_bookings"]:
            if locker_book["status"] == "Rental":
                del locker_book["is_extra"]
                locker_book["is_extended"] = True

                start_time = dt.fromisoformat(
                    locker_book["locker_start_rent_datetime"].replace("Z", "+00:00")
                )
                end_time = dt.fromisoformat(
                    locker_book["locker_end_rent_datetime"].replace("Z", "+00:00")
                )

                extend_duration = end_time - start_time
                extend_duration = extend_duration.total_seconds() / 3600
                locker_book["extend_duration_hour"] = int(extend_duration)

        return Response(data)


class BookingViewSet(viewsets.ModelViewSet):
    serializer_class = BookingSerializerNew
    queryset = Booking.objects.all()
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = []
    permission_classes = []
    lookup_field = "booking_id"

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "days",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="Filter booking in latest how many days",
            )
        ]
    )
    def list(self, request):
        days = request.query_params.get("days", None)
        qs = Booking.objects.all()

        if days:
            days = int(days)
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            qs = qs.filter(booking_made_datetime__range=(start_date, end_date))

        serializer = BookingSerializer(qs, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = super().create(request, *args, **kwargs)
        return Response(
            {
                "status": "success",
                "message": "booking created",
                "data": serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    def retrieve(self, request, booking_id):
        queryset = Booking.objects.all()
        booking = get_object_or_404(queryset, pk=booking_id)
        serializer = BookingReadSerializer(booking)
        return Response(serializer.data)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 100
    page_size_query_param = "page_size"
    max_page_size = 100


from bookings.serializer import BookingSerializerForCheckinOut


class BookingListView(generics.ListAPIView):
    serializer_class = BookingSerializerForCheckinOut
    queryset = Booking.objects.all()
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    # pagination_class = StandardResultsSetPagination
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    lookup_field = "booking_id"

    def get_queryset(self):
        qs = super().get_queryset()
        qs = qs.select_related("platform")
        qs = tasks.annotate_overstay_duration_for_booking(qs)
        qs = qs.prefetch_related(
            Prefetch(
                "room_bookings",
                queryset=RoomBooking.objects.select_related(
                    "room",
                    "person_in_charge",
                    "room__room_type",
                    "room__room_type__roomzone",
                ).distinct("booking_id"),
            ),
            Prefetch(
                "booking_status", queryset=BookingStatus.objects.filter(is_latest=True)
            ),
            "customer_staying",
            "platform",
        )

        return qs

    def list(self, request, *args, **kwargs):
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        customer_staying_id = request.GET.get("customerStayingId", None)
        each_customer_latest_booking = request.GET.get(
            "eachCustomerLatestBooking", None
        )
        for_upcoming_bookings = request.GET.get("forUpcomingBookings", None)
        for_inhouse_guest = request.GET.get("forInhouseGuest", None)
        for_guest_tracking = request.GET.get("forGuestTracking", None)
        for_locker_and_shower_booking_list = request.GET.get(
            "forLockerAndShowerBookingList", None
        )
        booking_no = request.GET.get("bookingNo", None)
        lot_filter = request.GET.get("lot", None)

        qs = self.get_queryset()
        # qs = qs.filter(Q(customer_booked__isnull=False)).exclude(
        #     Q(customer_booked = "") |
        #     Q(platform__platform = 'OTA-Reservation Slot')
        # )
        qs.exclude(platform__platform="OTA-Reservation Slot")

        if lot_filter:
            qs = qs.filter(lot_id=lot_filter)
        else:
            qs = qs.filter(lot_id=request.user.lot_id)

        qs = qs.annotate(
            customer_staying_name=Concat(
                "customer_staying__firstname",
                Value(" "),
                "customer_staying__lastname",
                output_field=CharField(),
            )
        )

        qs = qs.exclude(booking_no=None)

        qs = qs.order_by("booking_made_datetime")
        qs = qs.distinct("booking_id", "booking_made_datetime")
        qs = qs.exclude(customer_booked="Non Guest")

        if start_date_time and end_date_time:
            start_date = datetime.fromtimestamp(int(start_date_time))
            end_date = datetime.fromtimestamp(int(end_date_time))

            start_date = timezone.make_aware(
                start_date, timezone.get_current_timezone()
            )

            end_date = timezone.make_aware(end_date, timezone.get_current_timezone())

            if for_upcoming_bookings:
                qs = qs.filter(
                    Q(booking_status__is_latest=True)
                    & Q(booking_status__check_in_datetime__range=(start_date, end_date))
                )
            elif for_inhouse_guest:
                qs = qs.filter(
                    Q(booking_status__is_latest=True)
                    & Q(booking_status__check_in_datetime__range=(start_date, end_date))
                )
            elif for_guest_tracking:
                qs = qs.filter(
                    Q(booking_status__is_latest=True)
                    & Q(
                        Q(
                            booking_status__check_in_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                    )
                )
            else:
                qs = qs.filter(booking_made_datetime__range=(start_date, end_date))

        if booking_no:
            qs = qs.filter(booking_no__icontains=booking_no)
            serializer = BookingSerializerForCheckinOut(qs, many=True)
            return Response(serializer.data)

        if each_customer_latest_booking:

            none_customer_qs = qs.filter(customer_staying__isnull=True)

            latest_booking_subquery = (
                Booking.objects.filter(Q(customer_staying=OuterRef("customer_staying")))
                .order_by("-booking_made_datetime")
                .values("booking_made_datetime")[:1]
            )

            qs = qs.annotate(
                latest_booking_made_datetime=Subquery(latest_booking_subquery)
            )
            qs = qs.filter(booking_made_datetime=F("latest_booking_made_datetime"))

            qs = qs | none_customer_qs

        if for_upcoming_bookings:
            qs = qs.exclude(
                Q(booking_status__is_latest=True)
                & Q(
                    Q(booking_status__booking_status="Check In")
                    | Q(booking_status__booking_status="Check Out")
                    | Q(booking_status__booking_status="Overstayed")
                    | Q(booking_status__booking_status="Cancelled")
                    | Q(booking_status__booking_status="Reservation")
                )
            )

        if for_locker_and_shower_booking_list:
            bookings_with_roombookings = RoomBooking.objects.values(
                "booking_id"
            ).distinct()
            qs = qs.exclude(booking_id__in=Subquery(bookings_with_roombookings))

            qs = qs.prefetch_related(
                Prefetch(
                    "locker_bookings",
                    queryset=LockerBooking.objects.select_related("locker")
                    .distinct("booking_id")
                    .all(),
                ),
                Prefetch(
                    "shower_bookings",
                    queryset=ShowerBooking.objects.select_related("shower")
                    .distinct("booking_id")
                    .all(),
                ),
            )
        else:
            bookings_with_roombookings = RoomBooking.objects.values(
                "booking_id"
            ).distinct()
            qs = qs.filter(booking_id__in=Subquery(bookings_with_roombookings))

        # if for_inhouse_guest:
        #     qs = qs.exclude(
        #         Q(
        #             Q(booking_status__booking_status = 'Booked') |
        #             Q(booking_status__booking_status = 'Confirm Booking') |
        #             Q(booking_status__booking_status = 'No Show') |
        #             Q(booking_status__booking_status = 'Cancelled') |
        #             Q(booking_status__booking_status = 'Reservation')
        #         ) &
        #         Q(booking_status__is_latest = True)
        #     )

        if customer_staying_id:
            qs = qs.filter(customer_staying_id=customer_staying_id)

        serializer = BookingSerializerForCheckinOut(qs, many=True)

        if for_guest_tracking or each_customer_latest_booking:
            serializer = BookingSerializerForGuestTracking(qs, many=True)

        return Response(serializer.data)


class BookingListViewV2(generics.ListAPIView):
    serializer_class = BookingSerializerForCheckinOut
    queryset = Booking.objects.all()
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    pagination_class = StandardResultsSetPagination  # Enable pagination here
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    lookup_field = "booking_id"

    def get_queryset(self):
        qs = super().get_queryset()
        qs = qs.select_related("platform")
        qs = tasks.annotate_overstay_duration_for_booking(qs)
        qs = qs.prefetch_related(
            Prefetch(
                "room_bookings",
                queryset=RoomBooking.objects.select_related(
                    "room",
                    "person_in_charge",
                    "room__room_type",
                    "room__room_type__roomzone",
                ).distinct("booking_id"),
            ),
            Prefetch(
                "booking_status", queryset=BookingStatus.objects.filter(is_latest=True)
            ),
            "customer_staying",
            "platform",
        )
        return qs

    def list(self, request, *args, **kwargs):
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        customer_staying_id = request.GET.get("customerStayingId", None)
        each_customer_latest_booking = request.GET.get(
            "eachCustomerLatestBooking", None
        )
        for_upcoming_bookings = request.GET.get("forUpcomingBookings", None)
        for_inhouse_guest = request.GET.get("forInhouseGuest", None)
        for_guest_tracking = request.GET.get("forGuestTracking", None)
        for_locker_and_shower_booking_list = request.GET.get(
            "forLockerAndShowerBookingList", None
        )
        booking_no = request.GET.get("bookingNo", None)
        lot_filter = request.GET.get("lot", None)
        global_search = request.GET.get("globalSearch", None)

        qs = self.get_queryset()
        qs.exclude(platform__platform="OTA-Reservation Slot")

        if lot_filter:
            qs = qs.filter(lot_id=lot_filter)
        else:
            qs = qs.filter(lot_id=request.user.lot_id)

        qs = qs.annotate(
            customer_staying_name=Concat(
                "customer_staying__firstname",
                Value(" "),
                "customer_staying__lastname",
                output_field=CharField(),
            )
        )

        qs = qs.exclude(booking_no=None)
        qs = qs.order_by("booking_made_datetime")
        qs = qs.distinct("booking_id", "booking_made_datetime")
        qs = qs.exclude(customer_booked="Non Guest")

        if start_date_time and end_date_time:
            start_date = datetime.fromtimestamp(int(start_date_time))
            end_date = datetime.fromtimestamp(int(end_date_time))
            start_date = timezone.make_aware(
                start_date, timezone.get_current_timezone()
            )
            end_date = timezone.make_aware(end_date, timezone.get_current_timezone())

            if for_upcoming_bookings:
                qs = qs.filter(
                    Q(booking_status__is_latest=True)
                    & Q(booking_status__check_in_datetime__range=(start_date, end_date))
                )
            elif for_inhouse_guest or for_guest_tracking:
                qs = qs.filter(
                    Q(booking_status__is_latest=True)
                    & Q(booking_status__check_in_datetime__range=(start_date, end_date))
                )
            else:
                qs = qs.filter(booking_made_datetime__range=(start_date, end_date))

        if booking_no:
            qs = qs.filter(booking_no__icontains=booking_no)

        if each_customer_latest_booking:
            none_customer_qs = qs.filter(customer_staying__isnull=True)
            latest_booking_subquery = (
                Booking.objects.filter(Q(customer_staying=OuterRef("customer_staying")))
                .order_by("-booking_made_datetime")
                .values("booking_made_datetime")[:1]
            )
            qs = qs.annotate(
                latest_booking_made_datetime=Subquery(latest_booking_subquery)
            )
            qs = qs.filter(booking_made_datetime=F("latest_booking_made_datetime"))
            qs = qs | none_customer_qs

        if for_upcoming_bookings:
            qs = qs.exclude(
                Q(booking_status__is_latest=True)
                & Q(
                    Q(booking_status__booking_status="Check In")
                    | Q(booking_status__booking_status="Check Out")
                    | Q(booking_status__booking_status="Overstayed")
                    | Q(booking_status__booking_status="Cancelled")
                    | Q(booking_status__booking_status="Reservation")
                )
            )

        if for_locker_and_shower_booking_list:
            bookings_with_roombookings = RoomBooking.objects.values(
                "booking_id"
            ).distinct()
            qs = qs.exclude(booking_id__in=Subquery(bookings_with_roombookings))
            qs = qs.prefetch_related(
                Prefetch(
                    "locker_bookings",
                    queryset=LockerBooking.objects.select_related("locker")
                    .distinct("booking_id")
                    .all(),
                ),
                Prefetch(
                    "shower_bookings",
                    queryset=ShowerBooking.objects.select_related("shower")
                    .distinct("booking_id")
                    .all(),
                ),
            )
        else:
            bookings_with_roombookings = RoomBooking.objects.values(
                "booking_id"
            ).distinct()
            qs = qs.filter(booking_id__in=Subquery(bookings_with_roombookings))

        if customer_staying_id:
            qs = qs.filter(customer_staying_id=customer_staying_id)

        if global_search and global_search != "":
            qs = qs.filter(
                Q(
                    Q(customer_staying__firstname__icontains=global_search)
                    | Q(customer_staying__lastname__icontains=global_search)
                    | Q(customer_staying__firstname__icontains=global_search.split()[0])
                    & Q(customer_staying__lastname__icontains=global_search.split()[-1])
                )
                | Q(booking_no__icontains=global_search)
                | Q(ota_code__icontains=global_search)
                | Q(customer_booked__icontains=global_search)
            )

        page = self.paginate_queryset(qs)
        if page is not None:
            if for_guest_tracking or each_customer_latest_booking:
                serializer = BookingSerializerForGuestTracking(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(qs, many=True)

        return Response(serializer.data)


class BookingRoomActionViewset(viewsets.ViewSet):
    # ... other viewset code ...

    @action(
        methods=["GET"],
        detail=False,
        url_path="generate-room-availability",
        url_name="generate-room-availability",
        permission_classes=[],
    )
    def generate_room_availability(self, request):
        try:
            # Get client's requirements from the request, you can modify this as needed
            room_type = request.query_params.get("room_type")
            availability_duration = int(
                request.query_params.get("availability_duration")
            )
            # You can add more conditions based on client's requirements

            # Query the database to find available rooms based on client's requirements
            now = datetime.now()
            # Calculate check-out time
            end_time = now + timedelta(days=availability_duration)

            available_rooms = (
                Room.objects.filter(
                    room_type__type_name=room_type,  # Same room type
                    status="vacant, cleaned",  # Vacant and clean rooms
                )
                .exclude(
                    # Check if the room is already booked during the requested duration
                    Q(
                        Q(
                            room_bookings__start_time__lte=now,
                            room_bookings__end_time__gte=now,
                        )
                        | Q(
                            room_bookings__start_time__lte=end_time,
                            room_bookings__end_time__gte=end_time,
                        )
                    )
                )
                .filter(status="vacant, cleaned")
            )  # Check if the room is vacant and clean

            # Serialize the available rooms using RoomSerializer
            availability_data = RoomSerializer(available_rooms, many=True).data

            return Response(
                {
                    "status": "success",
                    "message": "Room availability generated",
                    "data": availability_data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "Failed to generate room availability",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=BookingAddRoomSerializer,
        responses={200: "OK"},
        operation_description="Add Room Booking",
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="add-room-booking",
        url_name="add-room-booking",
        permission_classes=[],
    )
    def add_room_booking(self, request):
        serializer = BookingAddRoomSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            with transaction.atomic():
                add_room_booking_service(serializer=serializer, user=request.user)
            return Response(
                {
                    "status": "success",
                    "message": "Room booking added",
                }
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "Room booking failed", "error": str(e)}
            )

    @swagger_auto_schema(
        request_body=BookingRoomTransferUpgradeSerializer,
        responses={200: "OK"},
        operation_description="Transfer Room Booking to another room",
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="room-transfer",
        url_name="room-transfer",
        permission_classes=[],
    )
    def room_transfer(self, request):
        try:
            # Get the current room_id from the JSON body
            with transaction.atomic():
                serializer = BookingRoomTransferUpgradeSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                room_booking = transfer_room(serializer=serializer, user=request.user)

            return Response(
                {"status": "success", "message": "Room transfer successful"}
            )

        except exceptions.UnableTransferRoom as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": "Room transfer failed", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=BookingRoomTransferUpgradeSerializer,
        responses={200: "OK"},
        operation_description="Upgrade Room Booking to a higher tier room",
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="upgrade-room",
        url_name="upgrade-room",
        permission_classes=[IsAuthenticated],
    )
    def upgrade_room(self, request):
        try:
            # Get the current room_id from the JSON body
            serializer = BookingRoomTransferUpgradeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            with transaction.atomic():
                if upgrade_current_room(
                    serializer.validated_data["room_booking_id"],
                    serializer.validated_data["new_room_id"],
                    request.user,
                ):
                    return Response(
                        {"status": "success", "message": "Room transfer successful"}
                    )
            return Response({"status": "error", "message": "Room transfer failed."})

        except exceptions.UnableUpgradeRoom as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": "Room transfer failed", "error": str(e)}
            )


class BookingLockerView(viewsets.ViewSet):
    queryset = LockerBooking.objects.all()
    serializer_class = LockerBookingSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def create(self, request):
        serializer = LockerBookingCreateSerializer(data=request.data)

        if serializer.is_valid():
            with transaction.atomic():
                create_locker_booking(data=serializer, user=request.user)
            return Response(
                {
                    "status": "success",
                    "message": "locker registered",
                },
                status=status.HTTP_200_OK,
            )
        try:

            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.LockerBooked as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=ExtendLockerSerializer,
        responses={
            200: "success",
            400: "bad request",
            403: "forbidden",
            500: "internal server error",
        },
    )
    @action(detail=False, methods=["PUT"], url_path="locker-extend")
    def locker_extend(self, request):
        serializer = ExtendLockerSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    extend_locker(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "message": f"locker extend success",
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.LockerBooked as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=TransferLockerSerializer,
        responses={
            200: "success",
            400: "bad request",
            403: "forbidden",
            404: "not found",
            500: "internal server error",
        },
    )
    @action(detail=False, methods=["PUT"], url_path="locker-transfer")
    def locker_transfer(self, request):
        serializer = TransferLockerSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    transfer_locker(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "message": f"locker transfer success",
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.LockerBooked as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except exceptions.LockerBookingNotExist as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=RebindLockerSerializer,
        responses={
            200: "success",
            400: "bad request",
            404: "not found",
            500: "internal server error",
        },
    )
    @action(detail=False, methods=["PUT"], url_path="locker-rebind")
    def locker_rebind(self, request):
        serializer = RebindLockerSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    rebind_locker(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "message": f"rebind locker success",
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.LockerBookingNotExist as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["POST"], url_path="locker-pos")
    def locker_booking_pos(self, request):
        lot = request.user.lot_id
        serializer = LockerBookingPosSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "msg": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                available_locker = Locker.objects.filter(
                    status="Available", zone__lot=lot, zone__is_archive=False
                )

                if available_locker.count() < int(validated_data["locker_count"]):
                    return Response(
                        {
                            "status": "failed",
                            "message": "available locker insufficient",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )

                available_locker = available_locker[
                    : int(validated_data["locker_count"])
                ]

                locker_rate = validated_data["locker_rate"]
                locker_rate_calculated = (
                    locker_rate * validated_data["locker_count"] if locker_rate else 0
                )

                start_datetime = datetime.fromtimestamp(
                    int(validated_data["start_datetime"])
                )
                start_datetime = timezone.make_aware(
                    start_datetime, timezone.get_current_timezone()
                )

                end_datetime = start_datetime + timedelta(
                    hours=validated_data["duration"]
                )

                platform = Platform.objects.filter(platform="Locker").first()

                new_booking: Booking = create_booking_for_pos_transaction(
                    item_sum=locker_rate_calculated,
                    booking_status="Booked",
                    user=request.user,
                    guest_id=validated_data["guest_id"],
                    start_date=start_datetime,
                    end_date=end_datetime,
                    platform_id=platform.pk if platform else None,
                )

                locker_booking_list = []
                locker_pos_return_list = []
                for locker in available_locker:
                    locker_booking_list.append(
                        LockerBooking(
                            booking_id=new_booking.pk,
                            locker_id=locker.pk,
                            sum=locker_rate,
                            status="Booked",
                            locker_start_rent_datetime=start_datetime,
                            locker_end_rent_datetime=end_datetime,
                            room_code="0",
                        )
                    )
                    locker_pos_return_list.append(
                        {"locker_id": locker.pk, "locker_code": locker.code}
                    )
                    locker.status = LOCKERSTATUS.OCCUPIED
                    locker.save()

                LockerBooking.objects.bulk_create(locker_booking_list)

                return_serializer = LockerPosReturnSerializer(
                    {"booking_id": new_booking.pk, "lockers": locker_pos_return_list}
                )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["PUT"],
        url_path="cancel-locker-booking/(?P<locker_booking_id>[0-9a-f-]+)",
        url_name="cancel-locker-booking",
    )
    def locker_booking_cancel(self, request, locker_booking_id):

        try:
            with transaction.atomic():
                try:
                    locker_booking = LockerBooking.objects.get(pk=locker_booking_id)

                except:
                    return Response(
                        {"status": "failed", "message": "locker booking not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                if locker_booking.status == "Cancelled":
                    return Response(
                        {
                            "status": "failed",
                            "message": "this locker booking already cancelled",
                        },
                        status=status.HTTP_406_NOT_ACCEPTABLE,
                    )

                booking = locker_booking.booking

                not_room_booking_transactions = Transaction.objects.filter(
                    booking_id=booking.pk, is_room_booking=False
                )

                is_refunded_or_void = False

                refund_or_void_transaction = not_room_booking_transactions.filter(
                    transaction_status__in=["Void", "Refund"]
                )

                for transaction_obj in refund_or_void_transaction:
                    for item in transaction_obj.items:
                        try:
                            if item["item_id"] == str(locker_booking.locker.pk):
                                is_refunded_or_void = True
                        except:
                            continue

                is_paid_or_pending = False

                if not is_refunded_or_void:
                    paid_or_pending_transaction = not_room_booking_transactions.filter(
                        transaction_status__in=["Pending Payment", "Paid"]
                    )

                    for transaction_obj in paid_or_pending_transaction:
                        for item in transaction_obj.items:
                            try:
                                if item["item_id"] == str(locker_booking.locker.pk):
                                    is_paid_or_pending = True
                            except:
                                continue

                if not is_refunded_or_void and not is_paid_or_pending:
                    return Response(
                        {
                            "status": "failed",
                            "message": "this locker is free you dumb ass",
                        },
                        status=status.HTTP_406_NOT_ACCEPTABLE,
                    )

                if is_paid_or_pending:
                    return Response(
                        {
                            "status": "failed",
                            "message": "need to refund or void the transaction first",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )

                locker_booking.status = "Cancelled"
                locker_booking.save()

                locker_booking.locker.status = "Available"
                locker_booking.locker.save()

            return Response(
                {"status": "success", "message": "locker cancelled"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["POST"], url_path="shower-pos")
    def shower_booking_pos(self, request):
        """
        NOTE:
        this will only create new booking without shower assigned.
        shower will be assigned inside guest profile page with card scanner
        """

        serializer = ShowerBookingPosSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "msg": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():

                shower_rate = validated_data["shower_rate"]
                shower_rate_calculated = (
                    shower_rate * validated_data["shower_count"] if shower_rate else 0
                )

                start_datetime = datetime.fromtimestamp(
                    int(validated_data["start_datetime"])
                )
                start_datetime = timezone.make_aware(
                    start_datetime, timezone.get_current_timezone()
                )

                end_datetime = start_datetime + timedelta(
                    hours=validated_data["duration"]
                )

                platform = Platform.objects.filter(platform="Shower").first()

                new_booking: Booking = create_booking_for_pos_transaction(
                    item_sum=shower_rate_calculated,
                    booking_status="Booked",
                    user=request.user,
                    guest_id=validated_data["guest_id"],
                    start_date=start_datetime,
                    end_date=end_datetime,
                    platform_id=platform.pk if platform else None,
                )
                shower_booking_list = []
                for i in range(int(validated_data["shower_count"])):
                    shower_booking_list.append(
                        ShowerBooking(
                            booking_id=new_booking.pk,
                            shower=None,
                            sum=shower_rate,
                            status="Booked",
                            shower_start_rent_datetime=start_datetime,
                            shower_end_rent_datetime=end_datetime,
                        )
                    )
                ShowerBooking.objects.bulk_create(shower_booking_list)

                return_serializer = ShowerPosReturnSerializer(
                    {"booking_id": new_booking.pk}
                )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class RoomBookingViewSet(viewsets.ViewSet):
    parser_classes = (CamelCaseJSONParser,)
    renderer_classes = (CamelCaseJSONRenderer,)
    authentication_classes = []
    permission_classes = []

    @action(
        detail=False,
        methods=["GET"],
        url_path="(?P<booking_no>[a-zA-Z0-9-]+)",
        url_name="with-booking-no",
    )
    def get_roombooking_by_booking_no(self, request, booking_no):

        try:

            room_bookings = RoomBooking.objects.filter(booking__booking_no=booking_no)

            # if not room_bookings.exists():
            #     return Response(
            #         {
            #             "status": "failed",
            #             "message": "this booking dont have any room bookings, or booking not found",
            #         },
            #         status=status.HTTP_404_NOT_FOUND,
            #     )

            return_serializer = RoomBookingSerializer(room_bookings, many=True)

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class BookingRoomSessionLockView(APIView):
    parser_classes = (CamelCaseJSONParser,)
    renderer_classes = (CamelCaseJSONRenderer,)
    authentication_classes = []
    permission_classes = []

    @swagger_auto_schema(
        request_body=RoomSessionLockRequestSerializer,
        responses={200: "OK"},
        operation_description="Lock Room Session making it unavailable for room listing and booking until the session is expired",
    )
    def post(self, request: HttpRequest):
        serializer = RoomSessionLockRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        room_ids = serializer.validated_data["room_ids"]
        token = serializer.validated_data["token"]
        try:
            with transaction.atomic():
                for room_id in room_ids:
                    (
                        session_room_lock,
                        created,
                    ) = BookingSessionRoomLock.objects.get_or_create(
                        room_id=room_id,
                        defaults={
                            "token": token,
                            "room_id": room_id,
                            "expiry_at": timezone.now()
                            + timedelta(minutes=1, seconds=30),
                        },
                    )
                    if not created:
                        session_room_lock.extend_session(token)
                    session_room_lock.save()
            return Response({"status": "success", "message": "Room session locked"})
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "Room session lock failed",
                    "error": str(e),
                }
            )


class ShowerBookingViewSet(viewsets.ViewSet):
    queryset = ShowerBooking.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = ShowerBookingSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    @swagger_auto_schema(
        request_body=ShowerBookingCreateSerializer,
        responses={
            200: "success",
            400: "bad request",
            404: "not found",
            500: "internal server error",
        },
    )
    def create(self, request):
        serializer = ShowerBookingCreateSerializer(data=request.data)

        try:
            if serializer.is_valid():
                with transaction.atomic():
                    register_shower_booking(data=serializer)
                return Response(
                    {
                        "status": "success",
                        "message": "shower registered",
                    },
                    status=status.HTTP_200_OK,
                )

            return Response(
                {
                    "status": "failed",
                    "msg": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except exceptions.ShowerNotExist as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )

        except:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ExtraGuestViewSet(viewsets.ViewSet):
    queryset = ExtraGuest.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = ExtraGuestSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def list(self, request):
        booking_id = request.query_params.get("bookingId", None)
        guest_name = request.query_params.get("name", None)

        qs = self.queryset.all()

        if booking_id:
            qs = qs.filter(booking_id=booking_id)

        if guest_name:
            qs = qs.filter(name=guest_name)

        serializer = ExtraGuestSerializer(qs, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        queryset = Customer.objects.all()
        room = get_object_or_404(queryset, pk=pk)
        serializer = ExtraGuestSerializer(room)
        return Response(serializer.data)

    def create(self, request):
        serializer = AddExtraGuestSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.data

            extra_guest_list = []
            for guest in data["extra_guests"]:
                id_type = IDType.objects.get(id_type_id=guest["id_type"])
                extra_guest_list.append(
                    ExtraGuest(
                        booking_id=data["booking"],
                        name=guest["name"],
                        id_no=guest["id_no"],
                        id_type=id_type,
                    )
                )
            ExtraGuest.objects.bulk_create(extra_guest_list)

            return Response(
                {
                    "status": "success",
                    "message": "extra guest registered",
                },
                status=status.HTTP_201_CREATED,
            )
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        queryset = ExtraGuest.objects.all()
        customer = get_object_or_404(queryset, pk=pk)
        serializer = ExtraGuestSerializer(customer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        queryset = ExtraGuest.objects.all()
        room = get_object_or_404(queryset, pk=pk)
        room.delete()
        return Response(
            {"status": "success", "message": "extra guest deleted"},
            status=status.HTTP_200_OK,
        )


class ExpressBookingGenerationViewset(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []
    serializer_class = RoomSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    @swagger_auto_schema(
        request_body=ExpressRoomBookingRequestSerializer,
        responses={
            200: RoomSerializerForExpressBookingRequest,
            400: "bad request",
            500: "internal server error",
        },
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="generate-rooms",
        url_name="generate-rooms",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def generate_rooms(self, request):
        # room for express booking
        generated_rooms = []
        serializer = ExpressRoomBookingRequestSerializer(data=request.data)
        union = None
        total_rooms_requested = 0

        try:
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            rooms = serializer.validated_data["rooms"]
            check_in_datetime = serializer.validated_data["check_in_datetime"]
            added_duration_datetime = check_in_datetime + timedelta(
                hours=serializer.validated_data["duration"]
            )
            for room in rooms:
                room_type_name = room["room_type_name"]
                count = room["count"]
                total_rooms_requested += count
                available_rooms = Room.available_objects.select_related(
                    "room_type", "room_type__roomzone"
                ).filter(
                    Q(
                        room_type__type_name=room_type_name,
                        room_type__roomzone__lot_id_id=request.user.lot_id,
                    )
                    & Q(
                        Q(session_lock__isnull=True)
                        | Q(session_lock__expiry_at__lte=timezone.now())
                    )
                )

                # Exclude rooms that are already booked
                exclude_room_bookings = exist_room_booking(
                    start_date=check_in_datetime,
                    end_date=added_duration_datetime,
                    room_type_name=room_type_name,
                )

                excluded_ids = [
                    room_booking.room_id for room_booking in exclude_room_bookings
                ]

                # Exclude rooms that are already booked
                # Sort by room code so that the can possiblity stay closer
                available_rooms = available_rooms.exclude(
                    room_id__in=excluded_ids
                ).order_by("room_code")

                if available_rooms.count() == 0:
                    current_room_type = RoomType.objects.filter(
                        type_name=room_type_name
                    ).first()
                    if not current_room_type:
                        raise exceptions.UnableDoBooking("Type not found")

                    return Response(
                        "No " + current_room_type.type_name + " room available",
                        status=status.HTTP_409_CONFLICT,
                    )

                if available_rooms.count() < count:
                    current_room_type = RoomType.objects.filter(
                        type_name=room_type_name
                    ).first()
                    if not current_room_type:
                        raise exceptions.UnableDoBooking("Type not found")

                    return Response(
                        f"Only {str(available_rooms.count())} of {current_room_type.type_name} room available.",
                        status=status.HTTP_409_CONFLICT,
                    )

                available_rooms = available_rooms[:count]

                if union is None:
                    union = available_rooms
                else:
                    union = union.union(available_rooms)

                generated_rooms.extend(available_rooms)

            out_serializer = RoomSerializerForExpressBookingRequest(union, many=True)

            return Response(out_serializer.data)

        except exceptions.UnableDoBooking as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_403_FORBIDDEN,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ConsentFormViewSet(viewsets.ViewSet):
    permission_classes = []
    authentication_classes = []
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (MultiPartParser,)

    @swagger_auto_schema(
        responses={
            "200": ConsentFormSerializer,
            "404": "not found",
            "500": "server error",
        }
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-consent-by-booking/(?P<booking_id>[0-9a-f-]+)",
        url_name="get-consent-by-booking",
        permission_classes=[],
    )
    def get_consent_by_booking(self, request, booking_id):
        try:
            try:
                qs = ConsentForm.objects.get(booking_id=booking_id)
            except:
                return Response(
                    {"status": "failed", "message": "not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            room_bookings = RoomBooking.objects.filter(booking_id=booking_id)

            room_no_list = ""
            for room_book in room_bookings:
                room_no_list = f"{room_no_list}{', ' if room_no_list != '' else ''}{room_book.room.room_code}"

            qs.room_no_list = room_no_list

            serializer = ConsentFormSerializer(qs)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "accountId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="account id",
            ),
            openapi.Parameter(
                "process",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="process value : check-in or check-out",
            ),
            openapi.Parameter(
                "bookingId",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="booking id",
            ),
        ]
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-pin",
        url_name="get-pin",
        permission_classes=[],
    )
    def get_session_pin(self, request):
        account_id = request.GET.get("accountId", None)
        process = request.GET.get("process", None)
        booking_id = request.GET.get("bookingId", None)

        try:
            consent_session: ConsentSession = create_session(account_id=account_id)

            if process and booking_id:
                update_session(
                    module="booking",
                    process=process,
                    booking_id=booking_id,
                    pin=consent_session.pin,
                )

            return Response(
                {
                    "status": "success",
                    "pin": str(consent_session.pin),
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=SubmitConsentSessionPinSerializer,
        responses={
            200: GetConsentFormInterfaceSerializer,
            400: "bad request",
            500: "server error",
        },
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-pin",
        url_name="submit-pin",
        permission_classes=[],
    )
    def submit_pin(self, request):
        serializer = SubmitConsentSessionPinSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "msg": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                consent_session = ConsentSession.objects.get(pin=validated_data["pin"])
            except:
                raise exceptions.UnableSubmitPin("please enter a correct pin")
            consent_form = ConsentForm.objects.get(id=consent_session.session_id)
            booking = consent_form.booking

            if not booking.customer_staying:
                return Response(
                    {
                        "status": "failed",
                        "message": "This booking dont have customer assigned. please tell the cashier to assign",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            consent_data = {
                "consent_form_id": consent_form.pk,
                "pin": validated_data["pin"],
                "name": booking.customer_booked,
                "phone_num": (
                    booking.customer_staying.phone_number
                    if booking.customer_staying.phone_number
                    else ""
                ),
                "email": (
                    booking.customer_staying.email
                    if booking.customer_staying.email
                    else ""
                ),
            }

            if consent_form.consent_checkin:
                encoded = base64.b64encode(consent_form.consent_checkin).decode("utf-8")
                consent_data["checkin_signature"] = encoded
            serializer = GetConsentFormInterfaceSerializer(consent_data)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except exceptions.UnableSubmitPin as e:
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=SubmitConsentFormInterfaceSerializer,
        responses={200: "OK", 400: "bad request", 500: "server error"},
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="submit",
        url_name="submit",
        permission_classes=[],
    )
    def submit_consent_form(self, request):
        serializer = SubmitConsentFormInterfaceSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "msg": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            with transaction.atomic():
                booking = upload_consent_form(data=serializer)
            return Response(
                {
                    "status": "success",
                    "message": "consent form submited",
                    "data": {
                        "bookingId": booking.booking_id,
                        "bookingNo": booking.booking_no,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetBookingPaymentInformationView(generics.RetrieveAPIView):
    lookup_field = "booking_id"
    queryset = Booking.objects.all()
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = []
    permission_classes = []

    def retrieve(self, request, *args, **kwargs):
        obj: Booking = self.get_object()

        booking_all_transaction = Transaction.objects.filter(booking_id=obj.booking_id)
        transaction_list = booking_all_transaction.filter(
            transaction_status__in=["Paid", "Pending Payment"]
        )

        t = transaction_list.aggregate(
            debit_sum=Sum("debit_amount"), credit_sum=Sum("credit_amount")
        )

        if transaction_list.exists():
            data = {
                "status": "Pending" if t["credit_sum"] > 0 else "Paid",
                "paid_amount": t["debit_sum"],
                "pending_amount": t["credit_sum"],
                "transaction_datetime": transaction_list[0].transaction_datetime,
                "payment_type": transaction_list[0].payment_type,
            }
        else:
            data = {
                "status": "No Transactions",
                "paid_amount": 0,
                "pending_amount": 0,
                "transaction_datetime": "",
                "payment_type": "",
            }

        overstay_transaction_created = False
        overstay_transaction_paid = False
        # for transaction_data in booking_all_transaction:
        #     remarks = transaction_data.payment_remarks
        #     if not remarks:
        #         continue

        #     if "Overstay" in remarks:
        #         overstay_transaction_created = True

        #         if transaction_data.transaction_status in ("Paid", "Void"):
        #             overstay_transaction_paid = True

        #         break

        for transaction_data in booking_all_transaction:
            for item in transaction_data.items:
                if item["category"] == TransactionItemCategory.OVERSTAY:
                    overstay_transaction_created = True
                    break

            if overstay_transaction_created:
                if transaction_data.transaction_status in ("Paid", "Void"):
                    overstay_transaction_paid = True

        data["overstay_transaction_created"] = overstay_transaction_created
        data["overstay_transaction_paid"] = overstay_transaction_paid

        serializer = BookingPaymentStatusInformationSerializer(data)

        return Response(serializer.data)

    @swagger_auto_schema(
        responses={200: BookingPaymentStatusInformationSerializer},
        operation_description="Get booking payment information by Booking Id, use this to get full information of booking payment ",
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class OtaReservation(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    permission_classes = []
    authentication_classes = []

    @swagger_auto_schema(
        request_body=OtaReservationSerializer,
        responses={
            "200": OtaReservationSummary,
            "400": "bad request",
            "404": "not found",
            "500": "server error",
        },
    )
    @action(
        methods=["POST"],
        detail=False,
        url_path="reserve",
        url_name="reserve",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def reserve_ota(self, request):
        serializer = OtaReservationSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                available_rooms = get_available_rooms(
                    serializer=serializer, user=request.user
                )

                if not available_rooms:
                    return Response(
                        {"status": "failed", "message": "no available room"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                validated_data = serializer.validated_data

                start_datetime = validated_data["check_in_datetime"]
                end_datetime = start_datetime + timedelta(
                    hours=validated_data["duration"]
                )

                created_booking = reserve_ota(
                    rooms=available_rooms,
                    start_date=start_datetime,
                    end_date=end_datetime,
                    lot_id=request.user.lot_id,
                )

                total_rooms = len(available_rooms)

                grouped_data = defaultdict(list)
                for room in available_rooms:
                    room_type_name = room.room_type.type_name
                    grouped_data[room_type_name].append(room)

                grouped_data = dict(grouped_data)

                type_list = []
                for room_type_name, rooms in grouped_data.items():
                    color_tag = rooms[0].room_type.color_tags

                    zone_grouped = defaultdict(list)
                    for room in rooms:
                        zone_name = room.room_type.roomzone.zone_name
                        zone_grouped[zone_name].append(room)

                    zone_list = []
                    for zone_name, rooms_inside_zone in zone_grouped.items():
                        zone_data = {
                            "zone_name": zone_name,
                            "room_list": rooms_inside_zone,
                        }
                        zone_list.append(zone_data)

                    type_list.append(
                        {
                            "type_name": room_type_name,
                            "zones": zone_list,
                            "color_code": color_tag,
                        }
                    )

                summary_data = {
                    "total_rooms": total_rooms,
                    "start_date": start_datetime,
                    "end_date": end_datetime,
                    "types": type_list,
                }

            response_serializer = OtaReservationSummary(summary_data)
            return Response(
                {
                    "status": "success",
                    "message": "room reserved",
                    "data": response_serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class POSBookingFilterViewSet(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    permission_classes = []

    @swagger_auto_schema(
        responses={"200": POSGuestSearchSerializer, "500": "server error"}
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-guest-booking",
        url_name="get-all-guest-booking",
        name="get-all-guest-booking",
    )
    def get_all_guest_booking(self, request):
        try:
            yesterday = timezone.now() - timedelta(days=1)
            today = timezone.now()
            tomorrow = timezone.now() + timedelta(days=1)

            bookings = (
                Booking.objects.prefetch_related("booking_status")
                .filter(
                    # Q(booking_made_datetime__range=(yesterday, tomorrow))
                    Q(
                        booking_status__is_latest=True,
                        booking_status__booking_status__in=[
                            "Confirm Booking",
                            "Check In",
                            "Overstayed",
                        ],
                        booking_status__check_in_datetime__range=(yesterday, tomorrow),
                        booking_status__check_out_datetime__range=(today, tomorrow),
                    )
                )
                .prefetch_related("customer_staying")
            )

            data = []
            for booking in bookings:
                room_bookings = booking.get_all_booking_room().exclude(
                    status="Reservation"
                )
                temp_array = []
                for room_booking in room_bookings:

                    if not room_booking.person_in_charge:
                        continue

                    person_in_charge_id = room_booking.person_in_charge.pk
                    person_in_charge_name = room_booking.person_in_charge

                    if not person_in_charge_exists(
                        temp_array, person_in_charge_id, person_in_charge_name
                    ):
                        temp_array.append(
                            {
                                "person_in_charge": {
                                    "person_in_charge_id": person_in_charge_id,
                                    "person_in_charge_name": person_in_charge_name,
                                    "is_member": room_booking.person_in_charge.member.member_tier
                                    != "None",
                                },
                                "booking": booking,
                                "room_no": room_booking.room.room_code,
                                "room_type": room_booking.room.room_type.type_name,
                            }
                        )

                data = data + temp_array

            serializer = POSGuestSearchSerializer(data, many=True)

            return Response(
                {
                    "status": "success",
                    "message": "guest lists sent",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ScanCardAPIViewByBooking(APIView):
    authentication_classes = []
    permission_classes = []
    # NOTE: Use GET request for testing for now,

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "booking_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="booking id",
                required=True,
            )
        ]
    )
    def get(self, request):
        booking_id = self.request.GET.get("booking_id")
        booking = Booking.objects.get(booking_id=booking_id)
        room_booking = booking.room_bookings.first()
        # message = card_integration.make_card_format(room_booking, "JASON PRO MAX", "*************")
        return Response(
            {"message": room_booking.room.room_code}, status=status.HTTP_200_OK
        )


class ScanCardAPIViewByRoomBooking(APIView):
    authentication_classes = []
    permission_classes = []
    # NOTE: Use GET request for testing for now,

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "booking_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="room_booking id",
                required=True,
            )
        ]
    )
    def get(self, request):
        room_booking_id = self.request.GET.get("room_booking_id")
        room_booking = RoomBooking.objects.get(pk=room_booking_id)
        # message = card_integration.make_card_format(room_booking, "JASON PRO MAX", "*************")
        return Response(
            {"message": room_booking.room.room_code}, status=status.HTTP_200_OK
        )


class FeedbackViewset(viewsets.ViewSet):
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = FeedBack.objects.all()
        return qs

    def list(self, request):

        rating = request.GET.get("rating", None)
        date_filter = request.GET.get("dateFilter", None)
        period = request.GET.get("period", "daily")

        try:
            qs = self.get_queryset()

            qs = qs.filter(Q(lot_id=request.user.lot_id) | Q(lot_id__isnull=True))

            if date_filter:
                date_filter = datetime.fromtimestamp(int(date_filter))
                date_filter = timezone.make_aware(
                    date_filter, timezone.get_current_timezone()
                )
                date_filter = date_filter.replace(hour=0, minute=0, second=0)

                if period == "daily":
                    start_date = date_filter
                    end_date = date_filter.replace(hour=23, minute=59, second=59)

                if period == "weekly":
                    start_date = date_filter - timedelta(days=7)
                    end_date = date_filter.replace(hour=23, minute=59, second=59)

                if period == "monthly":
                    start_date = date_filter - timedelta(days=30)
                    end_date = date_filter.replace(hour=23, minute=59, second=59)

                qs = qs.filter(created_at__range=(start_date, end_date))

            if rating:
                qs = qs.filter(rating=rating)

            serializer = FeedbackSerializer(qs, many=True)

            return Response({"status": "success", "data": serializer.data})

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["GET"], url_path="graph", url_name="graph")
    def get_feedback_graph(self, request):

        period = request.GET.get("period", "daily")
        date_filter = request.GET.get("dateFilter", None)
        # rating = request.GET.get("rating", None)

        try:
            qs = self.get_queryset()

            qs = qs.filter(Q(lot_id=request.user.lot_id) | Q(lot_id__isnull=True))

            # if rating:
            #     qs = qs.filter(rating=rating)

            if date_filter:
                date_filter = datetime.fromtimestamp(int(date_filter))
                date_filter = timezone.make_aware(
                    date_filter, timezone.get_current_timezone()
                )
                date_filter = date_filter.replace(hour=23, minute=59, second=59)
            else:
                date_filter = timezone.now().replace(hour=23, minute=59, second=59)

            if period == "daily":
                start_date = date_filter - timedelta(days=5)

                qs = qs.filter(created_at__range=(start_date, date_filter))

                grouped_by_date = (
                    qs.annotate(date=TruncDate("created_at"))
                    .values("date")
                    .annotate(
                        feedback_count=Count("id"),
                        total_rating=Cast(
                            Sum(Cast("rating", IntegerField())), IntegerField()
                        ),
                    )
                    .order_by("date")
                )

                start_graph_date = (
                    grouped_by_date.first()["date"] if grouped_by_date else None
                )
                end_graph_date = (
                    grouped_by_date.last()["date"] if grouped_by_date else None
                )

                chart_data = []
                for item in grouped_by_date:
                    chart_data.append(
                        {
                            "date": item["date"],
                            "average_rate": round(
                                item["total_rating"] / item["feedback_count"], 1
                            ),
                        }
                    )

            if period == "weekly":
                start_date = date_filter - timedelta(weeks=5)

                grouped_by_week = (
                    qs.filter(created_at__range=(start_date, date_filter))
                    .annotate(week=TruncWeek("created_at"))
                    .values("week")
                    .annotate(
                        feedback_count=Count("id"),
                        total_rating=Cast(
                            Sum(Cast("rating", IntegerField())), IntegerField()
                        ),
                    )
                    .order_by("week")
                )

                week_number = int(date_filter.strftime("%U"))
                year = int(date_filter.strftime("%Y"))

                start_graph_date = (
                    date_filter - timedelta(days=date_filter.weekday())
                ).strftime("%Y-%m-%d")
                end_graph_date = (
                    date_filter + timedelta(days=6 - date_filter.weekday())
                ).strftime("%Y-%m-%d")

                chart_data = [
                    {
                        "date": f"Week {item['week'].isocalendar()[1]} {item['week'].year}",
                        "average_rate": round(
                            item["total_rating"] / item["feedback_count"], 1
                        ),
                    }
                    for item in grouped_by_week
                ]

            if period == "monthly":
                start_date = date_filter - relativedelta(months=5)
                grouped_by_month = (
                    qs.filter(created_at__range=(start_date, date_filter))
                    .annotate(month=TruncMonth("created_at"))
                    .values("month")
                    .annotate(
                        feedback_count=Count("id"),
                        total_rating=Cast(
                            Sum(Cast("rating", IntegerField())), IntegerField()
                        ),
                    )
                    .order_by("month")
                )

                month = date_filter.month
                year = date_filter.year

                start_graph_date = datetime(year, month, 1).strftime("%Y-%m-%d")
                end_graph_date = (
                    datetime(year, month, 1) + relativedelta(day=31)
                ).strftime("%Y-%m-%d")

                chart_data = [
                    {
                        "date": f"{calendar.month_name[item['month'].month]} {item['month'].year}",
                        "average_rate": round(
                            item["total_rating"] / item["feedback_count"], 1
                        ),
                    }
                    for item in grouped_by_month
                ]

            empty_feedback_count = 0
            filled_feedback_count = 0
            for item in qs:
                if item.feedback == "":
                    empty_feedback_count += 1
                else:
                    filled_feedback_count += 1

            return_serializer = FeedbackGraphSerializer(
                {
                    "empty_feedback": empty_feedback_count,
                    "filled_feedback": filled_feedback_count,
                    "chart_data": chart_data,
                    "start_date": start_graph_date,
                    "end_date": end_graph_date,
                }
            )

            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            traceback.print_exc()
            return Response(
                {
                    "status": "failed",
                    "message": "server error",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
