from datetime import datetime
import uuid
import random

from bookings.serializer import SubmitConsentFormInterfaceSerializer


from bookings.models import ConsentSession, ConsentForm
from bookings import exceptions


def create_session(account_id : None) -> str:
    
    pin = generate_pin()
    consent_session = ConsentSession.objects.create(
        pin = pin,
        last_updated = datetime.now(),
        cashier_id = account_id
    )

    return consent_session


def upload_consent_form(data : SubmitConsentFormInterfaceSerializer):
    validated_data = data.validated_data

    try:
        session = ConsentSession.objects.get(pin = validated_data['pin'])
    except:
        raise exceptions.UnableSubmitConsent("pin is expired. please generate new pin")


    consent_form = ConsentForm.objects.get(id = validated_data['consent_form_id'])

    if consent_form.consent_checkin:
        try:
            checkout_signature = validated_data['checkout_signature']
        except:
            raise exceptions.UnableSubmitConsent('please sign the checkout signature field')
        
        update_session(
            module="consent",
            process="check-out",
            pin = validated_data['pin'],
            consent_file=checkout_signature
        )
        session.delete()
        
        return consent_form.booking
    
    consent_form.booking.customer_staying.phone_number = validated_data['phone_num']
    consent_form.booking.customer_staying.email = validated_data['email']
    consent_form.booking.customer_staying.save()
    update_session(
        module="consent",
        process="check-in",
        pin = validated_data['pin'],
        consent_file=validated_data['checkin_signature']
    )
    session.delete()

    return consent_form.booking



def update_session(module, pin, process = None, consent_file = None, booking_id = None):

    try:
        consent_session = ConsentSession.objects.get(pin = pin)
    except:
        raise exceptions.UnableSubmitConsent('session not found')
    
    
    if module == "booking":
        if not booking_id:
            raise exceptions.UnableSubmitConsent('must provide booking id')
        
        existed_consent_form = ConsentForm.objects.filter(booking_id = booking_id).first()

        if existed_consent_form:
            consent_session.session_id = existed_consent_form.pk
            consent_session.save()
        else:
            # create ConsentForm and the pk is from session_id
            new_consent_form = ConsentForm.objects.create(
                id = consent_session.session_id,
                booking_id = booking_id,
                created_at = datetime.now()
            )


    if module == "consent":
        if not consent_file:
            raise exceptions.UnableSubmitConsent('consent form file is required')
        
        # add consent form file data into ConsentSession.
        binary_consent_file = consent_file.read()
        consent_session.consent_form = binary_consent_file
        consent_session.save()



    try:
        consent_form = ConsentForm.objects.get(pk = consent_session.session_id)
    except:
        consent_form = None

    if consent_session.consent_form and consent_form:
        # add consent form data into ConsentForm
        if process == "check-in":
            if consent_form.consent_checkin:
                raise exceptions.UnableSubmitConsent('Check in consent form already exist')
            consent_form.consent_checkin = consent_session.consent_form
        
        if process == "check-out":
            if consent_form.consent_checkout:
                raise exceptions.UnableSubmitConsent('Check out consent form already exist')
            consent_form.consent_checkout = consent_session.consent_form
        
        consent_form.save()

def generate_pin():
    """
    Generate pin for consent session
    """
    pin_able_to_use = False
    pin = ""
    while not pin_able_to_use:
        pin = random.randint(100000, 999999)

        is_pin_exist = ConsentSession.objects.filter(pin = str(pin))

        if not is_pin_exist:
            pin_able_to_use = True
        
        if pin == "":
            continue

    return pin