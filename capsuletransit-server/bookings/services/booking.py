# from requests import Response
import json
import math
from datetime import date, datetime, timedelta
from typing import List

from bookings import exceptions
from bookings.models import (
    Booking,
    BookingNumberCounter,
    BookingSessionRoomLock,
    BookingStatus,
    ConsentForm,
    Customer,
    LockerBooking,
    MerchBooking,
    Platform,
    RoomBooking,
    TransferBooking,
    ShowerBooking,
)
from bookings.serializer import (
    AddBookingRemarksSerializer,
    BookingAddRoomSerializer,
    BookingRoomTransferUpgradeSerializer,
    BookingSerializer,
    ExpressBookingSerializer,
    OtaReservationSerializer,
    PlatformSerializer,
    POSGuestSearchSerializer,
    RegisterBookingWithoutPicSerializer,
    RoomBookingSerializer,
    TransferBookingSerializer,
    UpdateBookingStatusInfoSerializer,
)
from bookings.utils import (
    calculate_max_checkout_datetime,
    calculate_rate_sum,
    calculate_total_booking_sum,
    generate_booking_no,
)
from constant.enums import TransactionItemCategory

# django modules
from django.contrib.postgres.expressions import ArraySubquery
from django.db import transaction
from django.db.models import (
    <PERSON>,
    Char<PERSON>ield,
    ExpressionWrapper,
    F,
    OuterRef,
    Prefetch,
    Q,
    Subquery,
    Value,
    When,
)
from django.db.models.functions import Concat, JSONObject
from django.utils import timezone
from django.utils.timezone import datetime, make_aware
from guests.utils import create_non_guest_customer
from lot.models import AirportCode, Lot
from lot.utils import (
    get_lot_settings_by_lot_with_serializer,
    get_lot_settings_by_lot_id,
)
from rooms.models import ROOMSTATUS, PlatformTier, Room, RoomRate
from rooms.services.serializers import RoomSerializer
from transaction.models import Transaction
from transaction.services.transaction import (
    create_modified_transaction,
    create_new_pending_transaction,
)
from transaction.utils import calculate_amount_percentage, generate_invoice_no

from .availability import annotate_available_point
from .consentform import update_session
from accounts.models import Shift
from transaction import exceptions as transactionExceptions

# python modules
# from threading import Thread


def get_all_upcoming_bookings_list(request):
    bookings_list = {}

    bookings_list["room_bookings_list"] = get_all_room_bookings_list(request=request)
    return bookings_list


def get_all_room_bookings_list(request):
    room_status_filters = request.query_params.getlist("room_status_filters[]")
    room_type_filters = request.query_params.getlist("room_type_filters[]")
    room_zone_filters = request.query_params.getlist("room_zone_filters[]")
    platform_filters = request.query_params.getlist("platform_filters[]")
    duration = int(request.query_params.get("duration"))
    ota_directed = request.query_params.getlist("ota_directed")
    first_sort_by = (
        "room_code"
        if request.query_params.get("sort_by") == "room_code"
        else "first_availability"
    )
    second_sort_by = (
        "first_availability"
        if request.query_params.get("sort_by") == "availability"
        else "total_availability"
    )
    third_sort_by = (
        "total_availability"
        if request.query_params.get("sort_by") == "availability"
        else "room_code"
    )
    is_ascend = (
        True if request.query_params.get("is_ascend").lower() == "true" else False
    )

    bed_type = request.query_params.get("bed_type")
    bed_type_boolean = True if bed_type == "top" else False

    quiet_zone = request.query_params.get("quiet_zone")

    year = int(request.query_params.get("year"))
    month = int(request.query_params.get("month"))
    day = int(request.query_params.get("day"))
    hour = int(request.query_params.get("hour"))
    if ota_directed:
        utc_date_start = make_aware(
            datetime(year=year, month=month, day=day, hour=hour),
            timezone=timezone.utc,
        )
        utc_date_end = make_aware(
            datetime(year=year, month=month, day=day, hour=hour) + timedelta(days=5),
            timezone=timezone.utc,
        )
    else:
        utc_date_start = make_aware(
            datetime(year=year, month=month, day=day, hour=hour) + timedelta(days=-1),
            timezone=timezone.utc,
        )
        utc_date_end = make_aware(
            datetime(year=year, month=month, day=day, hour=hour) + timedelta(days=2),
            timezone=timezone.utc,
        )

    actual_checkin_and_out_date_time_are_null = Q(
        actual_checkin_date_time__isnull=True
    ) & Q(actual_checkout_date_time__isnull=True)

    only_actual_checkout_date_time_is_null = Q(
        actual_checkin_date_time__isnull=False
    ) & Q(actual_checkout_date_time__isnull=True)

    actual_checkin_and_out_date_time_are_not_null = Q(
        actual_checkin_date_time__isnull=False
    ) & Q(actual_checkout_date_time__isnull=False)

    # get the rooms that are in the room_bookings queryset
    rooms = Room.objects.all()

    if bed_type != "all":
        rooms = rooms.filter(is_upper=bed_type_boolean)
    rooms = (
        rooms.filter(
            Q(room_type__roomzone__lot_id_id=request.user.lot_id),
            Q(status__in=room_status_filters),
            Q(room_type__type_name__in=room_type_filters),
            Q(room_type__roomzone__zone_name__in=room_zone_filters),
            Q(session_lock__isnull=True)
            | Q(session_lock__expiry_at__lte=timezone.now()),
        )
        .annotate(
            room_type_details=F("room_type__RoomTypeDetails"),
            room_type_name=F("room_type__type_name"),
            roomzone=F("room_type__roomzone__zone_name"),
            color_code=F("room_type__color_tags"),
            max_pax=F("room_type__max_pax"),
            booking_periods=ArraySubquery(
                RoomBooking.objects.filter(
                    (
                        actual_checkin_and_out_date_time_are_null
                        & (
                            (
                                Q(
                                    booking__booking_status__check_in_datetime__gte=utc_date_start
                                )
                                & Q(
                                    booking__booking_status__max_check_out_datetime__lte=utc_date_end
                                )
                            )
                            | (
                                Q(
                                    booking__booking_status__check_in_datetime__lte=utc_date_start
                                )
                                & Q(
                                    booking__booking_status__max_check_out_datetime__gte=utc_date_start
                                )
                            )
                            | (
                                Q(
                                    booking__booking_status__check_in_datetime__lte=utc_date_end
                                )
                                & Q(
                                    booking__booking_status__max_check_out_datetime__gte=utc_date_end
                                )
                            )
                        )
                    )
                    | (
                        only_actual_checkout_date_time_is_null
                        & (
                            (
                                Q(actual_checkin_date_time__gte=utc_date_start)
                                & Q(
                                    booking__booking_status__max_check_out_datetime__lte=utc_date_end
                                )
                            )
                            | (
                                Q(actual_checkin_date_time__lte=utc_date_start)
                                & Q(
                                    booking__booking_status__max_check_out_datetime__gte=utc_date_start
                                )
                            )
                            | (
                                Q(actual_checkin_date_time__lte=utc_date_end)
                                & Q(
                                    booking__booking_status__max_check_out_datetime__gte=utc_date_end
                                )
                            )
                        )
                    )
                    | (
                        actual_checkin_and_out_date_time_are_not_null
                        & (
                            (
                                Q(actual_checkin_date_time__gte=utc_date_start)
                                & Q(actual_checkout_date_time__lte=utc_date_end)
                            )
                            | (
                                Q(actual_checkin_date_time__lte=utc_date_start)
                                & Q(actual_checkout_date_time__gte=utc_date_start)
                            )
                            | (
                                Q(actual_checkin_date_time__lte=utc_date_end)
                                & Q(actual_checkout_date_time__gte=utc_date_end)
                            )
                        )
                    ),
                    room=OuterRef("room_id"),
                    booking__booking_status__is_latest=True,
                    booking__platform__platform__in=platform_filters,
                )
                .annotate(
                    check_in_datetime=Case(
                        When(
                            Q(actual_checkin_date_time__isnull=False),
                            then=F("actual_checkin_date_time"),
                        ),
                        When(
                            Q(actual_checkin_date_time__isnull=True),
                            then=F("booking__booking_status__check_in_datetime"),
                        ),
                    ),
                    check_out_datetime=Case(
                        When(
                            Q(actual_checkin_date_time__isnull=False)
                            & Q(actual_checkout_date_time__isnull=False),
                            then=F("actual_checkout_date_time"),
                        ),
                        When(
                            Q(actual_checkout_date_time__isnull=True),
                            then=F("booking__booking_status__check_out_datetime"),
                        ),
                    ),
                    max_check_out_datetime=F(
                        "booking__booking_status__max_check_out_datetime"
                    ),
                    customer_firstname=F("booking__customer_staying__firstname"),
                    customer_lastname=F("booking__customer_staying__lastname"),
                    current_booking_status=F("booking__booking_status__booking_status"),
                    booking_no=F("booking__booking_no"),
                    booking_platform=F("booking__platform__platform"),
                )
                .exclude(
                    Q(booking__booking_status__booking_status="Cancelled")
                    | Q(
                        booking__booking_status__booking_status__icontains="Transfer to"
                    )
                )
                .values(
                    json=JSONObject(
                        room_booking_id="room_booking_id",
                        check_in_datetime="check_in_datetime",
                        check_out_datetime="check_out_datetime",
                        max_check_out_datetime="max_check_out_datetime",
                        customer_firstname="customer_firstname",
                        customer_lastname="customer_lastname",
                        current_booking_status="current_booking_status",
                        booking_no="booking_no",
                        booking_platform="booking_platform",
                        booking_id="booking",
                    ),
                )
            ),
        )
        .distinct("room_code")
    )

    if quiet_zone == "True":
        rooms = rooms.filter(quiet_zone=True)
    if quiet_zone == "False":
        rooms = rooms.filter(quiet_zone=False)

    rooms_serializer = RoomSerializer(
        rooms,
        many=True,
        context={
            "utc_date_start": utc_date_start,
            "utc_date_end": utc_date_end,
            "duration": duration,
            "queryset": rooms,
        },
    )

    num_of_rooms = len(rooms_serializer.data)

    # *multi-threading if needed
    # thread_1 = Thread(
    #     target=get_available_periods,
    #     args=(
    #         0,
    #         int(num_of_rooms / 2),
    #         rooms_serializer.data,
    #         utc_date_start, utc_date_end,
    #         duration
    #     )
    # )
    # thread_2 = Thread(
    #     target=get_available_periods,
    #     args=(
    #         int(num_of_rooms / 2),
    #         num_of_rooms,
    #         rooms_serializer.data,
    #         utc_date_start, utc_date_end,
    #         duration
    #     )
    # )

    # thread_1.start()
    # thread_2.start()

    # thread_1.join()
    # thread_2.join()

    # get_available_periods(
    #     0, num_of_rooms, rooms_serializer.data, utc_date_start, utc_date_end, duration
    # )

    annotate_available_point(rooms_serializer.data)
    if first_sort_by == "first_availability":
        sorted_rooms_serializer = sorted(
            rooms_serializer.data, key=lambda key: key["available_point"], reverse=True
        )
    if first_sort_by == "room_code":
        sorted_rooms_serializer = rooms_serializer.data

    if not is_ascend:
        sorted_rooms_serializer = sorted_rooms_serializer[::-1]

    return sorted_rooms_serializer


def get_room_booking_by_id(room_booking_id):
    room_booking = RoomBooking.objects.get(pk=room_booking_id)
    room_booking_serializer = RoomBookingSerializer(room_booking)
    return room_booking_serializer.data


def update_booking_remarks_by_id(data: AddBookingRemarksSerializer):
    validated_data = data.validated_data

    booking_id = validated_data["booking_id"]
    new_remarks = validated_data["new_remarks"]
    new_remarks = validated_data["new_remarks"]

    try:
        booking = Booking.objects.get(pk=booking_id)
    except Booking.DoesNotExist:
        return {"status": "failed", "msg": "Booking not found"}, 404

    updated_data = {"details": new_remarks}

    booking_serializer = BookingSerializer(booking, data=updated_data, partial=True)

    if booking_serializer.is_valid():
        booking_serializer.save()
        return {"status": "success", "msg": "Booking remarks updated"}, 200
    else:
        return {
            "status": "failed",
            "msg": "Save remarks error",
            "errors": booking_serializer.errors,
        }, 400


def get_available_upgrade(room_type, now, end_time, room_id):
    bookings = BookingStatus.objects.filter(
        Q(check_in_datetime__lte=now, check_out_datetime__gte=now)
        & Q(check_in_datetime__lte=end_time, check_out_datetime__gte=end_time)
    )
    for booking_status in bookings:
        room = (
            RoomBooking.objects.filter(
                booking__booking_id=booking_status.booking.booking_id,
                room__room_type__type_id=room_type,
                room__status="vacant, cleaned",
            )
            .exclude(room__room_id=room_id)
            .first()
        )
        room = (
            RoomBooking.objects.filter(
                booking__booking_id=booking_status.booking.booking_id,
                room__room_type__type_id=room_type,
                room__status="vacant, cleaned",
            )
            .exclude(room__room_id=room_id)
            .first()
        )
        if room:
            return room

    return None


def get_all_platforms_list():
    platforms = Platform.objects.filter(is_archive=False)
    platforms_serializer = PlatformSerializer(platforms, many=True)
    return platforms_serializer.data


def get_today_room_checkin():
    today = date.today()
    start_of_day = timezone.make_aware(
        timezone.datetime(today.year, today.month, today.day, 0, 0, 0)
    )
    test = BookingStatus.objects.last().check_in_datetime
    # print(start_of_day, "start_of_day")
    test = BookingStatus.objects.last().check_in_datetime
    # print(test)
    # print(start_of_day == test)

    bookings = Booking.objects.select_related("platform", "customer_staying").all()
    bookings = Booking.objects.select_related("platform", "customer_staying").all()
    joined_data = []
    # print(bookings)
    for booking in bookings:
        booking_statuses = BookingStatus.objects.filter(
            Q(booking_status="Confirm Booking")
            | Q(booking_status="Booked")
            | Q(booking_status="Check In")
            | Q(booking_status="Overstayed"),
        )
        # print(booking_statuses, "booking_statuses")
        for booking_status in booking_statuses:
            booking_data = {
                "Booking ID": booking.booking_id,
                "Booking Number": booking.booking_no,
                "Customer": (
                    booking.customer_staying.firstname
                    if booking.customer_staying
                    else booking.customer_booked
                ),
                "Platform": booking.platform.platform,
                "Status": booking_status.booking_status if booking_status else None,
                "Check In Time": (
                    booking_status.check_in_datetime if booking_status else None
                ),
                "Check Out Time": (
                    booking_status.check_out_datetime if booking_status else None
                ),
                "Room Bookings": [],
            }

            for room_booking in booking.room_bookings.all():
                room_booking_data = {
                    "Room ID": room_booking.room.room_id,
                    "Person In Charge": (
                        room_booking.person_in_charge.firstname
                        if room_booking.person_in_charge
                        else None
                    ),
                    "Actual Checkin Datetime": room_booking.actual_checkin_date_time,
                    "Actual Checkout Datetime": room_booking.actual_checkout_date_time,
                    "Price Paid Sum": room_booking.sum,
                    "Room Booking Status": room_booking.status,
                }
                booking_data["Room Bookings"].append(room_booking_data)

            joined_data.append(booking_data)
            # print(joined_data, "joined_data")
        return joined_data


def get_guest_booking_by_id(guest_id):
    booking = Booking.objects.get(pk=guest_id)
    # print("Booking Fields:")
    # for field in booking._meta.fields:
    #     print(f"{field.name}: {getattr(booking, field.name)}")

    booking_statuses = BookingStatus.objects.filter(booking=booking)
    # print("\nBookingStatus Fields:")
    # for booking_status in booking_statuses:
    #     for field in booking_status._meta.fields:
    #         print(f"{field.name}: {getattr(booking_status, field.name)}")

    joined_data = []

    for booking_status in booking_statuses:
        booking_data = {
            "bookingId": booking.booking_id,
            "bookingNo": booking.booking_no,
            "customer": (
                booking.customer_staying.firstname
                if booking.customer_staying
                else booking.customer_booked
            ),
            "platform": booking.platform.platform,
            "status": booking_status.booking_status if booking_status else None,
            "checkin": booking_status.check_in_datetime if booking_status else None,
            "checkout": booking_status.check_out_datetime if booking_status else None,
            "sum": booking.sum,
            "roomBookings": [],
            # 'lockerBookings': [],
        }

        for room_booking in booking.room_bookings.all():
            room_booking_data = {
                "roomId": room_booking.room.room_id,
                "pic": (
                    room_booking.person_in_charge.firstname
                    if room_booking.person_in_charge
                    else None
                ),
                "actualCheckin": room_booking.actual_checkin_date_time,
                "actualCheckout": room_booking.actual_checkout_date_time,
                "pps": room_booking.sum,
                "roomBookingStatus": room_booking.status,
            }
            booking_data["roomBookings"].append(room_booking_data)
        joined_data.append(booking_data)
    return joined_data


def get_guest_room_booking_info(guest_id):
    booking_status = BookingStatus.objects.filter(booking__booking_id=guest_id).first()
    booking_status = BookingStatus.objects.filter(booking__booking_id=guest_id).first()

    room_booking = RoomBooking.objects.filter(booking_id=guest_id).first()
    room_booking = RoomBooking.objects.filter(booking_id=guest_id).first()

    booking_data = {
        "booking_id": booking_status.booking_id,
        "status": booking_status.booking_status if booking_status else None,
        "checkin_datetime": (
            booking_status.check_in_datetime if booking_status else None
        ),
        "checkout_datetime": (
            booking_status.check_out_datetime if booking_status else None
        ),
        "checkin_datetime": (
            booking_status.check_in_datetime if booking_status else None
        ),
        "checkout_datetime": (
            booking_status.check_out_datetime if booking_status else None
        ),
        "room_id": room_booking.room.room_id,
        "room_type": room_booking.room.room_type.type_name,
        "room_type": room_booking.room.room_type.type_name,
    }

    return booking_data


def upgrade_room_calculation(
    room_booking_id, booking_id, current_room_id, selected_room_id, user
):
    """
    Function to calculate the sum of the room booking and booking after room upgrade
    """
    booking_status = BookingStatus.objects.get(is_latest=True, booking_id=booking_id)

    current_room = Room.objects.get(pk=current_room_id)
    selected_room = Room.objects.get(pk=selected_room_id)

    current_room_rates = RoomRate.objects.filter(
        room_type_id=current_room.room_type_id, is_latest=True
    )
    current_room_rate = calculate_rate_sum(
        start_date=booking_status.check_in_datetime,
        end_date=booking_status.check_out_datetime,
        room_rate=current_room_rates,
    )

    selected_room_rates = RoomRate.objects.filter(
        room_type_id=selected_room.room_type_id, is_latest=True
    )
    selected_room_rate = calculate_rate_sum(
        start_date=booking_status.check_in_datetime,
        end_date=booking_status.check_out_datetime,
        room_rate=selected_room_rates,
    )

    room_booking = RoomBooking.objects.get(pk=room_booking_id)
    room_booking.sum = selected_room_rate
    room_booking.save()

    booking = Booking.objects.get(pk=booking_id)
    booking.sum -= current_room_rate
    booking.sum += selected_room_rate
    booking.save()

    pending_transaction = Transaction.objects.filter(
        booking_id=booking.pk, transaction_status="Pending Payment"
    ).exists()

    if booking_status.booking_status == "Booked" and not pending_transaction:
        transactions = Transaction.objects.filter(booking_id=booking_id, is_latest=True)

        if transactions.count() > 0:
            trans = transactions.first()

            prev_sum = trans.sum
            prev_service_charge = trans.service_charge_amount
            prev_tax = trans.tax_amount

            new_sum = booking.sum

            if prev_service_charge != 0:
                service_charge_percentage = (prev_service_charge / prev_sum) * 100
                prev_service_charge = calculate_amount_percentage(
                    price=new_sum, percentage=service_charge_percentage
                )
                prev_service_charge = calculate_amount_percentage(
                    price=new_sum, percentage=service_charge_percentage
                )
            if prev_tax != 0:
                tax_percentage = (prev_tax / prev_sum) * 100
                prev_tax = calculate_amount_percentage(
                    price=new_sum, percentage=tax_percentage
                )
                prev_tax = calculate_amount_percentage(
                    price=new_sum, percentage=tax_percentage
                )
            trans.tax_amount = prev_tax
            trans.service_charge_amount = prev_service_charge
            trans.save()

    if booking_status.booking_status == "Confirm Booking" or pending_transaction:
        transactions = Transaction.objects.filter(booking_id=booking_id, is_latest=True)

        start_date = booking_status.check_in_datetime
        end_date = booking_status.check_out_datetime
        time_differenece = end_date - start_date
        hours_difference = time_differenece.total_seconds() / 3600

        if transactions.count() > 0:
            add_ons = selected_room_rate - current_room_rate
            details_json = [
                {
                    "category": "add ons",
                    "item_id": str(selected_room.pk),
                    "item_name": selected_room.room_code,
                    "item_type": selected_room.room_type.type_name,
                    "duration": int(hours_difference),
                    "quantity": str(1),
                    "price": str(add_ons),
                    "category": TransactionItemCategory.ROOM_UPGRADE,
                }
            ]

            create_new_pending_transaction(
                booking_id=booking_id,
                transaction_details=details_json,
                account_id=user.account_id,
                sum=add_ons,
                is_room_booking=True,
            )


def upgrade_current_room(room_booking_id, new_room_id, user):
    """
    Upgrade the current room to a new room
    """
    # Update the status of the old room to "dirty"
    with transaction.atomic():
        rmb = RoomBooking.objects.select_related("room").get(
            room_booking_id=room_booking_id
        )

        booking = rmb.booking

        latest_booking_status = BookingStatus.objects.get(
            booking_id=booking.pk, is_latest=True
        )

        from_date = latest_booking_status.check_in_datetime
        to_date = latest_booking_status.max_check_out_datetime

        is_room_booking_exist = exist_room_booking(
            room_ids=[new_room_id], start_date=from_date, end_date=to_date
        )

        if len(is_room_booking_exist) > 0:
            raise exceptions.UnableUpgradeRoom(
                "room already occupied. please upgrade to another room"
            )

        current_room = rmb.room
        current_room.status = ROOMSTATUS.VACANT_DIRTY
        current_room.save()

        locker_bookings = LockerBooking.objects.filter(
            booking_id=booking.pk, room_code=current_room.room_code
        )

        rmb.room_id = new_room_id
        rmb.save()

        locker_bookings.update(room_code=rmb.room.room_code)

        upgrade_room_calculation(
            room_booking_id=room_booking_id,
            booking_id=booking.pk,
            current_room_id=current_room.pk,
            selected_room_id=new_room_id,
            user=user,
        )

    # Update the room_id in the booking record to the new room
    return True


def register_booking(data: dict, lot_id):
    """
    Register a new booking
    """
    """
    data = {
        "checkInDatetime": "2023-09-21T14:30:00Z",
        "checkOutDatetime": "2023-09-21T20:30:00Z",
        "adult": 4,
        "child": 3,
        "platformId": "96ffdd98-f6ed-462a-9075-f582db3acb55",
        "otaCode": "",
        "details": "near window",
        "rooms": [
            {
                "picId": "0a0c18b0-9da9-4b8a-b093-dc94ad82add5",
                "roomId": "7a088a81-1f69-a3cd-291d-966bf6042302",
                "details": ""
            },
            {
                "picId": "0a0c18b0-9da9-4b8a-b093-dc94ad82add5",
                "roomId": "7864d170-b36a-6e09-d9f6-98b2f7e2b153",
                "details": ""
            },
            {
                "picId": "0a3f53a3-cb2f-4d31-abcb-5868a56609e5",
                "roomId": "88a3e304-476d-0a4c-448d-3b5ed96bf230",
                "details": ""
            }
        ]
    }
    """
    with transaction.atomic():

        accounts_lot = Lot.objects.get(pk=lot_id)

        airport = accounts_lot.airport_code_id
        airport_code = airport.airport_code

        with BookingNumberCounter.Context(airport_code) as counter:
            platform: Platform = Platform.objects.get(pk=data["platformId"])

            if data["rooms"][0]["picId"] == "":
                raise exceptions.BookingNotFound("please asign all pic")

            if data["adult"] < 1:
                raise exceptions.AdultPax("Adult pax should be more than 1")

            main_guest: Customer = Customer.objects.get(pk=data["rooms"][0]["picId"])

            latest_booking_datetime = (
                Booking.objects.filter(room_bookings__room_id=OuterRef("room_id"))
                .order_by("-booking_made_datetime")
                .values("booking_made_datetime")[:1]
            )

            latest_status = (
                BookingStatus.objects.filter(
                    booking_id=OuterRef("booking_id"), is_latest=True
                )
                .order_by("-status_datetime")
                .values(
                    "check_in_datetime", "check_out_datetime", "max_check_out_datetime"
                )[:1]
            )

            room_id_list = [item["roomId"] for item in data["rooms"]]

            str_checkin_date = datetime.strptime(
                data["checkInDatetime"], "%Y-%m-%dT%H:%M:%SZ"
            )
            str_checkout_date = datetime.strptime(
                data["checkOutDatetime"], "%Y-%m-%dT%H:%M:%SZ"
            )

            lot_settings = get_lot_settings_by_lot_with_serializer(lot_id)

            grace_period = 0
            for setting in lot_settings:
                if (
                    setting["settings_category"] == "Room Settings"
                    and setting["settings_name"] == "Grace Period"
                ):
                    grace_period = int(setting["settings_description"])

            max_checkout_datetime = calculate_max_checkout_datetime(
                check_out_datetime=str_checkout_date, grace_period_mins=grace_period
            )

            is_room_booking_exist = exist_room_booking(
                room_ids=room_id_list,
                start_date=str_checkin_date,
                end_date=max_checkout_datetime,
            )

            filtered_booking_exist = []
            for room_book in is_room_booking_exist:
                if room_book.status == "Reservation" and platform.can_late_payment:
                    continue

                filtered_booking_exist.append(room_book)

            if len(filtered_booking_exist) > 0:
                raise exceptions.RoomBooked(
                    f"Room {is_room_booking_exist[0].room.room_code} already booked"
                )

            main_room = (
                Room.objects.filter(pk=data["rooms"][0]["roomId"])
                .annotate(
                    room_airport=F(
                        "room_type_id__roomzone_id__lot_id_id__airport_code_id_id__airport_code"
                    )
                )
                .first()
            )

            time_differenece = str_checkout_date - str_checkin_date
            booking_duration = time_differenece.total_seconds() / 3600

            rooms_obj = Room.objects.filter(room_id__in=room_id_list)
            room_rates = []
            max_pax_total = 0
            for room in rooms_obj:
                max_pax_total += int(room.room_type.max_pax)
                related_rate = room.related_room_rate(platfrom_id=data["platformId"])

                hours_of_stay_rate = [
                    rate.hours_of_stay
                    for rate in related_rate
                    if int(rate.hours_of_stay) == int(booking_duration)
                ]
                if len(hours_of_stay_rate) == 0:
                    raise exceptions.UnableDoBooking(
                        f"{room.room_type.type_name} with {int(booking_duration)} hours duration dont have any rate assigned"
                    )
                room_rates.append(
                    {
                        "room_id": room.pk,
                        "rates": related_rate,
                    }
                )

            if data["adult"] > max_pax_total:
                raise exceptions.AdultPax(
                    f"adult pax should be not more than {max_pax_total}"
                )

            total_sum = calculate_total_booking_sum(
                start_date=data["checkInDatetime"],
                end_date=data["checkOutDatetime"],
                room_and_rates=room_rates,
            )

            booking_no = generate_booking_no(
                airport_code=airport_code, current_counter=counter.counter
            )

            booking = Booking.objects.create(
                booking_no=booking_no,
                booking_made_datetime=datetime.now(),
                platform_id=platform.pk,
                customer_staying_id=main_guest.pk,
                customer_booked=main_guest.firstname + " " + main_guest.lastname,
                adult_pax=data["adult"],
                child_pax=data["child"],
                details=data["details"],
                lot_id=lot_id,
                sum=total_sum,
                ota_code=(
                    None
                    if platform.platform == "Walk In"
                    or platform.platform == "Hotel Website"
                    else data["otaCode"]
                ),
            )

            booking_status = BookingStatus.objects.create(
                booking_id=booking.pk,
                booking_status="Booked",
                check_in_datetime=data["checkInDatetime"],
                check_out_datetime=data["checkOutDatetime"],
                max_check_out_datetime=max_checkout_datetime,
                status_datetime=datetime.now(),
                is_latest=True,
            )

            room_booking_list = []
            for room in data["rooms"]:
                room_data = Room.objects.get(pk=room["roomId"])
                room_rate = room_data.related_room_rate(platfrom_id=data["platformId"])

                if not room_rate.exists():
                    raise exceptions.UnableDoBooking(
                        f"Platform selected assigned tier dont have any assigned room rate for {room_data.room_type.type_name} type, with this booking's duration"
                    )

                sum = calculate_rate_sum(
                    start_date=data["checkInDatetime"],
                    end_date=data["checkOutDatetime"],
                    room_rate=room_rate,
                )

                room_booking_list.append(
                    RoomBooking(
                        booking_id=booking.pk,
                        room_id=room["roomId"],
                        person_in_charge_id=room["picId"],
                        details=room["details"],
                        sum=sum,
                        status="Booked",
                    )
                )
            RoomBooking.objects.bulk_create(room_booking_list)

            try:
                pin = data["pin"]
            except:
                pin = None

            if pin:
                update_session(
                    module="booking",
                    pin=data["pin"],
                    booking_id=booking.pk,
                    process="check-in",
                )

        session_lock = BookingSessionRoomLock.objects.filter(room_id__in=room_id_list)
        session_lock.delete()

    return booking


def register_booking_without_pic(data: RegisterBookingWithoutPicSerializer, user):
    validated_data = data.validated_data

    with transaction.atomic():

        accounts_lot = Lot.objects.get(pk=user.lot_id)

        airport = accounts_lot.airport_code_id
        airport_code = airport.airport_code

        with BookingNumberCounter.Context(airport_code) as counter:
            platform: Platform = Platform.objects.get(pk=validated_data["platform_id"])

            if validated_data["adult"] < 1:
                raise exceptions.UnableDoBooking("Adult pax should be more than 1")

            room_id_list = [item["room_id"] for item in validated_data["rooms"]]

            lot_settings = get_lot_settings_by_lot_with_serializer(user.lot_id)

            check_in_datetime = validated_data["check_in_datetime"]
            check_out_datetime = validated_data["check_out_datetime"]

            grace_period = 0
            for setting in lot_settings:
                if (
                    setting["settings_category"] == "Room Settings"
                    and setting["settings_name"] == "Grace Period"
                ):
                    grace_period = int(setting["settings_description"])

            max_checkout_datetime = calculate_max_checkout_datetime(
                check_out_datetime=check_out_datetime, grace_period_mins=grace_period
            )

            is_room_booking_exist = exist_room_booking(
                room_ids=room_id_list,
                start_date=check_in_datetime,
                end_date=max_checkout_datetime,
            )

            filtered_booking_exist = []
            for room_book in is_room_booking_exist:
                if room_book.status == "Reservation" and platform.can_late_payment:
                    continue

                filtered_booking_exist.append(room_book)

            if len(filtered_booking_exist) > 0:
                raise exceptions.UnableDoBooking(
                    f"Room {is_room_booking_exist[0].room.room_code} already booked"
                )

            main_room = (
                Room.objects.filter(pk=validated_data["rooms"][0]["room_id"])
                .annotate(
                    room_airport=F(
                        "room_type_id__roomzone_id__lot_id_id__airport_code_id_id__airport_code"
                    )
                )
                .first()
            )

            time_differenece = check_out_datetime - check_in_datetime
            booking_duration = time_differenece.total_seconds() / 3600

            rooms_obj = Room.objects.filter(room_id__in=room_id_list)
            room_rates = []
            max_pax_total = 0
            for room in rooms_obj:
                max_pax_total += int(room.room_type.max_pax)
                related_rate = room.related_room_rate(
                    platfrom_id=validated_data["platform_id"]
                )

                hours_of_stay_rate = [
                    rate.hours_of_stay
                    for rate in related_rate
                    if int(rate.hours_of_stay) == int(booking_duration)
                ]
                if len(hours_of_stay_rate) == 0:
                    raise exceptions.UnableDoBooking(
                        f"{room.room_type.type_name} with {int(booking_duration)} hours duration dont have any rate assigned"
                    )
                room_rates.append(
                    {
                        "room_id": room.pk,
                        "rates": related_rate,
                    }
                )

            if validated_data["adult"] > max_pax_total:
                raise exceptions.UnableDoBooking(
                    f"adult pax should be not more than {max_pax_total}"
                )

            total_sum = calculate_total_booking_sum(
                start_date=check_in_datetime,
                end_date=check_out_datetime,
                room_and_rates=room_rates,
            )

            booking_no = generate_booking_no(
                airport_code=airport_code, current_counter=counter.counter
            )

            booking = Booking.objects.create(
                booking_no=booking_no,
                booking_made_datetime=datetime.now(),
                platform_id=platform.pk,
                customer_staying_id=None,
                customer_booked=validated_data["customer_booked"],
                adult_pax=validated_data["adult"],
                child_pax=validated_data["child"],
                details=validated_data["details"],
                lot_id=user.lot_id,
                sum=total_sum,
                ota_code=(
                    None
                    if platform.platform == "Walk In"
                    or platform.platform == "Hotel Website"
                    else validated_data["ota_code"]
                ),
            )

            booking_status = BookingStatus.objects.create(
                booking_id=booking.pk,
                booking_status="Booked",
                check_in_datetime=check_in_datetime,
                check_out_datetime=check_out_datetime,
                max_check_out_datetime=max_checkout_datetime,
                status_datetime=datetime.now(),
                is_latest=True,
            )

            room_booking_list = []
            for room in validated_data["rooms"]:
                room_data = Room.objects.get(pk=room["room_id"])
                room_rate = room_data.related_room_rate(
                    platfrom_id=validated_data["platform_id"]
                )

                if not room_rate.exists():
                    raise exceptions.UnableDoBooking(
                        f"Platform selected assigned tier dont have any assigned room rate for {room_data.room_type.type_name} type, with this booking's duration"
                    )

                sum = calculate_rate_sum(
                    start_date=check_in_datetime,
                    end_date=check_out_datetime,
                    room_rate=room_rate,
                )

                room_booking_list.append(
                    RoomBooking(
                        booking_id=booking.pk,
                        room_id=room["room_id"],
                        person_in_charge_id=None,
                        details=room["details"],
                        sum=sum,
                        status="Booked",
                    )
                )
            RoomBooking.objects.bulk_create(room_booking_list)

        session_lock = BookingSessionRoomLock.objects.filter(room_id__in=room_id_list)
        session_lock.delete()

    return booking


def transfer_room(serializer: BookingRoomTransferUpgradeSerializer, user):
    """
    A function to transfer room given a room booking id and a new room id
    """

    validated_data = serializer.validated_data
    room_booking_id = validated_data["room_booking_id"]
    new_room_id = validated_data["new_room_id"]

    room_booking = (
        RoomBooking.objects.select_for_update(nowait=True)
        .select_related("room")
        .get(room_booking_id=room_booking_id)
    )

    latest_booking_status = BookingStatus.objects.get(
        booking_id=room_booking.booking.pk, is_latest=True
    )

    new_room = Room.objects.select_for_update(nowait=True).get(room_id=new_room_id)

    start_date = latest_booking_status.check_in_datetime
    end_date = latest_booking_status.check_out_datetime

    is_room_booking_exist = exist_room_booking(
        room_ids=[new_room.pk],
        start_date=start_date,
        end_date=end_date,
        exclude_reservation=True,
    )

    if len(is_room_booking_exist) > 0:
        raise exceptions.UnableTransferRoom(
            "room already occupied. please choose another room"
        )

    booking = room_booking.booking

    time_differenece = end_date - start_date
    hours_difference = time_differenece.total_seconds() / 3600

    if str(room_booking.room.room_type) != str(new_room.room_type):
        create_new_pending_transaction(
            booking_id=booking.pk,
            transaction_details=[
                {
                    "item_id": str(new_room.pk),
                    "item_name": str(new_room.room_code),
                    "item_type": str(new_room.room_type.type_name),
                    "price": str(0),
                    "duration": int(hours_difference),
                    "quantity": str(1),
                    "category": TransactionItemCategory.ROOM_SALES,
                }
            ],
            sum=0,
            account_id=user.account_id,
            is_room_booking=True,
        )
        # raise exceptions.UnableTransferRoom("Different Room type. Cannot Transfer")
    else:
        transaction_list = Transaction.objects.filter(
            is_room_booking=True, booking_id=booking.pk
        )

        for transaction in transaction_list:
            new_items = []
            for item in transaction.items:
                if str(item["item_id"]) == str(room_booking.room.pk):
                    item["item_name"] = str(new_room.room_code)
                    item["item_id"] = str(new_room.pk)
                new_items.append(item)

            transaction.items = new_items
            transaction.payment_details = json.dumps(new_items)
            transaction.save()

    room_booking.room.status = ROOMSTATUS.VACANT_DIRTY
    room_booking.room.save()

    room_booking.room = new_room
    room_booking.save()

    new_room.status = ROOMSTATUS.OCCUPIED_DIRTY
    new_room.save()

    locker_bookings = LockerBooking.objects.filter(
        booking_id=room_booking.booking.pk, room_code=room_booking.room.room_code
    )

    locker_bookings.update(room_code=new_room.room_code)


def create_express_booking(serializer: ExpressBookingSerializer):
    data = serializer.data
    check_in_datetime = data["checkInDatetime"]
    check_out_datetime = data["checkOutDatetime"]
    rooms_details = data["roomsDetails"]

    platform: Platform = Platform.objects.get(pk=data["platformId"])

    available_rooms = []
    total_rooms = 0
    for item in rooms_details:
        total_rooms += item["count"]
        rooms_data = Room.objects.filter(
            status=ROOMSTATUS.VACANT_CLEANED, room_type_id=item["roomTypeId"]
        )[: item["count"]]

        if rooms_data.count() == item["count"]:
            for room_item in rooms_data:
                available_rooms.append(room_item)
        else:
            rooms = (
                Room.objects.filter(room_type_id=item["roomTypeId"])
                .exclude(
                    room_bookings__booking__booking_status__max_check_out_datetime__gte=check_in_datetime,
                    room_bookings__booking__booking_status__check_in_datetime__lte=check_out_datetime,
                )
                .distinct()[: item["count"]]
            )

            if rooms.count() < item["count"]:
                raise exceptions.RoomNotExist(
                    f'insufficient room for {item["roomTypeId"]} room type'
                )

            for room in rooms:
                available_rooms.append(room)

    if len(available_rooms) == total_rooms:
        with transaction.atomic():
            with BookingNumberCounter.Context("KLIA") as counter:
                booking_no = generate_booking_no("KLIA", counter.counter)

                room_rates = []
                for room in available_rooms:
                    room_rates.append(
                        {"room_id": room.pk, "rates": room.related_room_rate()}
                    )

                total_sum = calculate_total_booking_sum(
                    start_date=check_in_datetime,
                    end_date=check_out_datetime,
                    room_and_rates=room_rates,
                )

                pic = Customer.objects.get(pk=data["picId"])

                booking = Booking.objects.create(
                    booking_no=booking_no,
                    booking_made_datetime=datetime.now(),
                    platform_id=platform.pk,
                    adult_pax=data["adult"],
                    child_pax=data["child"],
                    sum=total_sum,
                    details=data["remarks"],
                    customer_staying_id=pic.pk,
                    customer_booked=f"{pic.firstname} {pic.lastname}",
                    ota_code=(
                        None
                        if platform.platform == "Walk In"
                        or platform.platform == "Hotel Website"
                        else data["otaCode"]
                    ),
                )

                booking_status = BookingStatus.objects.create(
                    booking_id=booking.pk,
                    booking_status="Booked",
                    check_in_datetime=check_in_datetime,
                    check_out_datetime=check_out_datetime,
                    status_datetime=datetime.now(),
                    is_latest=True,
                )

                room_booking_list = []
                for room in available_rooms:
                    room_rate = room.related_room_rate()

                    sum = calculate_rate_sum(
                        start_date=check_in_datetime,
                        end_date=check_out_datetime,
                        room_rate=room_rate,
                    )
                    room_booking_list.append(
                        RoomBooking(
                            booking_id=booking.pk,
                            room_id=room.pk,
                            person_in_charge_id=data["picId"],
                            details=data["remarks"],
                            sum=sum,
                            status="Booked",
                        )
                    )
                RoomBooking.objects.bulk_create(room_booking_list)
        return booking
    else:
        raise Exception("Room(s) are not Availble")


def update_booking_status(data: UpdateBookingStatusInfoSerializer, action):
    validated_data = data.validated_data

    try:
        booking: Booking = Booking.objects.get(pk=validated_data["booking_id"])
    except:
        raise Exception("booking not found")

    room_booking = RoomBooking.objects.filter(booking_id=validated_data["booking_id"])

    booking_status = BookingStatus.objects.filter(
        booking_id=booking.pk, is_latest=True
    ).first()

    if action == "cancel":
        # if booking_status.booking_status == "Check In":
        #     raise exceptions.UnableCancel("bookings already checked in")

        if booking_status.booking_status == "Check Out":
            raise exceptions.UnableCancel("bookings already checked out")

        transactions = Transaction.objects.filter(
            booking_id=validated_data["booking_id"],
            is_latest=True,
        )

        pending_transaction = transactions.filter(transaction_status="Pending Payment")
        if pending_transaction.exists():
            raise exceptions.UnableCancel(
                "there is still a not yet completed transaction"
            )

        paid_room_booking_transaction = transactions.filter(
            transaction_status="Paid", is_room_booking=True
        )
        refund_room_booking_transaction = transactions.filter(
            transaction_status="Refund", is_room_booking=True
        )
        if (
            paid_room_booking_transaction.exists()
            and not refund_room_booking_transaction.exists()
        ):
            raise exceptions.UnableCancel("bookings already paid")

        booking.is_cancelled = True
        booking.save()

        new_status = "Cancelled"

    if action == "check out":
        for room in room_booking:
            if not room.actual_checkin_date_time:
                raise exceptions.UnableCheckout(
                    f"room {room.room.room_code} not checked in yet"
                )

            # time_range = (
            #     validated_data["actual_checkout_datetime"]
            #     - room.actual_checkin_date_time
            # )
            # hours = time_range.total_seconds() / 3600

            # if hours < 1:
            #     raise exceptions.UnableCheckout(
            #         "check out can be done must be more than 1 hour after check in"
            #     )

            room.room.status = "vacant, dirty"
            room.room.save()

        if not booking.platform.can_late_payment:
            transactions = Transaction.objects.filter(
                booking_id=validated_data["booking_id"],
                transaction_status="Pending Payment",
            ).exists()

            if transactions:
                raise exceptions.UnableCheckout(
                    "need to complete all pending transaction"
                )

        assigned_locker_bookings = LockerBooking.objects.filter(
            booking=validated_data["booking_id"]
        )
        assigned_shower_bookings = ShowerBooking.objects.filter(
            booking=validated_data["booking_id"]
        )

        # consent form and hotel policy check
        if (
            room_booking.exists()
            or assigned_locker_bookings.exists()
            or assigned_shower_bookings.exists()
        ):

            consent_form = ConsentForm.objects.filter(booking_id=booking.pk).first()

            if (
                not consent_form
                or not consent_form.consent_checkout
                or consent_form.consent_checkout == ""
            ):
                raise exceptions.UnableCheckout(
                    "Please sign Check Out Hotel Policy first"
                )

        try:
            pin = validated_data["pin"]
        except:
            pin = None

        if pin:
            update_session(
                module="booking",
                pin=validated_data["pin"],
                process="check-out",
                booking_id=validated_data["booking_id"],
            )

        """
            check for incomplete guest portofolio info
        """
        if room_booking.exists():
            main_room_booking = room_booking.first()
            if (
                not main_room_booking.person_in_charge.firstname
                or not main_room_booking.person_in_charge.lastname
                or not main_room_booking.person_in_charge.id_no
                # or not main_room_booking.person_in_charge.phone_number
                # or not main_room_booking.person_in_charge.email
                or not main_room_booking.person_in_charge.country
                or not main_room_booking.person_in_charge.gender
                or not main_room_booking.person_in_charge.id_type
                or not main_room_booking.person_in_charge.member
            ):
                raise exceptions.UnableCheckout(
                    "need to input all information on guest protofolio"
                )
        else:
            if (
                not booking.customer_staying.firstname
                or not booking.customer_staying.lastname
                or not booking.customer_staying.id_no
                # or not booking.customer_staying.phone_number
                # or not booking.customer_staying.email
                or not booking.customer_staying.country
                or not booking.customer_staying.gender
                or not booking.customer_staying.id_type
                or not booking.customer_staying.member
            ):
                raise exceptions.UnableCheckout(
                    "need to input all information on guest protofolio"
                )

        new_status = "Check Out"

    for room in room_booking:
        room.actual_checkout_date_time = validated_data["actual_checkout_datetime"]
        room.status = new_status
        room.save()

    locker_bookings = LockerBooking.objects.filter(booking_id=booking.pk)
    for locker_book in locker_bookings:
        locker_book.status = new_status
        locker_book.locker.status = "Available"
        locker_book.save()
        locker_book.locker.save()

    shower_bookings = ShowerBooking.objects.filter(booking_id=booking.pk)
    for shower_book in shower_bookings:
        shower_book.status = new_status
        shower_book.save()

    booking_status.is_latest = False
    booking_status.save()

    new_booking_status = BookingStatus.objects.create(
        booking_id=booking.pk,
        booking_status=new_status,
        check_in_datetime=booking_status.check_in_datetime,
        check_out_datetime=booking_status.check_out_datetime,
        max_check_out_datetime=validated_data["actual_checkout_datetime"],
        status_datetime=datetime.now(),
        is_latest=True,
        has_no_show=booking_status.has_no_show,
    )


def check_in(booking_id, user, pin=None):
    try:
        booking = Booking.objects.get(pk=booking_id)
    except:
        raise exceptions.BookingNotFound("booking does not exist")

    current_room_bookings = RoomBooking.objects.filter(booking_id=booking.pk)
    booking_status = (
        BookingStatus.objects.select_for_update()
        .filter(booking_id=booking.pk, is_latest=True)
        .first()
    )

    if current_room_bookings.exists():
        platform = booking.platform

        if not platform.can_late_payment:
            room_booking_transaction_pending = Transaction.objects.filter(
                booking_id=booking.pk,
                is_room_booking=True,
                transaction_status="Pending Payment",
            ).exists()

            if room_booking_transaction_pending:
                raise exceptions.UnableCheckIn("Settle payment before check in")

    else:
        transaction_obj = Transaction.objects.filter(
            booking_id=booking.pk, transaction_status="Paid"
        ).exists()

        if not transaction_obj:
            raise exceptions.UnableCheckIn("Settle payment before check in")

    """
    check in validation (after status no show or early check in)
    """
    current_time = timezone.now()
    early_check_in = current_time < booking_status.check_in_datetime
    late_check_in = current_time > booking_status.check_out_datetime

    more_than_one_hour = False
    if current_time >= booking_status.check_in_datetime:
        duration_range = (
            current_time - booking_status.check_in_datetime
        ).total_seconds() / 3600

        if duration_range > 0.5:
            more_than_one_hour = True

    new_check_in_datetime = None
    new_check_out_datetime = None
    new_max_check_out_datetime = None
    if (
        booking_status.booking_status == "No Show"
        or early_check_in
        or late_check_in
        or more_than_one_hour
    ):
        prev_check_in_datetime = booking_status.check_in_datetime
        prev_check_out_datetime = booking_status.check_out_datetime

        # calculate new check in and check out datetime
        time_difference = prev_check_out_datetime - prev_check_in_datetime
        hours = time_difference.total_seconds() / 3600
        check_in_datetime = timezone.now()
        check_out_datetime = check_in_datetime + timedelta(hours=hours)

        lot_settings = get_lot_settings_by_lot_with_serializer(user.lot_id)
        grace_period = 0
        for setting in lot_settings:
            if (
                setting["settings_category"] == "Room Settings"
                and setting["settings_name"] == "Grace Period"
            ):
                grace_period = int(setting["settings_description"])

        max_checkout_datetime = calculate_max_checkout_datetime(
            check_out_datetime=check_out_datetime, grace_period_mins=grace_period
        )

        # check if the room occupied in new booking time range
        for room_booking in current_room_bookings:
            booked_room_bookings = exist_room_booking(
                start_date=check_in_datetime,
                end_date=max_checkout_datetime,
                room_ids=[room_booking.room.pk],
                current_booking_id=booking.pk,
                exclude_reservation=True,
            )

            if len(booked_room_bookings) > 0:
                raise exceptions.UnableCheckIn(
                    "Will overlapped with next booking, please create a new booking instead."
                )

        # set new time for check_in params
        new_check_in_datetime = check_in_datetime
        new_check_out_datetime = check_out_datetime
        new_max_check_out_datetime = max_checkout_datetime

        # booking_status.is_latest = False
        # booking_status.save()

        # new_booking_status = BookingStatus.objects.create(
        #     booking_id=booking.pk,
        #     booking_status=booking_status.booking_status,
        #     check_in_datetime=check_in_datetime,
        #     check_out_datetime=check_out_datetime,
        #     status_datetime=datetime.now(),
        #     max_check_out_datetime=max_checkout_datetime,
        #     is_latest=True,
        #     has_no_show=True if booking_status.booking_status == "No Show" else False,
        # )

    assigned_locker_bookings = LockerBooking.objects.filter(booking=booking_id)
    assigned_shower_bookings = ShowerBooking.objects.filter(booking=booking_id)

    # consent form and hotel policy check
    if (
        current_room_bookings.exists()
        or assigned_locker_bookings.exists()
        or assigned_shower_bookings.exists()
    ):

        consent_form = ConsentForm.objects.filter(
            booking_id=booking.pk, consent_checkin__isnull=False
        ).first()

        if (
            not consent_form
            or not consent_form.consent_checkin
            or consent_form.consent_checkin == ""
        ):
            raise exceptions.UnableCheckIn("Please sign Check In Hotel Policy first")

    booking.check_in(
        new_check_in_datetime=new_check_in_datetime,
        new_check_out_datetime=new_check_out_datetime,
        new_max_check_out_datetime=new_max_check_out_datetime,
        is_no_show=(
            True
            if booking_status.booking_status == "No Show" or booking_status.has_no_show
            else False
        ),
    )

    locker_bookings = LockerBooking.objects.filter(booking_id=booking.pk)
    for locker_book in locker_bookings:
        locker_book.status = "Check In"
        locker_book.save()

    shower_bookings = ShowerBooking.objects.filter(booking_id=booking.pk)
    for shower_book in shower_bookings:
        shower_book.status = "Check In"
        shower_book.save()

    if pin:
        update_session(
            module="booking", pin=pin, process="check-out", booking_id=booking_id
        )


def add_room_booking_service(serializer: BookingAddRoomSerializer, user):
    booking = Booking.objects.get(booking_id=serializer.validated_data["booking_id"])
    start_time = datetime.fromtimestamp(serializer.validated_data["start_time"])
    end_time = datetime.fromtimestamp(serializer.validated_data["end_time"])

    current_status = BookingStatus.objects.filter(booking_id=booking.pk, is_latest=True)
    current_status_data = current_status.first()
    if current_status_data:
        time_difference = timezone.now() - current_status_data.check_in_datetime
        if time_difference > timedelta(minutes=30):
            raise Exception(
                "Please create a new booking instead. Interval has exceeded 30 mins"
            )

    start_time = timezone.make_aware(start_time, timezone.get_current_timezone())
    end_time = timezone.make_aware(end_time, timezone.get_current_timezone())

    actual_checkin_datetime = None
    room_booking_status = "Booked"
    if current_status_data and current_status_data.booking_status == "Check In":
        prev_room_booking = RoomBooking.objects.filter(booking_id=booking.pk).first()
        actual_checkin_datetime = (
            prev_room_booking.actual_checkin_date_time if prev_room_booking else None
        )
        room_booking_status = "Check In"

    for room in serializer.validated_data["rooms"]:
        booking.add_room(
            room_id=room["room_id"],
            pic=room["pic_id"],
            date_range=(start_time, end_time),
            remarks=room["remarks"],
            status=room_booking_status,
            actual_checkin_date_time=actual_checkin_datetime,
        )

    if current_status_data and current_status_data.booking_status == "Confirm Booking":
        current_status_data.is_latest = False
        current_status_data.save()

        BookingStatus.objects.create(
            booking_id=booking.pk,
            booking_status="Booked",
            check_in_datetime=current_status_data.check_in_datetime,
            check_out_datetime=current_status_data.check_out_datetime,
            max_check_out_datetime=current_status_data.max_check_out_datetime,
            status_datetime=timezone.now(),
            is_latest=True,
            has_no_show=current_status_data.has_no_show,
        )

    room_id_list = [item["room_id"] for item in serializer.validated_data["rooms"]]
    rooms = Room.objects.filter(room_id__in=room_id_list)

    time_differenece = end_time - start_time
    hours_difference = time_differenece.total_seconds() / 3600

    details_json = []
    sum = 0
    for room in rooms:
        rate = calculate_rate_sum(
            start_date=start_time,
            end_date=end_time,
            room_rate=room.related_room_rate(platfrom_id=booking.platform_id),
        )

        if rate == 0:
            raise Exception(f"rate not set for {room.room_type.type_name}")

        sum += rate

        details_json.append(
            {
                "item_id": str(room.pk),
                "item_name": room.room_code,
                "item_type": room.room_type.type_name,
                "duration": int(hours_difference),
                "quantity": 1,
                "price": str(rate),
                "category": TransactionItemCategory.ROOM_SALES,
            }
        )

    transactions = Transaction.objects.filter(
        booking_id=booking.pk, is_latest=True, is_room_booking=True
    )
    if transactions.count() > 0:
        new_transaction = create_new_pending_transaction(
            booking_id=booking.pk,
            transaction_details=details_json,
            account_id=user.account_id,
            sum=sum,
            is_room_booking=True,
        )


def adjust_room_duration(added_hour, booking_id):
    room_bookings = RoomBooking.objects.filter(booking_id=booking_id)

    prev_status = BookingStatus.objects.filter(
        booking_id=booking_id, is_latest=True
    ).first()

    start_datetime = prev_status.check_out_datetime + timedelta(minutes=1)
    new_end_datetime = start_datetime + timedelta(hours=added_hour)
    new_max_checkout_datetime = prev_status.max_check_out_datetime + timedelta(
        hours=added_hour
    )

    room_ids = []
    for item in room_bookings:
        room_ids.append(item.room_id)
    is_room_available = RoomBooking.objects.filter(
        Q(room_id__in=room_ids)
        & Q(
            Q(booking__booking_status__is_latest=True)
            & Q(
                Q(
                    booking__booking_status__check_in_datetime__range=(
                        start_datetime,
                        new_end_datetime,
                    )
                )
                | Q(
                    booking__booking_status__check_out_datetime__range=(
                        start_datetime,
                        new_end_datetime,
                    )
                )
                | Q(
                    Q(booking__booking_status__check_in_datetime__lte=start_datetime)
                    & Q(
                        booking__booking_status__max_check_out_datetime__gte=new_end_datetime
                    )
                )
            )
        )
    ).exists()
    if is_room_available:
        return False

    prev_status.is_latest = False
    prev_status.save()

    new_status = BookingStatus.objects.create(
        booking_id=booking_id,
        booking_status=prev_status.booking_status,
        check_in_datetime=prev_status.check_in_datetime,
        check_out_datetime=new_end_datetime,
        max_check_out_datetime=new_max_checkout_datetime,
        status_datetime=datetime.now(),
        is_latest=True,
        has_no_show=prev_status.has_no_show,
    )

    return True


def get_available_rooms(serializer: OtaReservationSerializer, user):
    validated_data = serializer.validated_data

    start_datetime = validated_data["check_in_datetime"]
    end_datetime = start_datetime + timedelta(hours=validated_data["duration"])

    available_room_list = []
    for room in validated_data["rooms"]:
        room_type_name = room["room_type_name"]
        count = room["count"]

        selected_rooms = Room.objects.select_related(
            "room_type", "room_type__roomzone"
        ).filter(
            room_type__type_name=room_type_name,
            room_type__roomzone__lot_id_id=user.lot_id,
            is_archived=False,
        )

        room_ids = [room.pk for room in selected_rooms]

        exclude_room_bookings = exist_room_booking(
            start_date=start_datetime, end_date=end_datetime, room_ids=room_ids
        )

        excluded_ids = [room_booking.room_id for room_booking in exclude_room_bookings]

        available_rooms = selected_rooms.exclude(room_id__in=excluded_ids).order_by(
            "room_code"
        )
        available_rooms = available_rooms[:count]

        for room in available_rooms:
            available_room_list.append(room)

    return available_room_list


def reserve_ota(rooms, start_date, end_date, lot_id):
    platform = Platform.objects.get(
        Q(
            Q(platform="OTA-Reservation Slot")
            | Q(platform="OTA - Reservation Slot")
            | Q(platform="Reservation Slot")
        )
        & Q(is_archive=False)
    )

    booking = Booking.objects.create(
        booking_no=None,
        booking_made_datetime=timezone.datetime.now(),
        platform_id=platform.pk,
        customer_staying=None,
        customer_booked=None,
        adult_pax=0,
        child_pax=0,
        details="OTA Reservation Slot",
        sum=0,
        ota_code=None,
        lot_id=lot_id,
    )

    booking_status = BookingStatus.objects.create(
        booking_id=booking.pk,
        booking_status="Reservation",
        check_in_datetime=start_date,
        check_out_datetime=end_date,
        max_check_out_datetime=end_date,
        status_datetime=datetime.now(),
        is_latest=True,
    )

    room_booking_list = []
    for room in rooms:
        room_booking_list.append(
            RoomBooking(
                booking_id=booking.pk,
                room_id=room.pk,
                person_in_charge_id=None,
                details="OTA Reservation Slot",
                sum=0,
                status="Reservation",
            )
        )
    RoomBooking.objects.bulk_create(room_booking_list)


def get_all_guest_booking_list():
    yesterday = datetime.now() - timedelta(days=1)
    tomorrow = datetime.now() + timedelta(days=1)
    room_bookings = (
        RoomBooking.objects.filter(
            Q(
                booking__booking_made_datetime__range=(
                    yesterday,
                    tomorrow,
                )
            )
        )
        .values("person_in_charge", "booking")
        .distinct()
    )
    added_info_guest = room_bookings.annotate(
        booking_datetime=F("booking__booking_made_datetime"),
        room_no=F("room__room_code"),
        status=F("status"),
        person_in_charge_name=ExpressionWrapper(
            Concat(
                "person_in_charge__firstname", Value(" "), "person_in_charge__lastname"
            ),
            output_field=CharField(),
        ),
    )


def create_booking_for_pos_transaction(
    item_sum,
    user,
    booking_status,
    guest_id=None,
    start_date=None,
    end_date=None,
    platform_id=None,
):

    accounts_lot = Lot.objects.get(pk=user.lot_id)
    airport = accounts_lot.airport_code_id
    airport_code = airport.airport_code

    with BookingNumberCounter.Context(airport_code) as counter:

        if guest_id:
            customer = Customer.objects.get(pk=guest_id)
        else:
            customer = Customer.objects.filter(firstname="Non Guest")
            if customer.count() == 0:
                customer = create_non_guest_customer()
            else:
                customer = customer.first()

        if not platform_id:
            platform = Platform.objects.filter(platform="Walk In").first()
            platform_id = platform.pk

        booking_no = generate_booking_no(
            airport_code=airport_code, current_counter=counter.counter
        )

        current_time = timezone.now()
        booking = Booking.objects.create(
            booking_no=booking_no,
            booking_made_datetime=current_time,
            platform_id=platform_id,
            customer_staying_id=customer.pk,
            customer_booked=customer.firstname,
            details="",
            sum=item_sum,
            ota_code=None,
            lot_id=user.lot_id,
        )

        pending_booking_status = BookingStatus.objects.create(
            booking_id=booking.pk,
            booking_status=booking_status,
            check_in_datetime=start_date if start_date else current_time,
            check_out_datetime=end_date if end_date else current_time,
            max_check_out_datetime=end_date if end_date else current_time,
            status_datetime=current_time,
            is_latest=True,
        )

        return booking


def check_out_pos_booking(booking_id):

    booking_status = BookingStatus.objects.filter(
        booking_id=booking_id, is_latest=True
    ).first()

    booking_status.is_latest = False
    booking_status.save()

    current_time = timezone.now()

    paid_booking_status = BookingStatus.objects.create(
        booking_id=booking_id,
        booking_status="Check Out",
        check_in_datetime=booking_status.check_in_datetime,
        check_out_datetime=booking_status.check_out_datetime,
        max_check_out_datetime=booking_status.max_check_out_datetime,
        status_datetime=booking_status.status_datetime,
        is_latest=True,
        has_no_show=booking_status.has_no_show,
    )


def create_merch_booking(transaction_data: Transaction):

    merch_info = []
    for item in transaction_data.items:
        if item["item_type"] == "Merch":
            merch_info.append({"id": item["item_id"], "qty": int(item["quantity"])})

    if len(merch_info) == 0:
        return

    new_merch_list = []
    for info in merch_info:
        new_merch_list.append(
            MerchBooking(
                quantity=info["qty"],
                purchase_datetime=timezone.now(),
                booking_id=transaction_data.booking_id,
                customer_id=transaction_data.customer_id,
                merch_id=info["id"],
                status="Paid",
            )
        )

    MerchBooking.objects.bulk_create(new_merch_list)


def transfer_booking(serializer: TransferBookingSerializer, user):
    data = serializer.validated_data
    booking_id = data["booking_id"]
    room_update = data["room_update"]
    new_lot = data["new_lot"]

    new_rooms_pk = []
    for i in room_update:
        new_rooms_pk.extend(i["new_room_ids"])

    accounts_lot = Lot.objects.get(pk=new_lot)
    airport = accounts_lot.airport_code_id
    airport_code = airport.airport_code
    with transaction.atomic():
        with BookingNumberCounter.Context(airport_code) as counter:

            previous_booking = Booking.objects.get(pk=booking_id)

            prev_booking_status = BookingStatus.objects.get(
                booking_id=previous_booking.pk, is_latest=True
            )

            from_date = prev_booking_status.check_in_datetime
            to_date = prev_booking_status.max_check_out_datetime

            is_room_booking_exist = exist_room_booking(
                start_date=from_date,
                end_date=to_date,
                room_ids=new_rooms_pk,
                exclude_reservation=True,
            )

            if len(is_room_booking_exist) > 0:
                raise exceptions.TransferedBooking(
                    "room already occupied. try to find another room"
                )

            # generate booking number
            booking_no = generate_booking_no(
                airport_code=airport_code, current_counter=counter.counter
            )

            previous_lot = (
                RoomBooking.objects.filter(booking=booking_id)
                .first()
                .room.room_type.roomzone.lot_id
            )

            new_rooms = Room.objects.select_for_update().filter(
                room_id__in=new_rooms_pk
            )

            new_lot = new_rooms.first().room_type.roomzone.lot_id

            previous_booking.is_cancelled = True
            previous_booking.save()

            # create new booking
            booking = Booking.objects.create(
                booking_no=booking_no,
                booking_made_datetime=previous_booking.booking_made_datetime,
                platform_id=previous_booking.platform.pk,
                adult_pax=previous_booking.adult_pax,
                child_pax=previous_booking.child_pax,
                sum=previous_booking.sum,
                details=f"old booking no {previous_booking.booking_no}",
                customer_staying=previous_booking.customer_staying,
                customer_booked=previous_booking.customer_booked,
                ota_code=previous_booking.ota_code,
                lot_id=new_lot.pk,
            )

            previous_booking.details = f"new booking no {booking_no}"
            previous_booking.save()

            TransferBooking.objects.create(
                from_booking_id=previous_booking.pk, to_booking_id=booking.pk
            )

            # update previous booking's booking status become Transfer to <new lot>
            if "Transfer From" in prev_booking_status.booking_status:
                raise exceptions.TransferedBooking(
                    "Not allowed to Transfer a Booking twice"
                )

            prev_booking_status.is_latest = False
            prev_booking_status.save()

            prev_booking_new_status = BookingStatus.objects.create(
                booking_id=previous_booking.pk,
                booking_status=f"Transfer to {new_lot.lot_number}",
                check_in_datetime=prev_booking_status.check_in_datetime,
                check_out_datetime=prev_booking_status.check_out_datetime,
                max_check_out_datetime=prev_booking_status.max_check_out_datetime,
                status_datetime=datetime.now(),
                is_latest=True,
                has_no_show=prev_booking_status.has_no_show,
            )

            # create new booking's boking status
            booking_status = BookingStatus.objects.create(
                booking_id=booking.pk,
                booking_status=f"Transfer From {previous_lot.lot_number}",
                check_in_datetime=prev_booking_status.check_in_datetime,
                check_out_datetime=prev_booking_status.check_out_datetime,
                max_check_out_datetime=prev_booking_status.max_check_out_datetime,
                status_datetime=datetime.now(),
                is_latest=True,
                has_no_show=prev_booking_status.has_no_show,
            )

            # get previous booking's room data
            prev_booking_room_bookings = RoomBooking.objects.filter(
                booking_id=booking_id
            )
            prev_room_ids = [item.room_id for item in prev_booking_room_bookings]
            prev_rooms = Room.objects.filter(pk__in=prev_room_ids)

            # create new room bookings
            room_booking_list = []
            for data in room_update:
                new_rooms_ids = data["new_room_ids"]

                prev_room_booking = RoomBooking.objects.get(
                    room_id=data["prev_room_id"], booking_id=previous_booking.pk
                )

                prev_room_rate = prev_room_booking.sum

                if len(new_rooms_ids) > 1:
                    rate_sum = prev_room_rate / len(new_rooms_ids)

                    for room_id in new_rooms_ids:
                        room_booking_list.append(
                            RoomBooking(
                                booking_id=booking.pk,
                                room_id=room_id,
                                person_in_charge=prev_room_booking.person_in_charge,
                                details=prev_room_booking.details,
                                sum=rate_sum,
                                status=prev_room_booking.status,
                                actual_checkin_date_time=prev_room_booking.actual_checkin_date_time,
                                actual_checkout_date_time=prev_room_booking.actual_checkout_date_time,
                            )
                        )

                else:
                    room_booking_list.append(
                        RoomBooking(
                            booking_id=booking.pk,
                            room_id=new_rooms_ids[0],
                            person_in_charge=prev_room_booking.person_in_charge,
                            details=prev_room_booking.details,
                            sum=prev_room_rate,
                            status=prev_room_booking.status,
                        )
                    )
            RoomBooking.objects.bulk_create(room_booking_list)

            prev_locker_booking = LockerBooking.objects.filter(
                booking_id=previous_booking.pk
            )

            locker_booking_list = []
            for locker_book in prev_locker_booking:
                locker_booking_list.append(
                    LockerBooking(
                        booking_id=booking.pk,
                        locker_id=locker_book.locker.pk,
                        sum=locker_book.sum,
                        status=locker_book.status,
                        locker_start_rent_datetime=locker_book.locker_start_rent_datetime,
                        locker_end_rent_datetime=locker_book.locker_end_rent_datetime,
                        room_code=locker_book.room_code,
                    )
                )
            LockerBooking.objects.bulk_create(locker_booking_list)

            # get all paid or pending room booking transaction from previous booking
            prev_booking_recorded_transaction = Transaction.objects.filter(
                booking=previous_booking.booking_id,
                is_latest=True,
                is_room_booking=True,
                transaction_status__in=["Paid", "Pending Payment"],
            )

            prev_booking_other_transaction = Transaction.objects.filter(
                booking=previous_booking.booking_id,
                is_latest=True,
                is_room_booking=False,
                transaction_status__in=["Paid", "Pending Payment"],
            )

            """
                THIS FEATURE WILL BE TEMPORARY DISABLED
            """
            # # check if there is any splitted transaction for that previous booking
            # prev_booking_transaction_sum = sum(
            #     item.sum for item in prev_booking_recorded_transaction
            # )
            # if (
            #     prev_booking_transaction_sum < previous_booking.sum
            #     and prev_booking_recorded_transaction.count() > 0
            # ):
            #     raise exceptions.UnableTransferBooking(
            #         "unable to transfer booking with splitted POA. Please save or settle all splitted POA first"
            #     )
            """
                THIS FEATURE WILL BE TEMPORARY DISABLED
            """

            current_shift = Shift.objects.filter(
                staffshift__staff_id=user.account_id, end_shift_datetime__isnull=True
            ).first()

            if prev_booking_recorded_transaction.count() > 0:
                """
                if there is paid, or pending room booking transaction

                - get data from that prev transaction
                - change respective rooms based on new available room
                - create new transaction assigned to new booking
                """

                formatted_room_update = {}
                for item in room_update:
                    formatted_room_update[str(item["prev_room_id"])] = item[
                        "new_room_ids"
                    ]

                for transaction_data in prev_booking_recorded_transaction:
                    transaction_items = transaction_data.items

                    new_transaction_items_details = []

                    for transaction_item in transaction_items:
                        try:
                            new_transaction_item_ids = formatted_room_update[
                                transaction_item["item_id"]
                            ]
                            new_room_price = int(transaction_item["price"]) / len(
                                new_transaction_item_ids
                            )
                            for new_transaction_item_id in new_transaction_item_ids:
                                room_data = Room.objects.get(pk=new_transaction_item_id)
                                new_transaction_items_details.append(
                                    {
                                        "item_id": str(room_data.pk),
                                        "item_name": str(room_data.room_code),
                                        "item_type": str(room_data.room_type.type_name),
                                        "quantity": str(1),
                                        "price": str(new_room_price),
                                        "duration": (
                                            int(transaction_item["duration"])
                                            if transaction_item["duration"]
                                            else 0
                                        ),
                                        "category": TransactionItemCategory.ROOM_SALES,
                                    }
                                )
                        except:
                            new_transaction_items_details.append(transaction_item)

                    create_modified_transaction(
                        previous_transaction=transaction_data,
                        new_booking=booking,
                        new_shift_id=current_shift.shift_id,
                        new_transaction_details=new_transaction_items_details,
                        remarks=f"old_booking_no = {previous_booking.booking_no}",
                    )

                    # Void previous booking transaction
                    if transaction_data.transaction_status == "Paid":
                        transaction_data.credit_amount = transaction_data.debit_amount
                        transaction_data.debit_amount = 0.00
                    transaction_data.transaction_status = "Void"

                    transaction_data.save()

            if prev_booking_other_transaction.count() > 0:
                for transaction_data in prev_booking_other_transaction:
                    transaction_details = transaction_data.items

                    create_modified_transaction(
                        previous_transaction=transaction_data,
                        new_booking=booking,
                        new_shift_id=current_shift.shift_id,
                        new_transaction_details=transaction_details,
                        remarks=f"old_booking_no = {previous_booking.booking_no}",
                    )

            message = ""

            unpaid_prev_booking_transaction = prev_booking_recorded_transaction.filter(
                transaction_status="Pending Payment"
            )
            unpaid_prev_booking_other_transaction = (
                prev_booking_other_transaction.filter(
                    transaction_status="Pending Payment"
                )
            )

            if (
                prev_booking_other_transaction.count() == 0
                and prev_booking_recorded_transaction.count() == 0
            ):
                message = "Please inform FO from another side to collect payment"
            elif (
                unpaid_prev_booking_other_transaction.count() == 0
                and unpaid_prev_booking_transaction.count() == 0
            ):
                message = "Payment is fully collected from original lot"
            # elif unpaid_prev_booking_transaction.count() > 0:
            #     raise exceptions.SettlePayment("Please settle room bill first")
            elif unpaid_prev_booking_other_transaction.count() > 0:
                raise exceptions.SettlePayment(
                    "Please pay and collect relevant POS before transfer"
                )

    return booking, message


def calculate_overstay_hour(booking_id):

    try:
        latest_booking_status = BookingStatus.objects.get(
            booking_id=booking_id, is_latest=True
        )
    except:
        return None

    current_time = timezone.now()

    if current_time > latest_booking_status.max_check_out_datetime:
        time_difference = current_time - latest_booking_status.max_check_out_datetime
        hour_difference = time_difference.total_seconds() / 3600

        return math.ceil(hour_difference)

    return None


def calculate_overstay_fee(hour, booking_id):

    room_bookings = RoomBooking.objects.filter(booking_id=booking_id)

    lot = room_bookings.first().room.room_type.roomzone.lot_id
    lot_settings = get_lot_settings_by_lot_id(lot_id=lot.pk)
    overstay_settings = lot_settings.filter(settings_category="Overstay Charges")

    total_charge = 0
    transaction_details = []
    for room_book in room_bookings:
        room_type = room_book.room.room_type.type_name

        if "single" in room_type.lower():
            room_type = "single"

        overstay_charge = 0
        setting_id = ""
        setting_name = ""
        setting_category = ""
        for setting in overstay_settings:
            if room_type.lower() in setting.settings_name.lower():
                setting_id = setting.pk
                setting_name = setting.settings_name
                item_type = setting.settings_category
                overstay_charge = float(setting.settings_description)

        transaction_details.append(
            {
                "item_id": setting_id,
                "item_name": setting_name,
                "item_type": setting_category,
                "quantity": hour,
                "price": overstay_charge,
                "category": TransactionItemCategory.OVERSTAY,
            }
        )

        total_charge += overstay_charge * hour

    return {"details": transaction_details, "amount": total_charge}


def confirm_late_payment_booking(instance: BookingStatus):
    if (
        not instance.booking.platform.can_late_payment
        or instance.booking_status != "Booked"
    ):
        return

    booking: Booking = instance.booking

    booking.confirm_booking()

    return True


def handle_add_booking_hour(transaction_obj: Transaction):

    # check if its from transfer booking then return
    payment_remarks = (
        transaction_obj.payment_remarks if transaction_obj.payment_remarks else ""
    )
    if "old_booking_no" in payment_remarks:
        return
    if "[SYSTEM]:Hour added" in payment_remarks:
        return

    added_duration = 0
    item_type_param = [
        "Add 1 hour",
        "3 hrs Extend",
        "6 hrs Extend",
        "12 hrs Extend",
        "24 hrs Extend",
    ]
    for item in transaction_obj.items:
        if item["item_type"] not in item_type_param:
            continue
        added_duration = int(item["duration"])
    if added_duration == 0:
        return

    room_bookings = RoomBooking.objects.filter(booking_id=transaction_obj.booking.pk)
    room_booking_ids = [room.room.pk for room in room_bookings]

    booking_status = BookingStatus.objects.filter(
        booking_id=transaction_obj.booking_id, is_latest=True
    ).first()

    new_checkout_datetime = booking_status.check_out_datetime + timedelta(
        hours=added_duration
    )
    new_max_checkout_datetime = booking_status.max_check_out_datetime + timedelta(
        hours=added_duration
    )

    booking_duration = new_checkout_datetime - booking_status.check_in_datetime
    total_seconds = booking_duration.total_seconds()
    booking_hours = total_seconds / 3600
    rounded_hours = round(booking_hours)
    new_checkout_datetime = booking_status.check_in_datetime + timedelta(
        hours=rounded_hours
    )

    room_booking_available = exist_room_booking(
        start_date=booking_status.check_in_datetime,
        end_date=new_max_checkout_datetime,
        exclude_reservation=True,
        current_booking_id=transaction_obj.booking.pk,
        room_ids=room_booking_ids,
    )

    if len(room_booking_available) > 0:
        raise transactionExceptions.UnableAddHour(
            f"Unable to add {added_duration} hours duration into this room. make sure the room is available for next {added_duration} hours"
        )

    new_status_name = booking_status.booking_status
    if new_status_name == "Overstayed":
        new_status_name = "Check In"

    new_booking_status = BookingStatus.objects.create(
        booking_id=transaction_obj.booking.pk,
        booking_status=new_status_name,
        check_in_datetime=booking_status.check_in_datetime,
        check_out_datetime=new_checkout_datetime,
        status_datetime=datetime.now(),
        max_check_out_datetime=new_max_checkout_datetime,
        is_latest=True,
        has_no_show=(
            True
            if booking_status.booking_status == "No Show" or booking_status.has_no_show
            else False
        ),
    )

    transaction_obj.payment_remarks = "[SYSTEM]:Hour added"
    transaction_obj.save()

    booking_status.is_latest = False
    booking_status.save()


# check roombooking exist
def exist_room_booking(
    start_date: datetime,
    end_date: datetime,
    room_ids: List[str] = None,
    room_type_id: str = None,
    room_type_name: str = None,
    exclude_reservation: bool = False,
    current_booking_id: str = None,
):

    actual_checkin_and_out_date_time_are_null = Q(
        actual_checkin_date_time__isnull=True
    ) & Q(actual_checkout_date_time__isnull=True)

    only_actual_checkout_date_time_is_null = Q(
        actual_checkin_date_time__isnull=False
    ) & Q(actual_checkout_date_time__isnull=True)

    actual_checkin_and_out_date_time_are_not_null = Q(
        actual_checkin_date_time__isnull=False
    ) & Q(actual_checkout_date_time__isnull=False)

    query_filter = Q()
    if room_ids:
        query_filter = Q(room_id__in=room_ids)

    if room_type_id:
        query_filter = Q(room__room_type_id=room_type_id)

    if room_type_name:
        query_filter = Q(room__room_type__type_name=room_type_name)

    room_booking_exist = RoomBooking.objects.filter(
        query_filter
        & Q(
            Q(
                actual_checkin_and_out_date_time_are_null
                & Q(
                    Q(booking__booking_status__is_latest=True)
                    & Q(
                        Q(
                            booking__booking_status__check_in_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                        | Q(
                            booking__booking_status__max_check_out_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                        | Q(
                            Q(
                                booking__booking_status__check_in_datetime__lte=start_date
                            )
                            & Q(
                                booking__booking_status__max_check_out_datetime__gte=end_date
                            )
                        )
                        | Q(
                            Q(
                                booking__booking_status__check_in_datetime__gte=start_date
                            )
                            & Q(
                                booking__booking_status__max_check_out_datetime__lte=end_date
                            )
                        )
                    )
                )
            )
            | Q(
                actual_checkin_and_out_date_time_are_not_null
                & Q(
                    Q(actual_checkin_date_time__range=(start_date, end_date))
                    | Q(actual_checkout_date_time__range=(start_date, end_date))
                    | Q(
                        Q(actual_checkin_date_time__lte=start_date)
                        & Q(actual_checkout_date_time__gte=end_date)
                    )
                    | Q(
                        Q(actual_checkin_date_time__gte=start_date)
                        & Q(actual_checkout_date_time__lte=end_date)
                    )
                )
            )
            | Q(
                only_actual_checkout_date_time_is_null
                & Q(
                    Q(booking__booking_status__is_latest=True)
                    & Q(
                        Q(actual_checkin_date_time__range=(start_date, end_date))
                        | Q(
                            booking__booking_status__max_check_out_datetime__range=(
                                start_date,
                                end_date,
                            )
                        )
                        | Q(
                            Q(actual_checkin_date_time__lte=start_date)
                            & Q(
                                booking__booking_status__max_check_out_datetime__gte=end_date
                            )
                        )
                        | Q(
                            Q(actual_checkin_date_time__gte=start_date)
                            & Q(
                                booking__booking_status__max_check_out_datetime__lte=end_date
                            )
                        )
                    )
                )
            )
        )
        # ).exclude(
        #     Q(booking__booking_status__is_latest = True) &
        #     Q(
        #         Q(booking__booking_status__booking_status = "Cancelled") |
        #         Q(booking__booking_status__booking_status = "No Show")
        #     )
        # )
    ).prefetch_related(
        Prefetch(
            "booking__booking_status",
            queryset=BookingStatus.objects.filter(is_latest=True),
        )
    )

    if current_booking_id:
        room_booking_exist = room_booking_exist.exclude(booking_id=current_booking_id)

    if exclude_reservation:
        room_booking_exist = room_booking_exist.exclude(status="Reservation")

    room_booking_exist_filtered = []
    for data in room_booking_exist:
        latest_booking_status = data.booking.booking_status.first().booking_status

        if (
            latest_booking_status == "No Show"
            or latest_booking_status == "Cancelled"
            or latest_booking_status == "Check Out"
            or "Transfer to" in latest_booking_status
        ):
            continue
        room_booking_exist_filtered.append(data)

    return room_booking_exist_filtered


def person_in_charge_exists(temp_array, person_in_charge_id, person_in_charge_name):
    for entry in temp_array:
        if (
            entry["person_in_charge"]["person_in_charge_id"] == person_in_charge_id
            and entry["person_in_charge"]["person_in_charge_name"]
            == person_in_charge_name
        ):
            return True
    return False
