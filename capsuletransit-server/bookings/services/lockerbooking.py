import json
from transaction.services.transaction import create_new_pending_transaction
from bookings.utils import calculate_rate_sum
from django.db.models import F, Max, Q
from datetime import datetime, timedelta
from django.utils.timezone import datetime as dt

from bookings.models import *

from bookings.serializer import (
    LockerBookingCreateSerializer,
    ExtendLockerSerializer,
    RebindLockerSerializer,
    TransferLockerSerializer,
)
from bookings import exceptions

from lockers.models import LOCKERSTATUS, LockerRate

from transaction.models import Transaction

from constant.enums import TransactionItemCategory

"""
    need refactor (signals)
"""


def create_locker_booking(data: LockerBookingCreateSerializer, user):
    validated_data = data.validated_data

    # LockerBooking query to get the latest booking record of requested locker
    locker_id_list = [item["locker_id"] for item in validated_data["lockers"]]

    lockers = Locker.objects.select_for_update().filter(locker_id__in=locker_id_list)

    for item in lockers:
        if not is_locker_booking_availabe(
            locker_id=item.pk,
            from_date=validated_data["start_rent"],
            to_date=validated_data["end_rent"],
        ):
            raise exceptions.LockerBooked(f"Locker {item.code} is already booked")
        else:
            item.status = LOCKERSTATUS.OCCUPIED
            item.save()

    # DEPRECATED: Locker Rates are now taken from the Merch Table
    # locker_rate = LockerRate.objects.filter(is_latest=True).order_by("hours_of_usage")
    locker_rate = Merch.objects.filter(lot=user.lot_id, item_details="Locker")

    room_bookings = RoomBooking.objects.filter(
        booking_id=validated_data["booking_id"]
    ).aggregate(max_pax_sum=Sum("room__room_type__max_pax"))

    booked_locker_bookings = LockerBooking.objects.filter(
        booking_id=validated_data["booking_id"]
    )

    locker_booking_list = []
    transaction_details = []
    total_sum = 0
    for locker in validated_data["lockers"]:

        sum = calculate_rate_sum(
            start_date=validated_data["start_rent"],
            end_date=validated_data["end_rent"],
            locker_rate=locker_rate,
        )

        # Check if total locker booking and current appended locker is less than room booked, then the locker is free
        if (booked_locker_bookings.count() + len(locker_booking_list)) < room_bookings[
            "max_pax_sum"
        ]:
            sum = 0

        if sum != 0:
            locker_obj: Locker = Locker.objects.get(pk=locker["locker_id"])
            transaction_details.append(
                {
                    "item_id": str(locker["locker_id"]),
                    "item_name": locker_obj.code,
                    "item_type": "Locker",
                    "quantity": 1,
                    "price": str(sum),
                    "category": TransactionItemCategory.LOCKER_SALES,
                }
            )

        total_sum += sum

        locker_booking_list.append(
            LockerBooking(
                booking_id=validated_data["booking_id"],
                locker_id=locker["locker_id"],
                sum=sum,
                status="Booked",
                locker_start_rent_datetime=validated_data["start_rent"],
                locker_end_rent_datetime=validated_data["end_rent"],
                room_code=locker["room_code"],
            )
        )
    created_locker_bookings = LockerBooking.objects.bulk_create(locker_booking_list)

    if len(locker_booking_list) > 0 and total_sum != 0:
        new_transaction = create_new_pending_transaction(
            booking_id=validated_data["booking_id"],
            transaction_details=transaction_details,
            sum=total_sum,
            account_id=user.account_id,
        )

    return created_locker_bookings


def extend_locker(data: ExtendLockerSerializer):
    validated_data = data.validated_data

    try:
        locker_booking_data = LockerBooking.objects.get(
            pk=validated_data["locker_booking_id"]
        )
    except:
        raise exceptions.LockerBookingNotExist("locker booking does not exist")

    locker = Locker.objects.select_for_update().get(
        locker_id=locker_booking_data.locker_id
    )

    if not is_locker_booking_availabe(
        locker_id=locker_booking_data.locker_id,
        from_date=locker_booking_data.locker_end_rent_datetime + timedelta(seconds=1),
        to_date=validated_data["new_end_rent"],
    ):
        raise exceptions.LockerBooked(f"Locker is already booked")

    locker.status = LOCKERSTATUS.RENTAL
    locker.save()

    locker_rate = LockerRate.objects.filter(is_latest=True).order_by("hours_of_usage")

    sum = calculate_rate_sum(
        start_date=locker_booking_data.locker_end_rent_datetime,
        end_date=validated_data["new_end_rent"],
        locker_rate=locker_rate,
    )

    created_locker_booking = LockerBooking.objects.create(
        booking_id=locker_booking_data.booking_id,
        locker_id=locker.pk,
        sum=sum,
        status="Rental",
        locker_start_rent_datetime=locker_booking_data.locker_end_rent_datetime,
        locker_end_rent_datetime=validated_data["new_end_rent"],
        room_code=locker_booking_data.room_code,
    )

    return created_locker_booking


def transfer_locker(data: TransferLockerSerializer):
    validated_data = data.validated_data

    try:
        locker_booking_data = LockerBooking.objects.get(
            pk=validated_data["locker_booking_id"]
        )
    except:
        raise exceptions.LockerBookingNotExist("locker booking does not exist")

    current_locker = Locker.objects.select_for_update().get(
        locker_id=locker_booking_data.locker_id
    )

    new_locker = Locker.objects.select_for_update().get(
        locker_id=validated_data["new_locker_id"]
    )

    if not is_locker_booking_availabe(
        locker_id=validated_data["new_locker_id"],
        from_date=locker_booking_data.locker_start_rent_datetime,
        to_date=locker_booking_data.locker_end_rent_datetime,
    ):
        raise exceptions.LockerBooked(f"Locker is already booked")

    current_locker.status = LOCKERSTATUS.AVAILABLE
    new_locker.status = LOCKERSTATUS.OCCUPIED
    locker_booking_data.locker_id = validated_data["new_locker_id"]
    current_locker.save()
    new_locker.save()

    transaction_list = Transaction.objects.filter(
        booking_id=locker_booking_data.booking.pk
    )

    for transaction in transaction_list:
        new_items = []
        for item in transaction.items:
            if str(item["item_name"]) == str(current_locker.code):
                item["item_name"] = str(new_locker.code)
                item["item_id"] = str(new_locker.locker_id)
            new_items.append(item)

        transaction.items = new_items
        transaction.payment_details = json.dumps(new_items)
        transaction.save()

    locker_booking_data.save()


def rebind_locker(data: RebindLockerSerializer):
    validated_data = data.validated_data

    try:
        locker_booking_data = LockerBooking.objects.get(
            pk=validated_data["locker_booking_id"]
        )
    except:
        raise exceptions.LockerBookingNotExist("locker booking does not exist")

    try:
        room_booking = RoomBooking.objects.get(
            booking_id=locker_booking_data.booking_id,
            room__room_code=validated_data["new_room_code"],
        )
    except:
        raise exceptions.RoomNotExist("Room does not exist in current booking")

    locker_booking_data.room_code = validated_data["new_room_code"]
    updated = locker_booking_data.save()

    return updated


def is_locker_booking_availabe(locker_id, from_date, to_date) -> bool:
    is_locker_bookings_exist = (
        (
            LockerBooking.objects.filter(
                Q(locker_id=locker_id)
                & Q(
                    Q(locker_start_rent_datetime__range=(from_date, to_date))
                    | Q(locker_end_rent_datetime__range=(from_date, to_date))
                    | Q(
                        Q(locker_start_rent_datetime__lte=from_date)
                        & Q(locker_end_rent_datetime__gte=to_date)
                    )
                )
            )
        )
        .exclude(status="Cancelled")
        .exists()
    )

    return not is_locker_bookings_exist
