
from bookings.models import Booking, BookingStatus, LockerBooking, RoomBooking, ShowerBooking
from django.db.models import Prefetch

from django.db.models import Q
from django.utils import timezone
from lot.utils import get_lot_settings_by_lot_id

from transaction.models import Transaction


def update_booking_status_no_show(accounts_lot_id):

    lot_settings = get_lot_settings_by_lot_id(lot_id=accounts_lot_id)

    no_show_interval_setting = [
        setting for setting in lot_settings \
        if setting.settings_name == "No Show Interval"\
        and setting.settings_category == "Room Settings"
    ]

    no_show_interval = int(no_show_interval_setting[0].settings_description)


    bookings_data = Booking.objects.select_for_update().filter(
        Q(booking_status__is_latest=True) &
        Q(
            Q(booking_status__booking_status="Confirm Booking")
        )
    ).prefetch_related(
        Prefetch(
            'booking_status',
            queryset=BookingStatus.objects.select_for_update().filter(
                Q(is_latest=True) &
                (
                    Q(booking_status="Confirm Booking") |
                    Q(booking_status="Booked") |
                    Q(booking_status="Transfer from Airside") |
                    Q(booking_status="Transfer from Landslide")
                )
            ),
            to_attr='latest_booking_status'
        )
    )


    # check only booking that have already paid can no show
    bookings = []
    for book in bookings_data:
        if book.platform.can_late_payment:
            bookings.append(book)
            continue

        room_booking_transactions = Transaction.objects.filter(
            is_room_booking = True,
            is_latest = True,
            transaction_status = "Paid"
        )

        if room_booking_transactions.exists():
            bookings.append(book)

    if len(bookings) == 0: return


    # time check
    current_time = timezone.now()
    status_id_need_to_update = []
    booking_id_need_to_update = []
    for book in bookings:
        if book.latest_booking_status[0].check_out_datetime > current_time: continue

        time_difference = current_time - book.latest_booking_status[0].check_out_datetime
        in_minutes = time_difference.total_seconds() / 60
        
        if in_minutes > no_show_interval:
            status_id_need_to_update.append(book.latest_booking_status[0].booking_status_id)
            booking_id_need_to_update.append(book.pk)

    if len(status_id_need_to_update) == 0: return


    # change booking status
    prev_booking_status = BookingStatus.objects.select_for_update().filter(booking_status_id__in = status_id_need_to_update)
    prev_booking_status.update(is_latest = False)

    new_booking_status_list = []
    for status in prev_booking_status:
        if status.is_latest and status.booking_status == "No Show":
            continue

        new_status = BookingStatus(
            booking_id=status.booking_id,
            booking_status="No Show",
            check_in_datetime=status.check_in_datetime,
            check_out_datetime=status.check_out_datetime,
            max_check_out_datetime=status.max_check_out_datetime,
            status_datetime=timezone.now(),
            is_latest=True,
            has_no_show = status.has_no_show
        )
        new_booking_status_list.append(new_status)

    BookingStatus.objects.bulk_create(new_booking_status_list)


    # change room booking status
    room_bookings = RoomBooking.objects.filter(booking_id__in=booking_id_need_to_update)
    for room_book in room_bookings:
        room_book.room.status = "vacant, cleaned"
        room_book.room.save()


    # change locker status
    locker_bookings = LockerBooking.objects.filter(booking_id__in=booking_id_need_to_update)
    for locker_book in locker_bookings:
        locker_book.locker.status = "Available"
        locker_book.locker.save()