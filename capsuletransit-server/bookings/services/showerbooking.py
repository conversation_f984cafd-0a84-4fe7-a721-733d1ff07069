from bookings.models import ShowerBooking

from bookings import exceptions

from bookings.serializer import  ShowerBookingCreateSerializer
from shower.models import Shower, ShowerRate
from bookings.utils import calculate_rate_sum

def register_shower_booking(data : ShowerBookingCreateSerializer):
    validated_data = data.validated_data

    showers = Shower.objects.select_for_update().filter(
        shower_id__in = validated_data['shower_ids']
    )
    
    if not showers.exists():
        raise exceptions.ShowerNotExist('shower does not exist')
    
    shower_rate = ShowerRate.objects.filter(
        is_latest = True
    ).order_by('hours_of_usage')
    
    shower_bookings = []
    for shower in showers:

        sum = calculate_rate_sum(
            start_date=validated_data['start_rent'],
            end_date=validated_data['end_rent'],
            shower_rate=shower_rate
        )

        shower_booking_available = ShowerBooking.objects.filter(
            pk = validated_data['booking_id']
        ).exists()

        if not shower_booking_available:
            sum = 0

        shower_bookings.append(
            ShowerBooking(
                booking_id = validated_data['booking_id'],
                shower_id = shower.pk,
                sum = sum,
                status = 'Check In',
                shower_start_rent_datetime = validated_data['start_rent'],
                shower_end_rent_datetime = validated_data['end_rent']
            )
        )

        shower.status = "Occupied"
        shower.save()
    
    result = ShowerBooking.objects.bulk_create(shower_bookings)
    return result
    