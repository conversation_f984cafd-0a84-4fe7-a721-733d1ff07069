from math import floor
import math
from threading import Thread

from django.utils.timezone import datetime

def annotate_available_point(bookings):

    #NOTE : TO OPTMIZE

    #Compleixty m * n 
    #where m is number of rooms 
    # n is number of booking periods

    # Available point 1 hour = 1/3 point 
    # every 3 hours = 1 point
    # 1 day = 8 point
    for booking in bookings:
        total = 8 * 2 # 48 worth of point
        for booking_period in booking["booking_periods"]:
            check_in_datetime = datetime.fromisoformat(booking_period["check_in_datetime"].replace("Z", "+00:00"))
            check_out_datetime = datetime.fromisoformat(booking_period["check_out_datetime"].replace("Z", "+00:00"))    


            duration = int( ( check_out_datetime -check_in_datetime).total_seconds()/3600)

            duration = duration if duration % 3 == 0 else duration + 1
            total_availability_used = math.ceil(duration / 3)
            total -= total_availability_used
            # total -= int(total_availability_used / 3 )

        #     print(total)
        # print("total", total)   
        booking['available_point'] = total
        
