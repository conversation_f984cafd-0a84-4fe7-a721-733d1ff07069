
from bookings.models import Booking, BookingStatus
from transaction.models import Transaction
from django.db.models import Q, Prefetch
from django.utils import timezone

from datetime import timedelta, datetime




def cancel_web_booking():
    

    web_booking = Booking.objects.select_for_update().filter(
        Q(booking_status__is_latest=True) &
        Q(booking_status__booking_status="Booked") &
        Q(platform__platform="Hotel Website")
    )

    current_time = timezone.now()

    booking_to_update = []
    for item in web_booking:
        duration_range = current_time - item.booking_made_datetime
        if duration_range > timedelta(minutes=15):
            booking_to_update.append(item)
    
    booking_ids = [booking.pk for booking in booking_to_update]

    # return if no bookings
    if len(booking_ids) == 0: return

    prev_booking_status = BookingStatus.objects.select_for_update().filter(booking_id__in = booking_ids, is_latest = True)
    
    new_booking_status = []
    for status in prev_booking_status:
        new_booking_status.append(
            BookingStatus(
                booking_id=status.booking_id,
                booking_status="Cancelled",
                check_in_datetime=status.check_in_datetime,
                check_out_datetime=status.check_out_datetime,
                max_check_out_datetime=status.max_check_out_datetime,
                status_datetime=timezone.now(),
                is_latest=True,
                has_no_show = status.has_no_show
            )
        )
    
    prev_booking_status.update(is_latest = False)

    Booking.objects.filter(pk__in=booking_ids).update(
        is_cancelled=True,
        details="Cancelled by System"
    )

    BookingStatus.objects.bulk_create(new_booking_status)

    transaction_to_void = Transaction.objects.filter(booking_id__in = booking_ids)
    transaction_to_void.update(transaction_status = "Void")

    