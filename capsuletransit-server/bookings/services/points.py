from bookings.models import BookingStatus, RoomBooking
from guests.models import Customer
from promotions.models import IndividualPromo, MemberPromolist


def add_point(booking_status_id):
    try:
        booking_status = BookingStatus.objects.get(booking_status_id=booking_status_id)

        customer = booking_status.booking.customer_staying
        if not customer.membership_no or customer.membership_no == "":
            return {"status":"failed","message":""}

        check_out_datetime = booking_status.check_out_datetime
        check_in_datetime = booking_status.check_in_datetime

        if not check_out_datetime or not check_in_datetime:
            return{
                "status":"failed",
                "message":'check in and check put datetime are required',
            }
        
        duration = check_out_datetime - check_in_datetime
        points = duration.total_seconds()//3600

        room_bookings_count = RoomBooking.objects.filter(
            booking_id = booking_status.booking.pk
        ).count()

        customer.point += (int(points) * room_bookings_count)
        customer.save()

        return{"status":"success","message":"Points added successfully", "data" : customer}
    except BookingStatus.DoesNotExist:
        return{
            "status":"failed",
            "message":"booking status not found"
        }
    except Exception as e:
        return{
            "status":"failed",
            "message":"server error",
            "errors":str(e)
        }


def auto_claim_milestone(customer : Customer):

    milestone_promo = IndividualPromo.objects.select_for_update().filter(
        is_milestone = True,
        archived = False
    ).first()

    if not milestone_promo:
        return
    
    if milestone_promo.seats == 0:
        return
    
    customer_milestone_promo = MemberPromolist.objects.filter(
        customer_id = customer.pk,
        individual_promo__is_milestone = True
    ).first()

    if customer_milestone_promo:
        return
    
    if customer.point >= milestone_promo.hours_to_hit:
        MemberPromolist.objects.create(
            individual_promo_id = milestone_promo.pk,
            customer_id = customer.pk
        )

        milestone_promo.deduct_seats(deduct_by=1)
