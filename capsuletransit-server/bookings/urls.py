from django.dispatch import receiver
from django.core.signals import request_finished
from django.db import connection, reset_queries
from rest_framework.routers import DefaultRouter
from django.urls import path

from .views import (
    BookingGuestPortofolioView,
    BookingRoomSessionLockView,
    BookingView,
    ExtraGuestViewSet,
    FeedbackViewset,
    PlatformView,
    BookingViewSet,
    BookingListView,
    BookingListViewV2,
    BookingLockerView,
    BookingRoomActionViewset,
    PlatformViewSet,
    RoomBookingViewSet,
    ShowerBookingViewSet,
    ExpressBookingGenerationViewset,
    ConsentFormViewSet,
    GetBookingPaymentInformationView,
    OtaReservation,
    POSBookingFilterViewSet,
    ScanCardAPIViewByBooking,
    ScanCardAPIViewByRoomBooking
)


router = DefaultRouter()
router.register("", BookingView, basename="bookings")
router.register("platforms", PlatformView, basename="platforms")
router.register("booking-viewset", BookingViewSet, basename="bookings-viewset")
router.register("pos-booking-filter", POSBookingFilterViewSet, basename='pos-booking-filter')
router.register("v1/locker", BookingLockerView, basename="booking-locker-viewset")
router.register("v1/bookingroom", BookingRoomActionViewset, basename="bookingroom")
router.register("v1/shower", ShowerBookingViewSet, basename="shower-booking-viewset")
router.register("v1/platforms", PlatformViewSet, basename="platform-viewset")
# router.register("details", DetailsView, basename="details")
router.register(
    "guest-portofolio", BookingGuestPortofolioView, basename="guest-portofolio"
)
router.register("extra-guest", ExtraGuestViewSet, basename="extra-guest")
router.register(
    "v1/express", ExpressBookingGenerationViewset, basename="express-bookings"
)
router.register("v1/consent", ConsentFormViewSet, basename="consent-form")
router.register("v1/ota", OtaReservation, basename="ota-reservation")
router.register("v1/room-booking", RoomBookingViewSet, basename="room-booking")
router.register("feedback", FeedbackViewset, basename="feedback")


urlpatterns = [
    # path(
    #     'guest-portofolio/<slug:booking_id>/', BookingGuestPortofolioView.as_view(), name='guest_portofolio',
    # ),
    path(
        "list/",
        BookingListView.as_view(),
        name="list",
    ),
    path(
        "list/v2/",
        BookingListViewV2.as_view(),
        name="list",
    ),
    path(
        "roomsessionlock/", BookingRoomSessionLockView.as_view(), name="roomsessionlock"
    ),
    path(
        "v1/get-payment-info/<slug:booking_id>/",
        GetBookingPaymentInformationView.as_view(),
    ),
    path("v1/scancardbybooking/", ScanCardAPIViewByBooking.as_view(), name="scancardbybooking"),
    path("v1/scancardbybooking/", ScanCardAPIViewByRoomBooking.as_view(), name="scancardbyroombooking"),
] + router.urls


# NOTE : This is for debugging purpose only
@receiver(request_finished)
def inspect_query(sender, **kwargs):
    # for query in connection.queries:
    #     print(query.get("time"))
    print(len(connection.queries))
    reset_queries()
