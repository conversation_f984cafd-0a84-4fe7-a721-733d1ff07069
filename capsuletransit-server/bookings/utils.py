import re
from datetime import datetime, timed<PERSON><PERSON>


def generate_booking_no(airport_code: str, current_counter: int):
    formatted_number = str(current_counter).zfill(6)

    return f"{airport_code}-{formatted_number}"


# TODO : REFACTOR CODE. ANTI PATTERN
def calculate_rate_sum(
    start_date, end_date, room_rate=None, locker_rate=None, shower_rate=None
):
    try:
        str_checkin_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%SZ")
        str_checkout_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%SZ")
    except:
        str_checkin_date = start_date
        str_checkout_date = end_date
    time_differenece = str_checkout_date - str_checkin_date
    hours_difference = time_differenece.total_seconds() / 3600

    if room_rate:
        for rate in room_rate.order_by("hours_of_stay"):
            half_hour = rate.hours_of_stay / 2
            if hours_difference <= half_hour:
                break
            if hours_difference <= rate.hours_of_stay:
                return rate.room_rate
        return 0

    if locker_rate:
        for rate in locker_rate.order_by("item_name"):
            numbers = re.findall(r"\d+", rate.item_name)
            if hours_difference <= int(numbers[0]):
                return rate.sum

    if shower_rate:
        for rate in shower_rate.order_by("hours_of_usage"):
            if hours_difference <= rate.hours_of_usage:
                return rate.shower_rate

    return 0


def calculate_total_booking_sum(start_date, end_date, room_and_rates):

    sum = 0
    for item in room_and_rates:
        sum += calculate_rate_sum(
            start_date=start_date, end_date=end_date, room_rate=item["rates"]
        )
    return sum


def calculate_max_checkout_datetime(check_out_datetime, grace_period_mins):
    time_difference = timedelta(minutes=grace_period_mins)
    return check_out_datetime + time_difference


# def check_changed_room_type(prev_room, new_room):
#     new_room_data = new_room
#     prev_room_data = prev_room

#     for room in new_room:
#         type_name = room.room_type.type_name

#         prev_room_i = next((index for index, room in enumerate(prev_room_data) if room.room_type.type_name == type_name), None)
#         new_room_i = next((index for index, room in enumerate(new_room_data) if room.room_type.type_name == type_name), None)

#         if prev_room_i != None:
#             prev_item_to_remove = prev_room_data[prev_room_i]
#             prev_room_data = prev_room_data.exclude(pk = prev_item_to_remove.pk)
#             new_item_to_remove = new_room_data[new_room_i]
#             new_room_data = new_room_data.exclude(pk = new_item_to_remove.pk)
