from django.db import models
import uuid

from transaction.models import Transaction
from cashierterminal.models import CashierTerminal
from lot.models import Lot



class PaymentTerminal(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    lot = models.ForeignKey(Lot, on_delete=models.CASCADE)
    terminal_id = models.CharField(max_length=8, unique=True)
    serial_number = models.CharField(max_length=10, unique=True)
    created_datetime = models.DateTimeField(auto_now_add=True)
    updated_datetime = models.DateTimeField(auto_now=True)
    payment_terminal_ip = models.GenericIPAddressField()
    archived = models.BooleanField(default=False)



class CashierPaymentTerminal(models.Model):
    payment_terminal = models.ForeignKey(PaymentTerminal, on_delete=models.CASCADE)
    cashier_terminal = models.ForeignKey(CashierTerminal, on_delete=models.CASCADE, null=True)
    created_datetime = models.DateTimeField(auto_now_add=True)
    updated_datetime = models.DateTimeField(auto_now=True)




class PaymentTerminalCommand(models.Model):
    cashier_number = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_processed = models.BooleanField(default=False)
    status = models.CharField(max_length=100)
    transaction = models.ForeignKey(Transaction, models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    serial_number = models.CharField(max_length=100)
    payment_terminal_ip = models.GenericIPAddressField()
