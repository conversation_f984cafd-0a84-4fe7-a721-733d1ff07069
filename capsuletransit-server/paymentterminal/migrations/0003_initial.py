# Generated by Django 4.2.3 on 2024-03-26 06:07

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('lot', '0005_lot_full_name'),
        ('transaction', '0005_transaction_guest_change_transaction_guest_given'),
        ('paymentterminal', '0002_delete_paymentterminal'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentTerminalCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cashier_number', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('status', models.CharField(max_length=100)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('serial_number', models.CharField(max_length=100)),
                ('payment_terminal_ip', models.GenericIPAddressField()),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='transaction.transaction')),
            ],
        ),
        migrations.CreateModel(
            name='PaymentTerminal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('terminal_id', models.CharField(max_length=8, unique=True)),
                ('serial_number', models.CharField(max_length=10, unique=True)),
                ('created_datetime', models.DateTimeField(auto_now_add=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True)),
                ('payment_terminal_ip', models.GenericIPAddressField()),
                ('lot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lot.lot')),
            ],
        ),
        migrations.CreateModel(
            name='CashierPaymentTerminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cashier_number', models.CharField(max_length=100)),
                ('created_datetime', models.DateTimeField(auto_now_add=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True)),
                ('payment_terminal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='paymentterminal.paymentterminal')),
            ],
        ),
    ]
