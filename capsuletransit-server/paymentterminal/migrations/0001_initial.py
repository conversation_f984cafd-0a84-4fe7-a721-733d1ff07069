# Generated by Django 4.2.3 on 2024-03-20 03:11

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PaymentTerminal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("pc_ip", models.GenericIPAddressField()),
                ("terminal_ip", models.GenericIPAddressField()),
            ],
            options={
                "unique_together": {("pc_ip", "terminal_ip")},
            },
        ),
    ]
