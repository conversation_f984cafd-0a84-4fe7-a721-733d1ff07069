from django.shortcuts import render
from rest_framework.decorators import action
from rest_framework import generics, status, viewsets
from rest_framework.response import Response
from django.db.models import Q
from django.db import transaction
from django.utils import timezone

from djangorestframework_camel_case.parser import Camel<PERSON>ase<PERSON><PERSON>NParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated

from paymentterminal.models import PaymentTerminal, PaymentTerminalCommand, CashierPaymentTerminal
from paymentterminal.serializers import AssignCashierSerializer, DefaultPaymentTerminalSerializer, PaymentTerminalAccessSerializer, PaymentTerminalGetCommandSerializer, PaymentTerminalSendResponseSerializer, WritePaymentTerminalSerializer, PaymentTerminalSerializer, ArchivePaymentTerminalSerializer
from transaction.models import Transaction
from cashierterminal.models import CashierTerminal

from paymentterminal import exceptions



class PaymentTerminalViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = PaymentTerminal.objects.all()
        return qs


    def list(self, request):
        
        try:
            qs = self.get_queryset()
            qs = qs.filter(lot_id=request.user.lot_id)
            return_serializer = PaymentTerminalSerializer(qs, many=True)
            return Response({
                "status" : "success",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    
    def create(self, request):
        serializer = WritePaymentTerminalSerializer(data=request.data)
        
        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            with transaction.atomic():
                payment_terminal_exist = PaymentTerminal.objects.filter(
                    Q(serial_number=validated_data['serial_number']) |
                    Q(payment_terminal_ip=validated_data['payment_terminal_ip']) |
                    Q(terminal_id=validated_data['terminal_id'])
                ).exists()

                if payment_terminal_exist:
                    return Response({
                        "status" : "failed",
                        "message": "payment terminal data must be unique"
                    }, status=status.HTTP_403_FORBIDDEN)


                PaymentTerminal.objects.create(
                    lot_id = request.user.lot_id,
                    terminal_id = validated_data['terminal_id'],
                    serial_number = validated_data['serial_number'],
                    payment_terminal_ip = validated_data['payment_terminal_ip']
                )

            return Response({
                "status" : "success",
                "message" : "payment terminal created"
            }, status=status.HTTP_201_CREATED)
        
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    @action(
        detail=False,
        methods=["GET"],
        url_path="get/(?P<payment_terminal_id>[0-9a-f-]+)",
        url_name="get",
    )
    def get_single_payment_terminal(self, request, payment_terminal_id):
        try:
            try:
                payment_terminal = PaymentTerminal.objects.get(pk = payment_terminal_id)
            except:
                return Response({
                    "status" : "failed",
                    "message": "payment terminal not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            return_serializer = PaymentTerminalSerializer(payment_terminal)

            return Response({
                "status" : "success",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    
    @action(
        detail=False,
        methods=["PATCH"],
        url_path="edit",
        url_name="edit",
    )
    def edit_payment_terminal(self, request):
        serializer = WritePaymentTerminalSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            with transaction.atomic():

                try:
                    payment_terminal = PaymentTerminal.objects.get(pk = validated_data['payment_terminal_id'])
                except:
                    return Response({
                        "status" : "failed",
                        "message": "payment terminal not found"
                    }, status=status.HTTP_404_NOT_FOUND)
                    
                payment_terminal_exist = PaymentTerminal.objects.filter(
                    Q(serial_number=validated_data['serial_number']) |
                    Q(payment_terminal_ip=validated_data['payment_terminal_ip']) |
                    Q(terminal_id=validated_data['terminal_id'])
                ).exclude(pk = payment_terminal.pk).exists()

                if payment_terminal_exist:
                    return Response({
                        "status" : "failed",
                        "message": "payment terminal data must be unique"
                    }, status=status.HTTP_403_FORBIDDEN)
                
                payment_terminal.terminal_id = validated_data['terminal_id']
                payment_terminal.serial_number = validated_data['serial_number']
                payment_terminal.payment_terminal_ip = validated_data['payment_terminal_ip']
                payment_terminal.save()

            return_serializer = PaymentTerminalSerializer(payment_terminal)

            return Response({
                "status" : "success",
                "message" : "payment terminal edited",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    @action(
        detail=False,
        methods=["PUT"],
        url_path="archive",
        url_name="archive",
    )
    def archive_payment_terminal(self, request):
        serializer = ArchivePaymentTerminalSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            with transaction.atomic():
                try:
                    payment_terminal = PaymentTerminal.objects.get(pk = validated_data['payment_terminal_id'])
                except:
                    return Response({
                        "status" : "failed",
                        "message": "payment terminal not found"
                    }, status=status.HTTP_404_NOT_FOUND)
                
                payment_terminal.archived = validated_data['archive']
                payment_terminal.save()
                

                """
                    if its archiving process,
                    need to remove default cashier's payment terminal
                    from CashierPaymentTerminal
                """
                if validated_data['archive']:
                    cashier_terminal = CashierPaymentTerminal.objects.filter(
                        payment_terminal_id = payment_terminal.pk
                    )
                    cashier_terminal.delete()

            return_serializer = PaymentTerminalSerializer(payment_terminal)

            return Response({
                "status" : "success",
                "message" : f"payment terminal {'archived' if validated_data['archive'] else 'unarchived'}",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        

    # assign payment terminal to the cashier (as their default payment terminal)
    @action(
        detail=False,
        methods=["POST"],
        url_path="assign-cashier",
        url_name="assign-cashier",
    )
    def assign_cashier_to_payment_terminal(self, request):
        serializer = AssignCashierSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            
            with transaction.atomic():

                try:
                    cashier_terminal = CashierTerminal.objects.get(pk = validated_data['cashier_terminal_id'])
                except:
                    return Response({
                        "status" : "failed",
                        "message" : "cashier terminal not found"
                    }, status=status.HTTP_204_NO_CONTENT)

                default_terminal_exist = CashierPaymentTerminal.objects.filter(
                    cashier_terminal_id = cashier_terminal.pk
                )

                if default_terminal_exist.exists():
                    return Response({
                        "status" : "failed",
                        "message" : f"{cashier_terminal.cashier_terminal} already assigned into terminal with id {default_terminal_exist.first().payment_terminal.terminal_id}"
                    }, status=status.HTTP_403_FORBIDDEN)
            
            CashierPaymentTerminal.objects.create(
                payment_terminal_id = validated_data['payment_terminal_id'],
                cashier_terminal_id = cashier_terminal.pk
            )
            
            return Response({
                "status" : "success",
                "message" : "assigned"
            }, status=status.HTTP_201_CREATED)


        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    @action(
        detail=False,
        methods=["PUT"],
        url_path="unassign-cashier",
        url_name="unassign-cashier",
    )
    def unassign_cashier_from_payment_terminal(self, request):
        serializer = AssignCashierSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            try:
                    cashier_terminal = CashierTerminal.objects.get(pk = validated_data['cashier_terminal_id'])
            except:
                return Response({
                    "status" : "failed",
                    "message" : "cashier terminal not found"
                }, status=status.HTTP_204_NO_CONTENT)

            try:
                cashier_payment_terminal = CashierPaymentTerminal.objects.get(
                    payment_terminal_id = validated_data['payment_terminal_id'],
                    cashier_terminal_id = cashier_terminal.pk
                )
            except:
                return Response({
                    "status" : "failed",
                    "message": "selected cashier not assigned into the this payment terminal"
                }, status=status.HTTP_404_NOT_FOUND)

            cashier_payment_terminal.delete()

            return Response({
                "status" : "success",
                "message" : "unassigned"
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    # to get payment terminal assigned into specific cashier
    @action(
        detail=False,
        methods=["GET"],
        url_path="default-payment-terminal",
        url_name="default-payment-terminal",
    )
    def get_default_payment_terminal(self, request):
        cashier_number = request.GET.get("cashierNumber", None)
        
        try:
            qs = self.get_queryset()
            qs = qs.filter(lot_id=request.user.lot_id)

            if cashier_number:
                qs = qs.filter(
                    cashierpaymentterminal__cashier_terminal__cashier_terminal=cashier_number
                )
            
            return_serializer = DefaultPaymentTerminalSerializer(qs, many=True)
            return Response({
                "status" : "success",
                "data" : return_serializer.data
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        
    
    # for active poll from FE
    @action(
        detail=False,
        methods=["GET"],
        url_path="get-result/(?P<transaction_id>[0-9a-f-]+)",
        url_name="get-result",
    )
    def get_result(self, request, transaction_id):
        try:
            
            try:
                command_obj = PaymentTerminalCommand.objects.get(transaction_id = transaction_id)
            except:
                return Response({
                    "status" : "failed",
                    "message" : "command not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            
            if command_obj.status == "Created" or\
                command_obj.is_processed:
                return Response({
                    "status" : "success",
                    "message" : "on process"
                }, status=status.HTTP_204_NO_CONTENT)
            
            return Response({
                "status" : "success",
                "message" : "retrived",
                "data" : {
                    "status" : command_obj.status
                }
            }, status=status.HTTP_200_OK)
                

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


    # FE request to access payment terminal
    @action(
        detail=False,
        methods=["POST"],
        url_path="create-command",
        url_name="create-command",
    )
    def create_command(self, request):
        serializer = PaymentTerminalAccessSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data

            with transaction.atomic():

                try:
                    transaction_obj = Transaction.objects.get(pk = validated_data['transaction_id'])
                except:
                    raise exceptions.NotFound("transaction not found")
                
                try:
                    payment_terminal = PaymentTerminal.objects.get(serial_number = validated_data['payment_terminal_serial_number'])
                except:
                    raise exceptions.NotFound("payment terminal not found")
                
                processed_command_exist = PaymentTerminalCommand.objects.filter(
                    Q(
                        Q(cashier_number = validated_data['cashier_number']) &
                        Q(
                            Q(status = "Created") |
                            Q(is_processed = True)
                        )
                    ) |
                    Q(
                        Q(serial_number = payment_terminal.serial_number) &
                        Q(
                            Q(status = "Created") |
                            Q(is_processed = True)
                        )
                    )
                ).exists()

                if processed_command_exist:
                    return Response({
                        "status" : "failed",
                        "message" : "there is an on going process for this cashier or selected payment terminal"
                    }, status=status.HTTP_403_FORBIDDEN)
                

                PaymentTerminalCommand.objects.create(
                    cashier_number = validated_data['cashier_number'],
                    status = "Created",
                    transaction_id = transaction_obj.pk,
                    amount = transaction_obj.credit_amount,
                    serial_number = payment_terminal.serial_number,
                    payment_terminal_ip = payment_terminal.payment_terminal_ip
                )
            
            return Response({
                "status" : "success",
                "message" : "command created"
            }, status=status.HTTP_201_CREATED)
        
        except exceptions.NotFound as e:
            return Response({
                "status" : "failed",
                "message": str(e)
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


    # for active poll from mid app
    @action(
        detail=False,
        methods=["GET"],
        url_path="get-command",
        url_name="get-command",
        permission_classes = [],
        authentication_classes = []
    )
    def get_command(self, request):

        cashier_number = request.GET.get("cashierNumber", 0)
        
        try:
            with transaction.atomic():
                command_obj = PaymentTerminalCommand.objects.select_for_update().filter(
                    cashier_number = cashier_number,
                    status = "Created"
                ).first()

                if command_obj:
                    command_obj.updated_at = timezone.now()
                    command_obj.is_processed = True
                    command_obj.status = "On Process"
                    command_obj.save()

                    return_serializer = PaymentTerminalGetCommandSerializer(command_obj)

                    return Response({
                        "status" : "success",
                        "message" : "retrived",
                        "data" : return_serializer.data
                    }, status=status.HTTP_200_OK)
                
                else:
                    return Response({
                        "status" : "failed",
                        "message" : "no command"
                    }, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    

    # for mid app send back response
    @action(
        detail=False,
        methods=["POST"],
        url_path="response",
        url_name="response",
        permission_classes = [],
        authentication_classes = []
    )
    def send_response(self, request):
        serializer = PaymentTerminalSendResponseSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response({
                    "status": "failed",
                    "message": serializer.errors,
                }, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data

            with transaction.atomic():
                command_obj = PaymentTerminalCommand.objects.select_for_update().filter(
                    cashier_number = validated_data['cashier_number'],
                    is_processed = True
                ).first()

                if not command_obj:
                    return Response({
                        "status" : "failed",
                        "message" : "no processed command found"
                    }, status=status.HTTP_404_NOT_FOUND)

                if validated_data['status'] == "Success":
                    transaction_obj = command_obj.transaction

                    transaction_obj.pan = validated_data['pan']
                    transaction_obj.payment_reference = validated_data['payment_reference']
                    transaction_obj.debit_amount = transaction_obj.credit_amount
                    transaction_obj.credit_amount = 0
                    transaction_obj.transaction_status = "Paid"
                    transaction_obj.save()
                
                command_obj.updated_at = timezone.now()
                command_obj.is_processed = False
                command_obj.status = validated_data['status']
                command_obj.save()
            
            return Response({
                "status" : "success",
                "message" : "received"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    @action(
        detail=False,
        methods=["PATCH"],
        url_path="stop-command/(?P<transaction_id>[0-9a-f-]+)",
        url_name="stop-command",
        permission_classes = [],
        authentication_classes = []
    )
    def stop_command(self, request, transaction_id):
        
        try:
            
            command_obj = PaymentTerminalCommand.objects.filter(
                transaction_id = transaction_id,
                status__in = ["Created", "On Process"]
            )

            if not command_obj.exists():
                return Response({
                    "status" : "failed",
                    "message" : "command not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            command_obj = command_obj.first()
            command_obj.status = "Failed"
            command_obj.is_processed = False
            command_obj.save()

            return Response({
                "status" : "success",
                "message" : "integration canceled"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )