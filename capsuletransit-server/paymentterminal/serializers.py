from rest_framework import serializers

from paymentterminal.models import PaymentTerminal, CashierPaymentTerminal
from cashierterminal.serializers import CashierTerminalSerializer


class CashierPaymentTerminalSerializer(serializers.ModelSerializer):
    cashier_terminal = CashierTerminalSerializer()
    class Meta:
        model = CashierPaymentTerminal
        fields = "__all__"

class PaymentTerminalSerializer(serializers.ModelSerializer):
    cashier_payment_terminals = CashierPaymentTerminalSerializer(
        source='cashierpaymentterminal_set',
        required=False,
        read_only=True,
        many=True
    )
    class Meta:
        model = PaymentTerminal
        fields = "__all__"

class DefaultPaymentTerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentTerminal
        fields = "__all__"


class WritePaymentTerminalSerializer(serializers.Serializer):
    payment_terminal_id = serializers.UUIDField(required=False)
    terminal_id = serializers.CharField(max_length=8)
    serial_number = serializers.CharField(max_length=10)
    payment_terminal_ip = serializers.IPAddressField()


class ArchivePaymentTerminalSerializer(serializers.Serializer):
    payment_terminal_id = serializers.UUIDField()
    archive = serializers.BooleanField()


class AssignCashierSerializer(serializers.Serializer):
    payment_terminal_id = serializers.UUIDField(required=False)
    cashier_terminal_id = serializers.IntegerField()



class PaymentTerminalAccessSerializer(serializers.Serializer):
    cashier_number = serializers.CharField()
    transaction_id = serializers.UUIDField()
    payment_terminal_serial_number = serializers.CharField()


class PaymentTerminalGetCommandSerializer(serializers.Serializer):
    cashier_number = serializers.CharField()
    amount = serializers.DecimalField(decimal_places=2, max_digits=10)
    serial_number = serializers.CharField()
    payment_terminal_ip = serializers.IPAddressField()


class PaymentTerminalSendResponseSerializer(serializers.Serializer):
    cashier_number = serializers.CharField()
    pan = serializers.CharField(max_length=20, allow_blank=True, allow_null = True)
    payment_reference = serializers.CharField(max_length=20, allow_blank=True, allow_null = True)
    status = serializers.CharField()# should be either Success, or Failed