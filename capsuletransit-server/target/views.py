from django.shortcuts import render
from rest_framework import viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend
from .models import Target
from .serializers import TargetSerializer

# Create your views here.

class TargetViewSet(viewsets.ModelViewSet):
    queryset = Target.objects.all()
    serializer_class = TargetSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['type', 'lot_id']
    search_fields = ['name']

    def get_queryset(self):
        queryset = Target.objects.all()
        start_time = self.request.query_params.get('start_time', None)
        end_time = self.request.query_params.get('end_time', None)

        if start_time and end_time:
            # Return targets where:
            # - target's start_time is before or equal to query's end_time AND
            # - target's end_time is after or equal to query's start_time
            queryset = queryset.filter(
                start_time__lte=end_time,
                end_time__gte=start_time
            )
        elif start_time:
            queryset = queryset.filter(end_time__gte=start_time)
        elif end_time:
            queryset = queryset.filter(start_time__lte=end_time)

        return queryset
