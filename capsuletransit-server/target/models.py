from django.db import models
import uuid

# Create your models here.

class Target(models.Model):
    reference_id = models.UUIDField(editable=True)
    lot_id = models.IntegerField()
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=100)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    target_value = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'targets'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.type})"
