from .models import Locke<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers


class LockerZoneSerializer(serializers.ModelSerializer):
    locker_count = serializers.IntegerField(required=False)
    class Meta:
        ref_name = "LockerZoneSerializer"
        model = LockerZone
        fields = "__all__"


class LockerSerializer(serializers.ModelSerializer):
    zone = LockerZoneSerializer()
    class Meta:
        ref_name  = "LockerSerializer"
        model = Locker
        fields = "__all__"



class WriteLockerSerializer(serializers.Serializer):
    qr_code = serializers.CharField()
    locker_code = serializers.CharField()
    zone_id = serializers.IntegerField()
    remarks = serializers.CharField(allow_blank=True)
    up_down = serializers.BooleanField()


class LockerRateSerializer(serializers.ModelSerializer):
    
     class Meta:
        model = LockerRate
        fields = "__all__"


class ArchiveLockerRateSerializer(serializers.Serializer):
    locker_rate_id = serializers.UUIDField()
    is_latest = serializers.<PERSON><PERSON>an<PERSON><PERSON>()

class ArchiveLockerSerializer(serializers.Serializer):
    locker_id = serializers.UUIDField()
    archive = serializers.BooleanField()



class CreateLockerZoneSerializer(serializers.Serializer):
    name = serializers.CharField()

class EditLockerZoneSerializer(serializers.Serializer):
    name = serializers.CharField()
    is_archive = serializers.BooleanField()


class ChangeLockerStatusSerializer(serializers.Serializer):
    locker_id = serializers.UUIDField()
    new_status = serializers.CharField()