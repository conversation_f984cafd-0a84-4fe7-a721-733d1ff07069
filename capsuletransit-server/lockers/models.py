import datetime
from django.db import models
from lot.models import Lot
from uuid import uuid4


class LockerZone(models.Model):
    zone_id = models.AutoField(primary_key=True, editable=False, unique=True)
    name = models.CharField(max_length=30)
    details = models.CharField(max_length=50, null=True, blank=True)
    is_archive = models.BooleanField(default=False)
    lot = models.ForeignKey(
        Lot,
        verbose_name="Locker Zone Lot",
        on_delete=models.RESTRICT,
        related_name="LockerZone",
    )

    def __str__(self):
        return self.name

    def get_all_locker(self):
        lockers = Locker.objects.filter(zone_id=self.zone_id)
        return lockers


LOCKER_STATUS = (
    ("Available", "Available"),
    ("Occupied", "Occupied"),
    ("Rental", "Rental"),
    ("Maintenance", "Maintenance"),
)


class LOCKERSTATUS:
    AVAILABLE = "Available"
    OCCUPIED = "Occupied"
    RENTAL = "Rental"
    MAINTENANCE = "Maintenance"
    choices = (
        ("Available", "Available"),
        ("Occupied", "Occupied"),
        ("Rental", "Rental"),
        ("Maintenance", "Maintenance"),
    )


class Locker(models.Model):
    locker_id = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        unique=True,
        verbose_name="Locker ID",
    )
    zone = models.ForeignKey(
        LockerZone, on_delete=models.CASCADE, verbose_name=("Locker Zone")
    )
    code = models.CharField(max_length=20)
    details = models.CharField(max_length=50)
    status = models.CharField(choices=LOCKER_STATUS, default="Available", max_length=15)
    is_archive = models.BooleanField(default=False)
    up_down = models.BooleanField(default=False, help_text="True = UP, Flase = DOWN")

    def __str__(self):
        return self.code

    @property
    def is_available(self):
        return self.status == "Available"


class LockerRate(models.Model):
    locker_rate_id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, unique=True
    )
    tiers = models.IntegerField(null=False, default=0)
    hours_of_usage = models.IntegerField(null=False)
    locker_rate = models.DecimalField(max_digits=100, decimal_places=2)
    lot = models.ForeignKey(Lot, on_delete=models.RESTRICT, null=True)
    is_latest = models.BooleanField(default=True)
