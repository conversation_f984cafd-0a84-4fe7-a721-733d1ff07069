import datetime
import uuid
from django.test import TestCase
from lockers.serializer import LockerBookingSerializer
# Create your tests here.


def test_register_serializer():
    start_rent = datetime.datetime.now()
    end_rent =   start_rent + datetime.timedelta(days=1)    
    booking_id = uuid.uuid4()
    lockers = [
        {
            "locker_id": uuid.uuid4(),
            "room_code": "A"
        },
        {
            "locker_id": uuid.uuid4(),
            "room_code": "B"
        }
    ]

    data = {
        "start_rent": start_rent,
        "end_rent": end_rent,
        "booking_id": booking_id,
        "lockers": lockers
    }

    serializer = LockerBookingSerializer(data=data)
    valid = serializer.is_valid(raise_exception=True)
    assert valid == True
