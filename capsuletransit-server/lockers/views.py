from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.db.models import Count, Q

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response


from .serializer import (
    Archive<PERSON>ockerRateSerializer,
    ArchiveLockerSerializer,
    ChangeLockerStatusSerializer,
    CreateLockerZoneSerializer,
    EditLockerZoneSerializer,
    WriteLockerSerializer,
    LockerRateSerializer,
    LockerSerializer,
    LockerZoneSerializer,
)
from .models import LOCKERSTATUS, Locker, LockerRate, LockerZone
from lockers.services.lockerzone import get_specific_locker_zone, get_locker_stats
from lockers.services.locker import get_specific_locker, get_available_locker

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON>ase<PERSON><PERSON><PERSON><PERSON><PERSON>

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

# Permissions
from accounts import permissions


# Create your views here.
class Lockers(viewsets.ViewSet):
    permission_classes = []

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-all-lockers",
        url_name="get-all-lockers",
        permission_classes=[IsAuthenticated],
        authentication_classes=[JWTAuthentication],
    )
    def get_all_lockers(self, request):
        lot = request.user.lot_id

        data = (
            Locker.objects.prefetch_related("zone")
            .filter(is_archive=False)
            .order_by("code")
        )
        data = data.filter(status="Available").filter(
            zone__is_archive=False, zone__lot=lot
        )
        serializer = LockerSerializer(data, many=True)
        try:
            return Response(
                {"status": "success", "msg": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-locker-by-zone",
        url_name="get-locker-by-zone",
        permission_classes=[],
    )
    def get_lockers_by_zone(self, request):
        zone_id = request.query_params.get("zone_id", None)
        data = Locker.objects.filter(is_archive=False, zone_id=zone_id).order_by("code")
        zone_name = LockerZone.objects.filter(zone_id=zone_id)[0]
        serializer = LockerSerializer(data, many=True)
        try:
            return Response(
                {
                    "status": "success",
                    "msg": "success",
                    "data": {"name": zone_name.name, "lockers": serializer.data},
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-locker",
        url_name="create-locker",
        permission_classes=[],
    )
    def register_locker(self, request):
        data = request.data
        try:
            with transaction.atomic():
                serializer = LockerSerializer(data=data)
                if serializer.is_valid():
                    # Save the data
                    serializer.save()
                    return Response(
                        {"status": "success", "msg": "success"},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "status": "failed",
                            "msg": "invalid data",
                            "errors": serializer.errors,
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["PUT"],
        detail=True,
        url_path="update-locker",
        url_name="update-locker",
        permission_classes=[],
    )
    def update_locker(self, request, pk=None):
        data = request.data
        try:
            with transaction.atomic():
                instance = get_specific_locker(pk)

                serializer = LockerSerializer(instance, data=data, partial=True)
                if serializer.is_valid():
                    # Save the updated data
                    serializer.save()
                    return Response(
                        {"status": "success", "msg": "success"},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "status": "failed",
                            "msg": "invalid data",
                            "errors": serializer.errors,
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        except Locker.DoesNotExist:
            return Response(
                {
                    "status": "failed",
                    "msg": "Object not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            print(e)
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "lockerId",
                in_=openapi.IN_PATH,
                type=openapi.TYPE_STRING,
                description="locker Id",
            ),
        ],
        responses={"200": LockerSerializer, "404": "not found"},
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-locker/(?P<locker_id>[0-9a-f-]+)",
        permission_classes=[],
    )
    def get_locker_by_id(self, request, locker_id):
        try:
            qs = Locker.objects.get(pk=locker_id)
        except:
            return Response(
                {"status": "failed", "message": "locker not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            serializer = LockerSerializer(qs)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "amount",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="amount of available locker to generate",
            ),
            openapi.Parameter(
                "lockerLevel",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="value either upper or lower",
            ),
            openapi.Parameter(
                "status",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="filter by status id (multiple add ':' in between. example = <status>:<status>)",
            ),
        ],
        responses={"200": LockerSerializer, "500": "server error"},
    )
    def list(self, request):
        """
        List available lockers.

        This endpoint returns a list of available lockers based on the 'amount' query parameter.

        Parameters:
        - `amount` (integer): The number of lockers to retrieve.

        Returns:
        - 200 OK: A list of available lockers.
        - 400 Bad Request: If 'amount' is not a valid integer.
        """
        amount = request.query_params.get("amount", None)
        locker_level = request.query_params.get("lockerLevel", None)
        status = request.query_params.get("status", None)

        try:
            qs = Locker.objects.all().prefetch_related("zone")
            qs = qs.filter(zone__lot_id = request.user.lot_id)

            if amount:
                qs = qs.filter(status='Available')

                amount = int(amount)
                count = qs.count()
                
                if int(count) < int(amount):
                    qs = qs[:count]
                else:
                    qs = qs[:amount]

            if locker_level == "upper":
                qs = qs.filter(up_down=True)

            if locker_level == "lower":
                qs = qs.filter(up_down=False)

            if status:
                splited_status = status.split(":")
                qs = qs.filter(status__in=splited_status)

            serializer = LockerSerializer(qs, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteLockerSerializer,
        responses={
            "200": "success",
            "400": "bad request",
            "403": "forbidden",
            "500": "server error",
        },
    )
    def create(self, request):
        serializer = WriteLockerSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            locker_exist = Locker.objects.filter(pk=validated_data["qr_code"]).exists()
            if locker_exist:
                return Response(
                    {"status": "failed", "message": "qr code already used"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            Locker.objects.create(
                locker_id=validated_data["qr_code"],
                code=validated_data["locker_code"],
                zone_id=validated_data["zone_id"],
                details=validated_data["remarks"],
                up_down=validated_data["up_down"],
                is_archive=False,
                status="Available",
            )
            return Response(
                {"status": "success", "message": "new locker created"},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=WriteLockerSerializer,
        responses={"200": "success", "400": "bad request", "500": "server error"},
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="edit/(?P<locker_id>[0-9a-f-]+)",
        url_name="edit",
        permission_classes=[],
    )
    def edit_locker(self, request, locker_id=None):
        serializer = WriteLockerSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {
                        "status": "failed",
                        "message": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not locker_id:
                return Response(
                    {
                        "status": "failed",
                        "message": "please provide locker id",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            with transaction.atomic():
                locker = Locker.objects.get(pk=locker_id)
                if str(locker.pk) == validated_data["qr_code"]:
                    locker.code = validated_data["locker_code"]
                    locker.zone_id = validated_data["zone_id"]
                    locker.details = validated_data["remarks"]
                    locker.up_down = validated_data["up_down"]
                else:
                    Locker.objects.create(
                        locker_id=validated_data["qr_code"],
                        code=validated_data["locker_code"],
                        zone_id=validated_data["zone_id"],
                        details=validated_data["remarks"],
                        up_down=validated_data["up_down"],
                        is_archive=locker.is_archive,
                        status=locker.status,
                    )
                    locker.is_archive = True

                locker.save()

            return Response(
                {"status": "success", "message": "locker updated"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=ArchiveLockerSerializer,
        responses={200: "success", 500: "server error"},
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="archive",
        url_name="archive-locker",
        permission_classes=[],
    )
    def archive_locker(self, request):
        serializer = ArchiveLockerSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                locker = Locker.objects.get(pk=validated_data["locker_id"])
            except:
                return Response(
                    {"status": "failed", "message": "locker not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            locker.is_archive = validated_data["archive"]
            locker.save()

            if validated_data["archive"]:
                message = "Locker archived successfully"
            else:
                message = "Locker unarchived successfully"

            return Response(
                {"status": "success", "message": message},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "msg": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(responses={"200": "success", "500": "server error"})
    @action(
        methods=["GET"],
        detail=False,
        url_path="get-status-list",
        url_name="get-status-list",
        permission_classes=[],
    )
    def get_locker_status_list(self, request):
        try:
            locker_status = LOCKERSTATUS.choices

            locker_status_list = []
            for stat in locker_status:
                locker_status_list.append(stat[0])

            return Response(
                {"status": "success", "data": locker_status_list},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        
    @action(
        methods=["PATCH"],
        detail=False,
        url_path="edit-locker-status",
        url_name="edit-locker-status",
        permission_classes=[],
    )
    def edit_locker_status(self,request):
        data = request.data
        serializer = ChangeLockerStatusSerializer(data=data)
        LOCKERSTATUS = ["Available","Occupied","Rental","Maintenance"]

        try:

            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            
            validated_data = serializer.validated_data


            with transaction.atomic():
                try:
                    locker = Locker.objects.get(pk=validated_data['locker_id'])
                except:
                    return Response(
                        {"status":"failed","message":'Locker not found'},
                        status=status.HTTP_204_NO_CONTENT,
                    )
                
                locker.status = validated_data['new_status']
                if locker.status not in LOCKERSTATUS:
                    return Response(
                        {"status":"failed","message":"Invalid Locker Status"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                locker.save()
                
                return Response(
                    {"status": "success", "message": "Locker status updated successfully"},
                    status=status.HTTP_200_OK,
                )
                
        except Exception as e:
            print(e)
            return Response(
                {"status":"failed","message":"Sever Error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

class LockersZone(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        return LockerZone.objects.all()

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "withLockerCount",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="get the data with locker count",
            )
        ],
        responses={"200": LockerZoneSerializer, "500": "server error"},
    )
    def list(self, request):
        with_locker_count = request.GET.get("withLockerCount", None)

        lot = request.user.lot_id

        try:
            qs = self.get_queryset()

            qs = qs.filter(lot=lot)

            if with_locker_count:
                new_qs = []
                for locker in qs:
                    new_qs.append(
                        {
                            "zone_id": locker.pk,
                            "name": locker.name,
                            "details": locker.details,
                            "is_archive": locker.is_archive,
                            "lot": locker.lot,
                            "locker_count": locker.get_all_locker().count(),
                        }
                    )
                qs = new_qs

            serializer = LockerZoneSerializer(qs, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            "200": LockerZoneSerializer,
            "404": "not found",
            "500": "server, serror",
        }
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="details/(?P<locker_zone_id>[0-9a-f-]+)",
        url_name="details",
        permission_classes=[],
    )
    def get_details(self, request, locker_zone_id=None):
        try:
            try:
                locker_zone = LockerZone.objects.get(pk=locker_zone_id)
            except:
                raise Response(
                    {"status": "failed", "message": "zone not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = LockerZoneSerializer(locker_zone)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-locker-zone-details",
        url_name="get-locker-zone-details",
        permission_classes=[],
    )
    def get_locker_details(self, request):
        queryset = get_locker_stats()
        try:
            return Response(
                {"status": "success", "msg": "success", "data": queryset},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "msg": "server error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=CreateLockerZoneSerializer,
        responses={"200": "success", "400": "bad request", "500": "server error"},
    )
    def create(self, request):
        lot_id = request.user.lot_id
        serializer = CreateLockerZoneSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            new_zone = LockerZone.objects.create(
                name=validated_data["name"],
                details=validated_data["name"],
                is_archive=False,
                lot_id=lot_id,
            )

            return Response(
                {
                    "status": "success",
                    "message": "zone created",
                    "id": new_zone.zone_id,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=EditLockerZoneSerializer,
        responses={
            "200": "success",
            "400": "bad request",
            "404": "not found",
            "500": "server error",
        },
    )
    def update(self, request, pk=None):
        serializer = EditLockerZoneSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                locker_zone = LockerZone.objects.get(pk=pk)
            except:
                raise Response(
                    {"status": "failed", "message": "zone not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            validated_data = serializer.validated_data

            locker_zone.name = validated_data["name"]
            locker_zone.details = validated_data["name"]
            locker_zone.is_archive = validated_data["is_archive"]
            locker_zone.save()

            return Response(
                {"status": "success", "message": "zone edited"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def destroy(self, request, pk=None):
        try:
            try:
                locker_zone = LockerZone.objects.get(pk=pk)
            except:
                raise Response(
                    {"status": "failed", "message": "zone not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            locker_zone.delete()

            return Response(
                {"status": "success", "message": "zone deleted"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class LockerRateViewSet(viewsets.ViewSet):
    queryset = LockerRate.objects.all()
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = LockerRate
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    @swagger_auto_schema(
        request_body=ArchiveLockerRateSerializer,
        responses={200: "success", 500: "server error"},
    )
    @action(
        methods=["PUT"],
        detail=False,
        url_path="archive",
        url_name="archive-locker-rate",
        permission_classes=[],
    )
    def archive_lockerRate(self, request):
        serializer = ArchiveLockerRateSerializer(data=request.data)
        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data
            locker_rate = LockerRate.objects.get(pk=validated_data["locker_rate_id"])

            if not validated_data["is_latest"]:
                locker_rate.is_latest = validated_data["is_latest"]
                locker_rate.save()
                return Response(
                    {"status": "success", "message": "locker rate archive success"},
                    status=status.HTTP_200_OK,
                )

            try:
                existing_hours = LockerRate.objects.get(
                    hours_of_usage=locker_rate.hours_of_usage, is_latest=True
                )
                # Your existing code if the object is found
                existing_hours.is_latest = False
                existing_hours.save()
                locker_rate.is_latest = validated_data["is_latest"]
            except LockerRate.DoesNotExist:
                locker_rate.is_latest = validated_data["is_latest"]

            locker_rate.save()

            return Response(
                {"status": "success", "message": "locker rate archive success"},
                status=status.HTTP_200_OK,
            )

        except LockerRate.DoesNotExist:
            return Response(
                {"status": "failed", "message": "locker rate not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except LockerRate.DoesNotExist:
            return Response(
                {"status": "failed", "message": "existing hours of usage not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(responses={"200": LockerRateSerializer, "500": "server error"})
    def list(self, request):
        qs = self.queryset.all()
        serializer = LockerRateSerializer(qs, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        responses={
            "200": LockerRateSerializer,
            "404": "not found",
            "500": "server error",
        }
    )
    def retrieve(self, request, pk=None):
        queryset = LockerRate.objects.all()
        locker = get_object_or_404(queryset, pk=pk)
        serializer = LockerRateSerializer(locker)
        return Response(serializer.data)

    @swagger_auto_schema(
        request_body=LockerRateSerializer,
        responses={200: "success", 400: "bad request", 500: "internal server error"},
    )
    def create(self, request):
        serializer = LockerRateSerializer(data=request.data)
        if serializer.is_valid():
            with transaction.atomic():

                validated_data = serializer.validated_data

                locker_rates = LockerRate.objects.all()
                for rate in locker_rates:
                    if (
                        validated_data["hours_of_usage"] == rate.hours_of_usage
                        and rate.is_latest
                    ):
                        return Response(
                            {"status": "failed", "message": "Duration is duplicated"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                user_lot_id = request.user.lot_id

                validated_data["lot_id"] = user_lot_id

                model = serializer.save()
                return Response(
                    {
                        "status": "success",
                        "message": "new locker rate registered",
                        "id": model.locker_rate_id,
                    },
                    status=status.HTTP_201_CREATED,
                )
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        request_body=LockerRateSerializer,
        responses={200: "success", 400: "bad request", 500: "internal server error"},
    )
    def update(self, request, pk=None):
        queryset = LockerRate.objects.all()
        locker_rate = get_object_or_404(queryset, pk=pk)
        serializer = LockerRateSerializer(locker_rate, data=request.data)
        if serializer.is_valid():
            with transaction.atomic():

                validated_data = serializer.validated_data

                locker_rates = LockerRate.objects.all()
                for rate in locker_rates:
                    if (
                        validated_data["hours_of_usage"] == rate.hours_of_usage
                        and locker_rate.pk != rate.pk
                        and rate.is_latest
                    ):
                        return Response(
                            {"status": "failed", "message": "Duration is duplicated"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                new_locker_rate = None
                if locker_rate.locker_rate != validated_data["locker_rate"]:
                    new_locker_rate = LockerRate.objects.create(
                        tiers=0,
                        hours_of_usage=validated_data["hours_of_usage"],
                        locker_rate=validated_data["locker_rate"],
                        lot=locker_rate.lot,
                        is_latest=True,
                    )
                    locker_rate.is_latest = False
                    locker_rate.save()
                    serializer = LockerRateSerializer(new_locker_rate)
                else:
                    serializer.save()

            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        responses={"200": "success", "404": "not found", "500": "server error"}
    )
    def destroy(self, request, pk=None):
        queryset = LockerRate.objects.all()
        locker = get_object_or_404(queryset, pk=pk)
        locker.delete()
        return Response(
            {"status": "success", "message": "locker rate deleted"},
            status=status.HTTP_200_OK,
        )
