# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("lot", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="LockerZone",
            fields=[
                (
                    "zone_id",
                    models.AutoField(
                        editable=False, primary_key=True, serialize=False, unique=True
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=30)),
                ("details", models.CharField(blank=True, max_length=50, null=True)),
                ("is_archive", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="LockerRate",
            fields=[
                (
                    "locker_rate_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("tiers", models.IntegerField(default=0)),
                ("hours_of_usage", models.IntegerField()),
                ("locker_rate", models.DecimalField(decimal_places=2, max_digits=100)),
                ("is_latest", models.BooleanField(default=True)),
                (
                    "lot",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="lot.lot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Locker",
            fields=[
                (
                    "locker_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Locker ID",
                    ),
                ),
                ("code", models.CharField(max_length=20)),
                ("details", models.CharField(max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Available", "Available"),
                            ("Occupied", "Occupied"),
                            ("Rental", "Rental"),
                            ("Maintenance", "Maintenance"),
                        ],
                        default="Available",
                        max_length=15,
                    ),
                ),
                ("is_archive", models.BooleanField(default=False)),
                (
                    "up_down",
                    models.BooleanField(
                        default=False, help_text="True = UP, Flase = DOWN"
                    ),
                ),
                (
                    "zone",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lockers.lockerzone",
                        verbose_name="Locker Zone",
                    ),
                ),
            ],
        ),
    ]
