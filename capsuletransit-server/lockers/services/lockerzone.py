from lockers.models import LockerZone
from django.db.models import Count, Q


def get_specific_locker_zone(request):
    queryset = LockerZone.objects.get(pk=request)
    return queryset


def get_locker_stats():
    queryset = LockerZone.objects.annotate(
        available_lockers=Count("locker", filter=Q(locker__status="Available")),
        occupied_lockers=Count("locker", filter=Q(locker__status="Occupied")),
        rental_lockers=Count("locker", filter=Q(locker__status="Rental")),
        maintenance_lockers=Count("locker", filter=Q(locker__status="Maintenance")),
    ).values(
        "zone_id",
        "name",
        "available_lockers",
        "occupied_lockers",
        "rental_lockers",
        "maintenance_lockers",
    )
    return queryset
