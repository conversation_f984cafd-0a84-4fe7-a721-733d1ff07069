
from guests.models import Customer, Country, IDType, Gender, Member
from django.db.models import Sum
from bookings.models import BookingNumberCounter
from guests.utils import generate_membership_no

def create_new_customer(
    first_name : str,
    last_name : str,
    id_no : str,
    country_code : str,
    gender : str,
    id_type : str,
    phone_no : str = "",
    email : str = "",
    is_member : bool = False
):
    
    country = Country.objects.filter(country_code = country_code).first()
    id_type_obj = IDType.objects.filter(id_type__iexact = id_type.lower()).first()
    gender_obj = Gender.objects.filter(gender__iexact=gender.lower()).first()

    if not country:
        return None
    
    exist_guest = Customer.objects.filter(
        id_no__iexact = id_no.lower(),
        country_id = country.pk
    ).first()

    if exist_guest:
        return exist_guest
    
    membership_no = None
    if is_member:
        with BookingNumberCounter.Context("GUEST") as counter:
            membership_no = generate_membership_no(counter=counter.counter)

        member = Member.objects.filter(member_condition="Member", is_archive=False).first()
    else:
        member = Member.objects.filter(member_tier = "None", is_archive=False).first()

    new_customer = Customer.objects.create(
        member_id = member.pk,
        id_type_id = id_type_obj.pk,
        gender_id = gender_obj.pk,
        country_id = country.pk,
        firstname = first_name,
        lastname = last_name,
        id_no = id_no,
        phone_number = f"+{country.prefix} {phone_no}" if phone_no != "" else None,
        email = email,
        membership_no = membership_no,
    )

    return new_customer


def customer_milestone_progress(customer : Customer, milestone_hours_to_hit : int):
    
    customer_milestone = customer.memberpromolist_set.filter(
        individual_promo__is_milestone = True
    )
    milestone_total_point = customer_milestone.aggregate(total_point = Sum("individual_promo__hours_to_hit"))
    customer_used_point = (
        milestone_total_point['total_point']
        if milestone_total_point['total_point']
        else 0
    )

    customer_available_point = customer.point - customer_used_point
            
    milestone_progress = (
        customer_available_point / milestone_hours_to_hit
    ) * 100
    if milestone_progress > 100:
        milestone_progress = 100

    cycle = customer_milestone.count()

    return {
        "customer_available_point" : customer_available_point,
        "customer_cycle" : cycle,
        "customer_milestone_progress" : milestone_progress
    }
    