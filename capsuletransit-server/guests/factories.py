from factory.django import DjangoModelFactory
from factory import Faker, Iterator, fuzzy, LazyAttribute, lazy_attribute

# * to use the faker library directly, use this import that comes with the factory_boy library install
from factory.faker import faker as ori_faker

from .models import Customer, IDType, Gender, Country, ID_TYPE, GENDER_TYPE, Member


ID_TYPE_ARR = [x[0] for x in ID_TYPE]
class IDTypeFactory(DjangoModelFactory):
    class Meta:
        model = IDType

    id_type_id = Faker("uuid4")
    id_type = Iterator(ID_TYPE_ARR)


GENDER_TYPE_ARR = [x[0] for x in GENDER_TYPE]
class GenderFactory(DjangoModelFactory):
    class Meta:
        model = Gender

    gender_id = Faker("uuid4")
    gender = Iterator(GENDER_TYPE_ARR)


class CountryFactory(DjangoModelFactory):
    class Meta:
        model = Country

    country_id = Faker("uuid4")
    country_name = Iterator(["Malaysia", "Singapore", "Japan"])
    country_code = Iterator(["MY", "SG", "JP"])

class MemberFactory(DjangoModelFactory):
    class Meta:
        model = Member

    member_id = Faker("uuid4")
    member_tier = "None"
    member_condition = "Default"
    is_archive = False


class CustomerFactory(DjangoModelFactory):

    class Meta:
        model = Customer

    member = Member.objects.get(pk="e365f156-ef76-45fc-97f7-ba9533244679")
    id_type = fuzzy.FuzzyChoice(IDType.objects.all())
    gender = fuzzy.FuzzyChoice(Gender.objects.all())
    country = fuzzy.FuzzyChoice(Country.objects.all())
    firstname = Faker("first_name")
    lastname = Faker("last_name")

    @lazy_attribute
    def id_no(self):
        faker = ori_faker.Faker()
        return faker.numerify("######-##-####")

    @lazy_attribute
    def phone_number(self):
        faker = ori_faker.Faker()
        return "01" + str(faker.numerify("%")) + "-" + str(faker.numerify("#######!"))
    
    email = LazyAttribute(lambda a: "{}{}@email.com".format(a.firstname, a.lastname).lower())
