from .models import Customer, IDType, Gender, Country
from django.utils import timezone


def create_non_guest_customer():

    id_type = IDType.objects.filter(id_type = "MyKad").first()
    gender = Gender.objects.filter(gender = "OTHERS").first()
    country = Country.objects.filter(country_name = "Malaysia").first()

    new_customer = Customer.objects.create(
        firstname = "Non Guest",
        lastname = "",
        id_type_id = id_type.pk,
        gender_id = gender.pk,
        country_id = country.pk,
        id_no = ""
    )
    
    return new_customer


def generate_membership_no(counter):
    current_date = timezone.now()

    day = current_date.day
    month = current_date.month
    year = current_date.year % 100

    day_str = f"{day:02d}"
    month_str = f"{month:02d}"
    year_str = f"{year:02d}"

    membership_no = f"{day_str}{month_str}{year_str}{counter}"
    return membership_no.zfill(10)


