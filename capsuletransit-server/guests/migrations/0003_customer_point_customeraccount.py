# Generated by Django 4.2.3 on 2024-06-26 04:23

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('guests', '0002_alter_customer_phone_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='point',
            field=models.IntegerField(default=0, verbose_name='Member Point'),
        ),
        migrations.CreateModel(
            name='CustomerAccount',
            fields=[
                ('account_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='Account Id')),
                ('username', models.Char<PERSON>ield(max_length=20, unique=True)),
                ('password', models.CharField(max_length=200)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='guests.customer', verbose_name='Customer')),
            ],
        ),
    ]
