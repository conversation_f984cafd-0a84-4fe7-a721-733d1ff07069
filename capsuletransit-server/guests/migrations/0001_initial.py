# Generated by Django 4.2.3 on 2024-03-07 17:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "country_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Country ID",
                    ),
                ),
                (
                    "country_name",
                    models.CharField(
                        help_text="choices will be grabbed from standardised source",
                        max_length=100,
                        verbose_name="Country Name",
                    ),
                ),
                (
                    "country_code",
                    models.CharField(max_length=10, verbose_name="Country Code"),
                ),
                ("prefix", models.CharField(blank=True, max_length=10, null=True)),
                ("favorite", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Gender",
            fields=[
                (
                    "gender_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Gender ID",
                    ),
                ),
                (
                    "gender",
                    models.Char<PERSON>ield(
                        choices=[
                            ("MALE", "MALE"),
                            ("FEMALE", "FEMALE"),
                            ("OTHERS", "OTHERS"),
                        ],
                        max_length=20,
                        verbose_name="Gender",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="IDType",
            fields=[
                (
                    "id_type_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Gender ID",
                    ),
                ),
                (
                    "id_type",
                    models.CharField(
                        choices=[
                            ("Identification Number", "Identification Number"),
                            ("Passport", "Passport"),
                        ],
                        max_length=50,
                        verbose_name="ID Type",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Member",
            fields=[
                (
                    "member_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Member ID",
                    ),
                ),
                (
                    "member_tier",
                    models.CharField(
                        default=None,
                        help_text="no preset list of value yet, this table's data will be dynamic for admin to register new items in",
                        max_length=50,
                        verbose_name="Member Tier",
                    ),
                ),
                (
                    "member_condition",
                    models.CharField(
                        help_text="no preset list of value yet, this table’s data will be dynamic for admin to register new items in",
                        max_length=50,
                        verbose_name="Memeber Condition",
                    ),
                ),
                (
                    "is_archive",
                    models.BooleanField(verbose_name="Is Member Type Archived?"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "customer_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Cusotmer ID",
                    ),
                ),
                (
                    "firstname",
                    models.CharField(max_length=100, verbose_name="Firstname"),
                ),
                ("lastname", models.CharField(max_length=100, verbose_name="Lastname")),
                (
                    "id_no",
                    models.CharField(
                        max_length=15, verbose_name="Identification Card Number"
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=15,
                        null=True,
                        verbose_name="Phone Number",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=60, null=True, verbose_name="Email"
                    ),
                ),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.country",
                        verbose_name="Country",
                    ),
                ),
                (
                    "gender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.gender",
                        verbose_name="Gender",
                    ),
                ),
                (
                    "id_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.idtype",
                        verbose_name="ID Type",
                    ),
                ),
                (
                    "member",
                    models.ForeignKey(
                        default=None,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="guests.member",
                        verbose_name="Member ID",
                    ),
                ),
            ],
        ),
    ]
