# Generated by Django 4.2.3 on 2024-07-23 12:00

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('guests', '0003_customer_point_customeraccount'),
    ]

    operations = [
        migrations.AddField(
            model_name='customeraccount',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
