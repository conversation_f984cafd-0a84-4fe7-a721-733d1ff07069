from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db.models import Q, Prefetch, Count, Max
from rest_framework.response import Response
from rest_framework import viewsets, status, generics
from djangorestframework_camel_case.parser import Camel<PERSON><PERSON><PERSON><PERSON>NParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer

from django.db import connection, transaction

from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from guests.services.customer import customer_milestone_progress

from .models import *
from .serializer import (
    CountrySerializer,
    CustomerSerializer,
    CustomerWriteSerializer,
    EditFavoriteCountrySerializer,
    GenderSerializer,
    IDTypeSerializer,
    MemberSerializer,
    MembershipProgressSerializer,
)

from guests import exceptions
from promotions.models import IndividualPromo, MemberPromolist


class GuestViewSet(viewsets.ViewSet):
    queryset = Customer.objects.all()
    permission_classes = []
    authentication_classes = []
    serializer_class = CustomerSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)

    def get_queryset(self):
        qs = self.queryset.all()
        qs = qs.select_related("member", "id_type", "gender", "country")

        return qs

    def list(self, request):
        guest_name = request.query_params.get("name", None)
        qs = self.get_queryset()

        if guest_name:
            qs = qs.filter(
                Q(firstname__icontains=guest_name) | Q(lastname__icontains=guest_name)
            )

        serializer = CustomerSerializer(qs, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        queryset = self.get_queryset()
        room = get_object_or_404(queryset, pk=pk)
        serializer = CustomerSerializer(room)
        return Response(serializer.data)

    def create(self, request):
        serializer = CustomerWriteSerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return JsonResponse(
                    serializer.errors, status=status.HTTP_400_BAD_REQUEST
                )

            validated_data = serializer.validated_data

            with transaction.atomic():

                id_no = validated_data["id_no"]
                country = validated_data["country"]
                email = (validated_data["email"],)
                id_type = validated_data["id_type"]

                if id_no != "-":
                    exist_guest = Customer.objects.filter(
                        id_no__iexact=id_no.lower(), country_id=country.pk
                    ).first()

                    if exist_guest:
                        return Response(
                            {
                                "status": "success",
                                "message": "guest exist",
                                "guestid": exist_guest.pk,
                                "guest_name": f"{exist_guest.firstname} {exist_guest.lastname}",
                                "guest_country": exist_guest.country.country_name,
                            },
                            status=status.HTTP_208_ALREADY_REPORTED,
                        )

                if len(id_no) > 12:
                    raise exceptions.UnableCreateGuest("IC number maximum 12 number")

                if (
                    id_type.id_type == "MyKad"
                    and country.country_name != "Malaysia"
                    or id_type.id_type == "MyKad"
                    and country.country_code != "MYS"
                ):
                    raise exceptions.UnableCreateGuest(
                        "MyKad only valid for MY Citizen"
                    )

                if country.country_name == "Malaysia" or country.country_code == "MY":
                    if not id_no.isdigit() and id_type.id_type == "MyKad":
                        raise exceptions.UnableCreateGuest(
                            "Input should be a valid IC Number"
                        )

                if email and email[0] != "" and not email[0].find("@") != -1:
                    raise exceptions.UnableCreateGuest("Input should be a valid email")

                member = Member.objects.filter(
                    member_tier="None", is_archive=False
                ).first()

                try:
                    phone_no = validated_data["phone_number"]
                except:
                    phone_no = ""

                new_customer = Customer.objects.create(
                    member_id=member.pk,
                    id_type_id=id_type.pk,
                    gender_id=validated_data["gender"].pk,
                    country_id=country.pk,
                    firstname=validated_data["firstname"],
                    lastname=validated_data["lastname"],
                    id_no=id_no,
                    phone_number=f"{phone_no}" if phone_no != "" else None,
                    email=email[0],
                )

            return Response(
                {
                    "status": "success",
                    "message": "new guest registered",
                    "guestid": new_customer.pk,
                    "guest_name": f"{new_customer.firstname} {new_customer.lastname}",
                    "guest_country": new_customer.country.country_name,
                },
                status=status.HTTP_201_CREATED,
            )

        except exceptions.UnableCreateGuest as e:
            return Response(
                {"non_field_errors": [str(e)]}, status=status.HTTP_403_FORBIDDEN
            )

        except Exception as e:
            print(e)
            return Response(
                {"status": "failed", "message": "Sever Error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=CustomerSerializer,
        responses={
            200: "success",
            400: "bad request",
            500: "internal server error",
        },
    )
    def update(self, request, pk=None):
        queryset = self.get_queryset()
        customer = get_object_or_404(queryset, pk=pk)
        serializer = CustomerWriteSerializer(customer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        queryset = self.get_queryset()
        room = get_object_or_404(queryset, pk=pk)
        room.delete()
        return Response(
            {"status": "success", "message": "guest deleted"}, status=status.HTTP_200_OK
        )

    @action(detail=False, methods=["GET"])
    def get_customer_by_id_no(self, request):
        id_no = request.query_params.get("id_no", None)
        if not id_no:
            return Response(
                {"error": "id_no parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            customer = Customer.objects.get(id_no=id_no)
            serializer = CustomerSerializer(customer)
            return Response(serializer.data)
        except Customer.DoesNotExist:
            return Response(
                {"error": "Customer not found"}, status=status.HTTP_404_NOT_FOUND
            )

    @swagger_auto_schema(
        responses={
            200: "success",
            400: "bad request",
            404: "not found",
            500: "internal server error",
        }
    )
    @action(detail=False, methods=["GET"], url_path="id-type")
    def get_id_type(self, request):
        data = IDType.objects.all()
        serializer = IDTypeSerializer(data, many=True)
        try:
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    @swagger_auto_schema(
        responses={
            200: "success",
            400: "bad request",
            404: "not found",
            500: "internal server error",
        }
    )
    @action(detail=False, methods=["GET"], url_path="gender")
    def get_gender(self, request):
        data = Gender.objects.all()
        serializer = GenderSerializer(data, many=True)
        try:
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    @swagger_auto_schema(
        responses={
            200: "success",
            400: "bad request",
            404: "not found",
            500: "internal server error",
        }
    )
    @action(detail=False, methods=["GET"], url_path="country")
    def get_country(self, request):

        favorite = request.GET.get("favorite", None)

        data = Country.objects.all()

        if favorite != None:
            favorite = True if favorite == "true" else False
            print(favorite)
            data = data.filter(favorite=favorite)

        serializer = CountrySerializer(data, many=True)
        try:
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    @swagger_auto_schema(
        request_body=EditFavoriteCountrySerializer,
        responses={
            "200": "success",
            "400": "bad request",
            "404": "not found",
            "500": "server error",
        },
    )
    @action(detail=False, methods=["PUT"], url_path="country/edit-favorite")
    def edit_country_favorite(self, request):
        serializer = EditFavoriteCountrySerializer(data=request.data)

        try:
            if not serializer.is_valid():
                return Response(
                    {"status": "failed", "message": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            validated_data = serializer.validated_data

            try:
                country = Country.objects.get(pk=validated_data["country_id"])
            except:
                return Response(
                    {"status": "failed", "message": "country not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            country.favorite = validated_data["favorite"]
            country.save()

            return Response(
                {"status": "success", "message": "favorite status updated"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            200: MemberSerializer,
            400: "bad request",
            404: "not found",
            500: "internal server error",
        }
    )
    @action(detail=False, methods=["GET"], url_path="member")
    def get_member(self, request):
        data = Member.objects.all()
        serializer = MemberSerializer(data, many=True)
        try:
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    @action(detail=False, methods=["GET"], url_path="search")
    def search_guest(self, request):
        search_param = request.GET.get("value", None)

        try:
            if not search_param:
                return Response(
                    {"status": "failed", "message": "please provide search value"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            search_param = search_param.strip()

            qs = self.get_queryset()
            qs = qs.filter(
                Q(id_no__icontains=search_param)
                | Q(firstname__icontains=search_param)
                | Q(lastname__icontains=search_param)
                | Q(firstname__icontains=search_param.split()[0])
                & Q(lastname__icontains=search_param.split()[-1])
            )

            if not qs.exists():
                return Response(
                    {"status": "failed", "message": "guest not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return_serializer = CustomerSerializer(qs, many=True)
            return Response(return_serializer.data)
        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)}
            )

    # @action(detail=False, methods=["GET"], url_path="membership-progress")
    # def guest_membership_progress(self, request):
    #     start_date_time = request.GET.get("startDateTime", None)
    #     end_date_time = request.GET.get("endDateTime", None)

    #     try:
    #         qs = self.get_queryset().annotate(
    #             booking_count=Count(
    #                 'booking',
    #                 filter=Q(booking__is_cancelled=False)
    #             )
    #         )


    #         # filter out non member customer
    #         qs = qs.filter(membership_no__isnull=False)

    #         qs = qs.prefetch_related(
    #             Prefetch(
    #                 "memberpromolist_set",
    #                 queryset=MemberPromolist.objects.select_related(
    #                     "individual_promo", "transaction"
    #                 ),
    #             )
    #         )

    #         if start_date_time and end_date_time:
    #             qs = qs.filter(
    #                 memberpromolist__transaction__transaction_datetime__range=(
    #                     start_date_time,
    #                     end_date_time,
    #                 )
    #             )

    #         is_milestone_promo = IndividualPromo.objects.filter(
    #             is_milestone=True, archived=False
    #         ).first()

    #         return_data = []
    #         for customer in qs:

    #             milestone_progress = (
    #                 customer.point / is_milestone_promo.hours_to_hit
    #             ) * 100
    #             if milestone_progress > 100:
    #                 milestone_progress = 100

    #             claimed_promo_count = customer.memberpromolist_set.count()
    #             retention_count = customer.booking_count

    #             latest_transaction_date = None
    #             for promo in customer.memberpromolist_set.all():

    #                 if (
    #                     not promo.transaction
    #                     or not promo.transaction.transaction_datetime
    #                 ):
    #                     continue

    #                 if (
    #                     not latest_transaction_date
    #                     or promo.transaction.transaction_datetime
    #                     > latest_transaction_date
    #                 ):
    #                     latest_transaction_date = promo.transaction.transaction_datetime

    #             if (start_date_time and end_date_time) and not latest_transaction_date:
    #                 continue

    #             return_data.append(
    #                 {
    #                     "customer_id": customer.pk,
    #                     "customer_name": f"{customer.firstname} {customer.lastname}",
    #                     "retention_count": retention_count,
    #                     "milestone": f"{customer.point}/{is_milestone_promo.hours_to_hit}",
    #                     "milestone_progress": milestone_progress,
    #                     "claimed_promo_count": claimed_promo_count,
    #                     "latest_applied_date": (
    #                         latest_transaction_date if latest_transaction_date else "-"
    #                     ),
    #                 }
    #             )

    #         return_serializer = MembershipProgressSerializer(return_data, many=True)
    #         return Response(
    #             {"status": "success", "data": return_serializer.data},
    #             status=status.HTTP_200_OK,
    #         )

    #     except Exception as e:
    #         return Response(
    #             {"status": "failed", "message": "server error", "error": str(e)}
    #         )


    @action(detail=False, methods=["GET"], url_path="membership-progress")
    def guest_membership_progress(self, request):
        start_date_time = request.GET.get("startDateTime", None)
        end_date_time = request.GET.get("endDateTime", None)
        no_progress_member = request.GET.get("noProgressMember", "")

        try:
            qs = self.get_queryset().annotate(
                booking_count=Count(
                    'booking',
                    filter=Q(booking__is_cancelled=False)
                )
            )


            # filter out non member customer
            qs = qs.filter(membership_no__isnull=False)

            qs = qs.prefetch_related(
                Prefetch(
                    "memberpromolist_set",
                    queryset=MemberPromolist.objects.select_related(
                        "individual_promo", "transaction"
                    ),
                )
            )

            if start_date_time and end_date_time:
                qs = qs.annotate(
                    latest_transaction_date=Max(
                        'memberpromolist__transaction__transaction_datetime',
                        filter=Q(memberpromolist__transaction__transaction_datetime__range=(start_date_time, end_date_time))
                    )
                )
            else:
                qs = qs.annotate(
                    latest_transaction_date=Max('memberpromolist__transaction__transaction_datetime')
                )

            is_milestone_promo = IndividualPromo.objects.filter(
                is_milestone=True, archived=False
            ).first()

            return_data = []
            for customer in qs:
                
                member_start_date = "-"
                if customer.customeraccount.first():
                    member_start_date = customer.customeraccount.first().created_at

                # calculate milestone progress
                milestone_progress_data = customer_milestone_progress(
                    customer=customer,
                    milestone_hours_to_hit=is_milestone_promo.hours_to_hit
                )
                milestone_progress = milestone_progress_data['customer_milestone_progress']
                customer_milestone_cycle = milestone_progress_data['customer_cycle']

                claimed_promo_count = customer.memberpromolist_set.count()
                retention_count = customer.booking_count

                # latest_transaction_date = None
                # for promo in customer.memberpromolist_set.all():

                #     if not promo.transaction or not promo.transaction.transaction_datetime:
                #         continue
                    
                #     if not latest_transaction_date or promo.transaction.transaction_datetime > latest_transaction_date:
                #         latest_transaction_date = promo.transaction.transaction_datetime
                    
                # if (start_date_time and end_date_time) and not latest_transaction_date: continue

                latest_transaction_date = customer.latest_transaction_date

                if (start_date_time and end_date_time) and not latest_transaction_date:
                    latest_transaction_date = None

                if not latest_transaction_date and no_progress_member != "true":
                    continue

                return_data.append(
                    {
                        "customer_id": customer.pk,
                        "customer_name": f"{customer.firstname} {customer.lastname}",
                        "retention_count": retention_count,
                        "milestone": f"{customer.point}/{is_milestone_promo.hours_to_hit}",
                        "milestone_progress": milestone_progress,
                        "claimed_promo_count": claimed_promo_count,
                        "latest_applied_date": latest_transaction_date,
                        "membership_start_date" : member_start_date,
                        "cycle" : customer_milestone_cycle
                    }
                )

            return_data = sorted(
                return_data,
                key=lambda x: (
                    x["latest_applied_date"] is not None,
                    -x["retention_count"],
                    x["customer_name"].lower()
                )
            )

            return_serializer = MembershipProgressSerializer(return_data, many=True)
            return Response(
                {"status": "success", "data": return_serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "failed", "message": "server error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
