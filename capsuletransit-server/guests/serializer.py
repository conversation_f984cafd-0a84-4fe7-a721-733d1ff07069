from rest_framework import serializers
from .models import *


class MemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = "__all__"


class IDTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = IDType
        fields = "__all__"


class GenderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Gender
        fields = "__all__"


class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = "__all__"


class EditFavoriteCountrySerializer(serializers.Serializer):
    country_id = serializers.UUIDField()
    favorite = serializers.BooleanField()


class CustomerSerializer(serializers.ModelSerializer):
    member = MemberSerializer()
    id_type = IDTypeSerializer()
    gender = GenderSerializer()
    country = CountrySerializer()
    rentention_count = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = "__all__"

    def get_rentention_count(self, obj):
        return obj.booking_set.count()


class CustomerWriteSerializer(serializers.ModelSerializer):
    email = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        model = Customer
        fields = "__all__"

    # def validate(self, data):
    #     id_no = data.get("id_no")
    #     country = data.get("country")
    #     email = data.get("email")
    #     id_type = data.get("id_type")
    #     phone_number = data.get("phone_number")

    #     if len(id_no) > 12:
    #         raise serializers.ValidationError("IC number maximum 12 number")

    #     if (
    #         id_type.id_type == "MyKad"
    #         and country.country_name != "Malaysia"
    #         or id_type.id_type == "MyKad"
    #         and country.country_code != "MYS"
    #     ):
    #         raise serializers.ValidationError("MyKad only valid for MY Citizen")

    #     if country.country_name == "Malaysia" or country.country_code == "MY":
    #         if not id_no.isdigit() and id_type.id_type == "MyKad":
    #             raise serializers.ValidationError("Input should be a valid IC Number")

    #     if email and not email.find("@") != -1:
    #         raise serializers.ValidationError("Input should be a valid email")

    #     if email and phone_number:
    #         members=Member.objects.filter(member_tier = "Normal Member",is_archive=False)

    #         if members.count()>0:
    #             member=members.first()
    #             data["member_id"]= member.pk

    #         else:
    #             member= Member.objects.create(
    #                 member_tier="Normal Member",
    #                 member_condition="Member"
    #             )
    #             data["member_id"]= member.pk

    #     return data


class CustomerOnlySerializer(serializers.ModelSerializer):

    class Meta:
        model = Customer
        fields = "__all__"


class CustomerAccountSerializer(serializers.ModelSerializer):
    customer = CustomerOnlySerializer()

    class Meta:
        model = CustomerAccount
        exclude = ["password"]


class MembershipProgressSerializer(serializers.Serializer):
    customer_id = serializers.UUIDField()
    customer_name = serializers.CharField()
    retention_count = serializers.IntegerField()
    milestone = serializers.CharField()
    milestone_progress = serializers.DecimalField(decimal_places=2, max_digits=10)
    claimed_promo_count = serializers.IntegerField()
    latest_applied_date = serializers.CharField()
    cycle = serializers.IntegerField()
    membership_start_date = serializers.CharField()

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if representation["latest_applied_date"] is None:
            representation["latest_applied_date"] = "-"

        return representation
