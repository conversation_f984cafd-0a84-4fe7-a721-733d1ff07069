from uuid import uuid4
from django.db import models

ID_TYPE = (
    ("Identification Number", "Identification Number"),
    ("Passport", "Passport"),
)

class IDType(models.Model):
    id_type_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Gender ID")
    id_type = models.CharField(max_length=50, choices=ID_TYPE, verbose_name="ID Type")

    def __str__(self) -> str:
        return self.id_type


GENDER_TYPE = (
    ("MALE", "MALE"),
    ("FEMALE", "FEMALE"),
    ("OTHERS", "OTHERS"),
)

class Gender(models.Model):
    gender_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Gender ID")
    gender = models.Char<PERSON>ield(max_length=20, choices=GENDER_TYPE, verbose_name="Gender")

    def __str__(self) -> str:
        return self.gender


class Country(models.Model):
    country_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Country ID")
    country_name = models.CharField(max_length=100, verbose_name="Country Name", help_text="choices will be grabbed from standardised source")
    country_code = models.CharField(max_length=10, verbose_name="Country Code")
    prefix = models.CharField(max_length=10, null=True, blank=True)
    favorite = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.country_name


class Member(models.Model):
    member_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Member ID")
    member_tier = models.CharField(max_length=50, default=None, verbose_name="Member Tier", help_text="no preset list of value yet, this table's data will be dynamic for admin to register new items in")
    member_condition = models.CharField(max_length=50, verbose_name="Memeber Condition", help_text="no preset list of value yet, this table’s data will be dynamic for admin to register new items in")
    is_archive = models.BooleanField(verbose_name="Is Member Type Archived?")

    def __str__(self) -> str:
        return self.member_tier


class Customer(models.Model):
    customer_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Cusotmer ID")

    #TODO: CLARIFY WITH CHLOE IF THIS SHOULD BE NULL OR MAKE A MEMBER MODEL WITH member_tier as None
    member = models.ForeignKey(Member, default=None, on_delete=models.RESTRICT, verbose_name="Member ID")
    id_type = models.ForeignKey(IDType, on_delete=models.RESTRICT, verbose_name="ID Type")
    gender = models.ForeignKey(Gender, on_delete=models.RESTRICT, verbose_name="Gender")
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, verbose_name="Country")
    firstname = models.CharField(max_length=100, verbose_name="Firstname")
    lastname = models.CharField(max_length=100, verbose_name="Lastname")
    id_no = models.CharField(max_length=15, verbose_name="Identification Card Number")
    phone_number = models.CharField(max_length=20, verbose_name="Phone Number", null=True, blank=True)
    email = models.EmailField(max_length=60, verbose_name="Email", null=True, blank=True)
    point = models.IntegerField(default=0, verbose_name="Member Point")
    membership_no = models.CharField(max_length=20, null=True)

    def __str__(self) -> str:
        return f"{self.firstname} {self.lastname}"


class CustomerAccount(models.Model):
    account_id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True, verbose_name="Account Id")
    username = models.CharField(max_length=20, unique=True)
    password = models.CharField(max_length=200)
    customer = models.ForeignKey(Customer, on_delete=models.RESTRICT, verbose_name="Customer", related_name="customeraccount")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)